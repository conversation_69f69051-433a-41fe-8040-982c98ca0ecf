{"version": 3, "sources": ["../../refractor/lang/flow.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = flow\nflow.displayName = 'flow'\nflow.aliases = []\nfunction flow(Prism) {\n  ;(function (Prism) {\n    Prism.languages.flow = Prism.languages.extend('javascript', {})\n    Prism.languages.insertBefore('flow', 'keyword', {\n      type: [\n        {\n          pattern:\n            /\\b(?:[Bb]oolean|Function|[Nn]umber|[Ss]tring|any|mixed|null|void)\\b/,\n          alias: 'tag'\n        }\n      ]\n    })\n    Prism.languages.flow['function-variable'].pattern =\n      /(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?=\\s*=\\s*(?:function\\b|(?:\\([^()]*\\)(?:\\s*:\\s*\\w+)?|(?!\\s)[_$a-z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*)\\s*=>))/i\n    delete Prism.languages.flow['parameter']\n    Prism.languages.insertBefore('flow', 'operator', {\n      'flow-punctuation': {\n        pattern: /\\{\\||\\|\\}/,\n        alias: 'punctuation'\n      }\n    })\n    if (!Array.isArray(Prism.languages.flow.keyword)) {\n      Prism.languages.flow.keyword = [Prism.languages.flow.keyword]\n    }\n    Prism.languages.flow.keyword.unshift(\n      {\n        pattern: /(^|[^$]\\b)(?:Class|declare|opaque|type)\\b(?!\\$)/,\n        lookbehind: true\n      },\n      {\n        pattern:\n          /(^|[^$]\\B)\\$(?:Diff|Enum|Exact|Keys|ObjMap|PropertyType|Record|Shape|Subtype|Supertype|await)\\b(?!\\$)/,\n        lookbehind: true\n      }\n    )\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,OAAO,cAAc,CAAC,CAAC;AAC9D,QAAAA,OAAM,UAAU,aAAa,QAAQ,WAAW;AAAA,UAC9C,MAAM;AAAA,YACJ;AAAA,cACE,SACE;AAAA,cACF,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,KAAK,mBAAmB,EAAE,UACxC;AACF,eAAOA,OAAM,UAAU,KAAK,WAAW;AACvC,QAAAA,OAAM,UAAU,aAAa,QAAQ,YAAY;AAAA,UAC/C,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,YAAI,CAAC,MAAM,QAAQA,OAAM,UAAU,KAAK,OAAO,GAAG;AAChD,UAAAA,OAAM,UAAU,KAAK,UAAU,CAACA,OAAM,UAAU,KAAK,OAAO;AAAA,QAC9D;AACA,QAAAA,OAAM,UAAU,KAAK,QAAQ;AAAA,UAC3B;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}