{"version": 3, "sources": ["../../highlight.js/lib/languages/cos.js"], "sourcesContent": ["/*\nLanguage: <PERSON><PERSON><PERSON>bject Script\nAuthor: <PERSON><PERSON> <<EMAIL>>\nCategory: enterprise, scripting\nWebsite: https://cedocs.intersystems.com/latest/csp/docbook/DocBook.UI.Page.cls\n*/\n\n/** @type LanguageFn */\nfunction cos(hljs) {\n  const STRINGS = {\n    className: 'string',\n    variants: [{\n      begin: '\"',\n      end: '\"',\n      contains: [{ // escaped\n        begin: \"\\\"\\\"\",\n        relevance: 0\n      }]\n    }]\n  };\n\n  const NUMBERS = {\n    className: \"number\",\n    begin: \"\\\\b(\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)\",\n    relevance: 0\n  };\n\n  const COS_KEYWORDS =\n    'property parameter class classmethod clientmethod extends as break ' +\n    'catch close continue do d|0 else elseif for goto halt hang h|0 if job ' +\n    'j|0 kill k|0 lock l|0 merge new open quit q|0 read r|0 return set s|0 ' +\n    'tcommit throw trollback try tstart use view while write w|0 xecute x|0 ' +\n    'zkill znspace zn ztrap zwrite zw zzdump zzwrite print zbreak zinsert ' +\n    'zload zprint zremove zsave zzprint mv mvcall mvcrt mvdim mvprint zquit ' +\n    'zsync ascii';\n\n  // registered function - no need in them due to all functions are highlighted,\n  // but I'll just leave this here.\n\n  // \"$bit\", \"$bitcount\",\n  // \"$bitfind\", \"$bitlogic\", \"$case\", \"$char\", \"$classmethod\", \"$classname\",\n  // \"$compile\", \"$data\", \"$decimal\", \"$double\", \"$extract\", \"$factor\",\n  // \"$find\", \"$fnumber\", \"$get\", \"$increment\", \"$inumber\", \"$isobject\",\n  // \"$isvaliddouble\", \"$isvalidnum\", \"$justify\", \"$length\", \"$list\",\n  // \"$listbuild\", \"$listdata\", \"$listfind\", \"$listfromstring\", \"$listget\",\n  // \"$listlength\", \"$listnext\", \"$listsame\", \"$listtostring\", \"$listvalid\",\n  // \"$locate\", \"$match\", \"$method\", \"$name\", \"$nconvert\", \"$next\",\n  // \"$normalize\", \"$now\", \"$number\", \"$order\", \"$parameter\", \"$piece\",\n  // \"$prefetchoff\", \"$prefetchon\", \"$property\", \"$qlength\", \"$qsubscript\",\n  // \"$query\", \"$random\", \"$replace\", \"$reverse\", \"$sconvert\", \"$select\",\n  // \"$sortbegin\", \"$sortend\", \"$stack\", \"$text\", \"$translate\", \"$view\",\n  // \"$wascii\", \"$wchar\", \"$wextract\", \"$wfind\", \"$wiswide\", \"$wlength\",\n  // \"$wreverse\", \"$xecute\", \"$zabs\", \"$zarccos\", \"$zarcsin\", \"$zarctan\",\n  // \"$zcos\", \"$zcot\", \"$zcsc\", \"$zdate\", \"$zdateh\", \"$zdatetime\",\n  // \"$zdatetimeh\", \"$zexp\", \"$zhex\", \"$zln\", \"$zlog\", \"$zpower\", \"$zsec\",\n  // \"$zsin\", \"$zsqr\", \"$ztan\", \"$ztime\", \"$ztimeh\", \"$zboolean\",\n  // \"$zconvert\", \"$zcrc\", \"$zcyc\", \"$zdascii\", \"$zdchar\", \"$zf\",\n  // \"$ziswide\", \"$zlascii\", \"$zlchar\", \"$zname\", \"$zposition\", \"$zqascii\",\n  // \"$zqchar\", \"$zsearch\", \"$zseek\", \"$zstrip\", \"$zwascii\", \"$zwchar\",\n  // \"$zwidth\", \"$zwpack\", \"$zwbpack\", \"$zwunpack\", \"$zwbunpack\", \"$zzenkaku\",\n  // \"$change\", \"$mv\", \"$mvat\", \"$mvfmt\", \"$mvfmts\", \"$mviconv\",\n  // \"$mviconvs\", \"$mvinmat\", \"$mvlover\", \"$mvoconv\", \"$mvoconvs\", \"$mvraise\",\n  // \"$mvtrans\", \"$mvv\", \"$mvname\", \"$zbitand\", \"$zbitcount\", \"$zbitfind\",\n  // \"$zbitget\", \"$zbitlen\", \"$zbitnot\", \"$zbitor\", \"$zbitset\", \"$zbitstr\",\n  // \"$zbitxor\", \"$zincrement\", \"$znext\", \"$zorder\", \"$zprevious\", \"$zsort\",\n  // \"device\", \"$ecode\", \"$estack\", \"$etrap\", \"$halt\", \"$horolog\",\n  // \"$io\", \"$job\", \"$key\", \"$namespace\", \"$principal\", \"$quit\", \"$roles\",\n  // \"$storage\", \"$system\", \"$test\", \"$this\", \"$tlevel\", \"$username\",\n  // \"$x\", \"$y\", \"$za\", \"$zb\", \"$zchild\", \"$zeof\", \"$zeos\", \"$zerror\",\n  // \"$zhorolog\", \"$zio\", \"$zjob\", \"$zmode\", \"$znspace\", \"$zparent\", \"$zpi\",\n  // \"$zpos\", \"$zreference\", \"$zstorage\", \"$ztimestamp\", \"$ztimezone\",\n  // \"$ztrap\", \"$zversion\"\n\n  return {\n    name: 'Caché Object Script',\n    case_insensitive: true,\n    aliases: [\n      \"cls\"\n    ],\n    keywords: COS_KEYWORDS,\n    contains: [\n      NUMBERS,\n      STRINGS,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: \"comment\",\n        begin: /;/,\n        end: \"$\",\n        relevance: 0\n      },\n      { // Functions and user-defined functions: write $ztime(60*60*3), $$myFunc(10), $$^Val(1)\n        className: \"built_in\",\n        begin: /(?:\\$\\$?|\\.\\.)\\^?[a-zA-Z]+/\n      },\n      { // Macro command: quit $$$OK\n        className: \"built_in\",\n        begin: /\\$\\$\\$[a-zA-Z]+/\n      },\n      { // Special (global) variables: write %request.Content; Built-in classes: %Library.Integer\n        className: \"built_in\",\n        begin: /%[a-z]+(?:\\.[a-z]+)*/\n      },\n      { // Global variable: set ^globalName = 12 write ^globalName\n        className: \"symbol\",\n        begin: /\\^%?[a-zA-Z][\\w]*/\n      },\n      { // Some control constructions: do ##class(Package.ClassName).Method(), ##super()\n        className: \"keyword\",\n        begin: /##class|##super|#define|#dim/\n      },\n      // sub-languages: are not fully supported by hljs by 11/15/2015\n      // left for the future implementation.\n      {\n        begin: /&sql\\(/,\n        end: /\\)/,\n        excludeBegin: true,\n        excludeEnd: true,\n        subLanguage: \"sql\"\n      },\n      {\n        begin: /&(js|jscript|javascript)</,\n        end: />/,\n        excludeBegin: true,\n        excludeEnd: true,\n        subLanguage: \"javascript\"\n      },\n      {\n        // this brakes first and last tag, but this is the only way to embed a valid html\n        begin: /&html<\\s*</,\n        end: />\\s*>/,\n        subLanguage: \"xml\"\n      }\n    ]\n  };\n}\n\nmodule.exports = cos;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACT,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU,CAAC;AAAA;AAAA,YACT,OAAO;AAAA,YACP,WAAW;AAAA,UACb,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,eACJ;AA6CF,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS;AAAA,UACP;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA;AAAA;AAAA,UAGA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,aAAa;AAAA,UACf;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,aAAa;AAAA,UACf;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,aAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}