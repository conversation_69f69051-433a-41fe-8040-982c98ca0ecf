{"version": 3, "sources": ["../../refractor/lang/xquery.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = xquery\nxquery.displayName = 'xquery'\nxquery.aliases = []\nfunction xquery(Prism) {\n  ;(function (Prism) {\n    Prism.languages.xquery = Prism.languages.extend('markup', {\n      'xquery-comment': {\n        pattern: /\\(:[\\s\\S]*?:\\)/,\n        greedy: true,\n        alias: 'comment'\n      },\n      string: {\n        pattern: /([\"'])(?:\\1\\1|(?!\\1)[\\s\\S])*\\1/,\n        greedy: true\n      },\n      extension: {\n        pattern: /\\(#.+?#\\)/,\n        alias: 'symbol'\n      },\n      variable: /\\$[-\\w:]+/,\n      axis: {\n        pattern:\n          /(^|[^-])(?:ancestor(?:-or-self)?|attribute|child|descendant(?:-or-self)?|following(?:-sibling)?|parent|preceding(?:-sibling)?|self)(?=::)/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      'keyword-operator': {\n        pattern:\n          /(^|[^:-])\\b(?:and|castable as|div|eq|except|ge|gt|idiv|instance of|intersect|is|le|lt|mod|ne|or|union)\\b(?=$|[^:-])/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      keyword: {\n        pattern:\n          /(^|[^:-])\\b(?:as|ascending|at|base-uri|boundary-space|case|cast as|collation|construction|copy-namespaces|declare|default|descending|else|empty (?:greatest|least)|encoding|every|external|for|function|if|import|in|inherit|lax|let|map|module|namespace|no-inherit|no-preserve|option|order(?: by|ed|ing)?|preserve|return|satisfies|schema|some|stable|strict|strip|then|to|treat as|typeswitch|unordered|validate|variable|version|where|xquery)\\b(?=$|[^:-])/,\n        lookbehind: true\n      },\n      function: /[\\w-]+(?::[\\w-]+)*(?=\\s*\\()/,\n      'xquery-element': {\n        pattern: /(element\\s+)[\\w-]+(?::[\\w-]+)*/,\n        lookbehind: true,\n        alias: 'tag'\n      },\n      'xquery-attribute': {\n        pattern: /(attribute\\s+)[\\w-]+(?::[\\w-]+)*/,\n        lookbehind: true,\n        alias: 'attr-name'\n      },\n      builtin: {\n        pattern:\n          /(^|[^:-])\\b(?:attribute|comment|document|element|processing-instruction|text|xs:(?:ENTITIES|ENTITY|ID|IDREFS?|NCName|NMTOKENS?|NOTATION|Name|QName|anyAtomicType|anyType|anyURI|base64Binary|boolean|byte|date|dateTime|dayTimeDuration|decimal|double|duration|float|gDay|gMonth|gMonthDay|gYear|gYearMonth|hexBinary|int|integer|language|long|negativeInteger|nonNegativeInteger|nonPositiveInteger|normalizedString|positiveInteger|short|string|time|token|unsigned(?:Byte|Int|Long|Short)|untyped(?:Atomic)?|yearMonthDuration))\\b(?=$|[^:-])/,\n        lookbehind: true\n      },\n      number: /\\b\\d+(?:\\.\\d+)?(?:E[+-]?\\d+)?/,\n      operator: [\n        /[+*=?|@]|\\.\\.?|:=|!=|<[=<]?|>[=>]?/,\n        {\n          pattern: /(\\s)-(?=\\s)/,\n          lookbehind: true\n        }\n      ],\n      punctuation: /[[\\](){},;:/]/\n    })\n    Prism.languages.xquery.tag.pattern =\n      /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s+[^\\s>\\/=]+(?:=(?:(\"|')(?:\\\\[\\s\\S]|\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+))?)*\\s*\\/?>/\n    Prism.languages.xquery['tag'].inside['attr-value'].pattern =\n      /=(?:(\"|')(?:\\\\[\\s\\S]|\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+)/\n    Prism.languages.xquery['tag'].inside['attr-value'].inside['punctuation'] =\n      /^=\"|\"$/\n    Prism.languages.xquery['tag'].inside['attr-value'].inside['expression'] = {\n      // Allow for two levels of nesting\n      pattern: /\\{(?!\\{)(?:\\{(?:\\{[^{}]*\\}|[^{}])*\\}|[^{}])+\\}/,\n      inside: Prism.languages.xquery,\n      alias: 'language-xquery'\n    } // The following will handle plain text inside tags\n    var stringifyToken = function (token) {\n      if (typeof token === 'string') {\n        return token\n      }\n      if (typeof token.content === 'string') {\n        return token.content\n      }\n      return token.content.map(stringifyToken).join('')\n    }\n    var walkTokens = function (tokens) {\n      var openedTags = []\n      for (var i = 0; i < tokens.length; i++) {\n        var token = tokens[i]\n        var notTagNorBrace = false\n        if (typeof token !== 'string') {\n          if (\n            token.type === 'tag' &&\n            token.content[0] &&\n            token.content[0].type === 'tag'\n          ) {\n            // We found a tag, now find its kind\n            if (token.content[0].content[0].content === '</') {\n              // Closing tag\n              if (\n                openedTags.length > 0 &&\n                openedTags[openedTags.length - 1].tagName ===\n                  stringifyToken(token.content[0].content[1])\n              ) {\n                // Pop matching opening tag\n                openedTags.pop()\n              }\n            } else {\n              if (token.content[token.content.length - 1].content === '/>') {\n                // Autoclosed tag, ignore\n              } else {\n                // Opening tag\n                openedTags.push({\n                  tagName: stringifyToken(token.content[0].content[1]),\n                  openedBraces: 0\n                })\n              }\n            }\n          } else if (\n            openedTags.length > 0 &&\n            token.type === 'punctuation' &&\n            token.content === '{' && // Ignore `{{`\n            (!tokens[i + 1] ||\n              tokens[i + 1].type !== 'punctuation' ||\n              tokens[i + 1].content !== '{') &&\n            (!tokens[i - 1] ||\n              tokens[i - 1].type !== 'plain-text' ||\n              tokens[i - 1].content !== '{')\n          ) {\n            // Here we might have entered an XQuery expression inside a tag\n            openedTags[openedTags.length - 1].openedBraces++\n          } else if (\n            openedTags.length > 0 &&\n            openedTags[openedTags.length - 1].openedBraces > 0 &&\n            token.type === 'punctuation' &&\n            token.content === '}'\n          ) {\n            // Here we might have left an XQuery expression inside a tag\n            openedTags[openedTags.length - 1].openedBraces--\n          } else if (token.type !== 'comment') {\n            notTagNorBrace = true\n          }\n        }\n        if (notTagNorBrace || typeof token === 'string') {\n          if (\n            openedTags.length > 0 &&\n            openedTags[openedTags.length - 1].openedBraces === 0\n          ) {\n            // Here we are inside a tag, and not inside an XQuery expression.\n            // That's plain text: drop any tokens matched.\n            var plainText = stringifyToken(token) // And merge text with adjacent text\n            if (\n              i < tokens.length - 1 &&\n              (typeof tokens[i + 1] === 'string' ||\n                tokens[i + 1].type === 'plain-text')\n            ) {\n              plainText += stringifyToken(tokens[i + 1])\n              tokens.splice(i + 1, 1)\n            }\n            if (\n              i > 0 &&\n              (typeof tokens[i - 1] === 'string' ||\n                tokens[i - 1].type === 'plain-text')\n            ) {\n              plainText = stringifyToken(tokens[i - 1]) + plainText\n              tokens.splice(i - 1, 1)\n              i--\n            }\n            if (/^\\s+$/.test(plainText)) {\n              tokens[i] = plainText\n            } else {\n              tokens[i] = new Prism.Token(\n                'plain-text',\n                plainText,\n                null,\n                plainText\n              )\n            }\n          }\n        }\n        if (token.content && typeof token.content !== 'string') {\n          walkTokens(token.content)\n        }\n      }\n    }\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (env.language !== 'xquery') {\n        return\n      }\n      walkTokens(env.tokens)\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,OAAO,UAAU;AAAA,UACxD,kBAAkB;AAAA,YAChB,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,WAAW;AAAA,YACT,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,UACV,MAAM;AAAA,YACJ,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,oBAAoB;AAAA,YAClB,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACP,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,UAAU;AAAA,UACV,kBAAkB;AAAA,YAChB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACP,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,QAAQ;AAAA,UACR,UAAU;AAAA,YACR;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf,CAAC;AACD,QAAAA,OAAM,UAAU,OAAO,IAAI,UACzB;AACF,QAAAA,OAAM,UAAU,OAAO,KAAK,EAAE,OAAO,YAAY,EAAE,UACjD;AACF,QAAAA,OAAM,UAAU,OAAO,KAAK,EAAE,OAAO,YAAY,EAAE,OAAO,aAAa,IACrE;AACF,QAAAA,OAAM,UAAU,OAAO,KAAK,EAAE,OAAO,YAAY,EAAE,OAAO,YAAY,IAAI;AAAA;AAAA,UAExE,SAAS;AAAA,UACT,QAAQA,OAAM,UAAU;AAAA,UACxB,OAAO;AAAA,QACT;AACA,YAAI,iBAAiB,SAAU,OAAO;AACpC,cAAI,OAAO,UAAU,UAAU;AAC7B,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,MAAM,YAAY,UAAU;AACrC,mBAAO,MAAM;AAAA,UACf;AACA,iBAAO,MAAM,QAAQ,IAAI,cAAc,EAAE,KAAK,EAAE;AAAA,QAClD;AACA,YAAI,aAAa,SAAU,QAAQ;AACjC,cAAI,aAAa,CAAC;AAClB,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAI,QAAQ,OAAO,CAAC;AACpB,gBAAI,iBAAiB;AACrB,gBAAI,OAAO,UAAU,UAAU;AAC7B,kBACE,MAAM,SAAS,SACf,MAAM,QAAQ,CAAC,KACf,MAAM,QAAQ,CAAC,EAAE,SAAS,OAC1B;AAEA,oBAAI,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,YAAY,MAAM;AAEhD,sBACE,WAAW,SAAS,KACpB,WAAW,WAAW,SAAS,CAAC,EAAE,YAChC,eAAe,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC,GAC5C;AAEA,+BAAW,IAAI;AAAA,kBACjB;AAAA,gBACF,OAAO;AACL,sBAAI,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,EAAE,YAAY,MAAM;AAAA,kBAE9D,OAAO;AAEL,+BAAW,KAAK;AAAA,sBACd,SAAS,eAAe,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;AAAA,sBACnD,cAAc;AAAA,oBAChB,CAAC;AAAA,kBACH;AAAA,gBACF;AAAA,cACF,WACE,WAAW,SAAS,KACpB,MAAM,SAAS,iBACf,MAAM,YAAY;AAAA,eACjB,CAAC,OAAO,IAAI,CAAC,KACZ,OAAO,IAAI,CAAC,EAAE,SAAS,iBACvB,OAAO,IAAI,CAAC,EAAE,YAAY,SAC3B,CAAC,OAAO,IAAI,CAAC,KACZ,OAAO,IAAI,CAAC,EAAE,SAAS,gBACvB,OAAO,IAAI,CAAC,EAAE,YAAY,MAC5B;AAEA,2BAAW,WAAW,SAAS,CAAC,EAAE;AAAA,cACpC,WACE,WAAW,SAAS,KACpB,WAAW,WAAW,SAAS,CAAC,EAAE,eAAe,KACjD,MAAM,SAAS,iBACf,MAAM,YAAY,KAClB;AAEA,2BAAW,WAAW,SAAS,CAAC,EAAE;AAAA,cACpC,WAAW,MAAM,SAAS,WAAW;AACnC,iCAAiB;AAAA,cACnB;AAAA,YACF;AACA,gBAAI,kBAAkB,OAAO,UAAU,UAAU;AAC/C,kBACE,WAAW,SAAS,KACpB,WAAW,WAAW,SAAS,CAAC,EAAE,iBAAiB,GACnD;AAGA,oBAAI,YAAY,eAAe,KAAK;AACpC,oBACE,IAAI,OAAO,SAAS,MACnB,OAAO,OAAO,IAAI,CAAC,MAAM,YACxB,OAAO,IAAI,CAAC,EAAE,SAAS,eACzB;AACA,+BAAa,eAAe,OAAO,IAAI,CAAC,CAAC;AACzC,yBAAO,OAAO,IAAI,GAAG,CAAC;AAAA,gBACxB;AACA,oBACE,IAAI,MACH,OAAO,OAAO,IAAI,CAAC,MAAM,YACxB,OAAO,IAAI,CAAC,EAAE,SAAS,eACzB;AACA,8BAAY,eAAe,OAAO,IAAI,CAAC,CAAC,IAAI;AAC5C,yBAAO,OAAO,IAAI,GAAG,CAAC;AACtB;AAAA,gBACF;AACA,oBAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,yBAAO,CAAC,IAAI;AAAA,gBACd,OAAO;AACL,yBAAO,CAAC,IAAI,IAAIA,OAAM;AAAA,oBACpB;AAAA,oBACA;AAAA,oBACA;AAAA,oBACA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM,WAAW,OAAO,MAAM,YAAY,UAAU;AACtD,yBAAW,MAAM,OAAO;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,cAAI,IAAI,aAAa,UAAU;AAC7B;AAAA,UACF;AACA,qBAAW,IAAI,MAAM;AAAA,QACvB,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}