{"version": 3, "sources": ["../../highlight.js/lib/languages/ruby.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Ruby\nDescription: Ruby is a dynamic, open source programming language with a focus on simplicity and productivity.\nWebsite: https://www.ruby-lang.org/\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <gojpe<PERSON>@yandex.ru>, <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nCategory: common\n*/\n\nfunction ruby(hljs) {\n  const RUBY_METHOD_RE = '([a-zA-Z_]\\\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\\\*\\\\*|[-/+%^&*~`|]|\\\\[\\\\]=?)';\n  const R<PERSON>BY_KEYWORDS = {\n    keyword:\n      'and then defined module in return redo if BEGIN retry end for self when ' +\n      'next until do begin unless END rescue else break undef not super class case ' +\n      'require yield alias while ensure elsif or include attr_reader attr_writer attr_accessor ' +\n      '__FILE__',\n    built_in: 'proc lambda',\n    literal:\n      'true false nil'\n  };\n  const YARDOCTAG = {\n    className: 'doctag',\n    begin: '@[A-Za-z]+'\n  };\n  const IRB_OBJECT = {\n    begin: '#<',\n    end: '>'\n  };\n  const COMMENT_MODES = [\n    hljs.COMMENT(\n      '#',\n      '$',\n      {\n        contains: [ YARDOCTAG ]\n      }\n    ),\n    hljs.COMMENT(\n      '^=begin',\n      '^=end',\n      {\n        contains: [ YARDOCTAG ],\n        relevance: 10\n      }\n    ),\n    hljs.COMMENT('^__END__', '\\\\n$')\n  ];\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: RUBY_KEYWORDS\n  };\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ],\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      {\n        begin: /`/,\n        end: /`/\n      },\n      {\n        begin: /%[qQwWx]?\\(/,\n        end: /\\)/\n      },\n      {\n        begin: /%[qQwWx]?\\[/,\n        end: /\\]/\n      },\n      {\n        begin: /%[qQwWx]?\\{/,\n        end: /\\}/\n      },\n      {\n        begin: /%[qQwWx]?</,\n        end: />/\n      },\n      {\n        begin: /%[qQwWx]?\\//,\n        end: /\\//\n      },\n      {\n        begin: /%[qQwWx]?%/,\n        end: /%/\n      },\n      {\n        begin: /%[qQwWx]?-/,\n        end: /-/\n      },\n      {\n        begin: /%[qQwWx]?\\|/,\n        end: /\\|/\n      },\n      // in the following expressions, \\B in the beginning suppresses recognition of ?-sequences\n      // where ? is the last character of a preceding identifier, as in: `func?4`\n      {\n        begin: /\\B\\?(\\\\\\d{1,3})/\n      },\n      {\n        begin: /\\B\\?(\\\\x[A-Fa-f0-9]{1,2})/\n      },\n      {\n        begin: /\\B\\?(\\\\u\\{?[A-Fa-f0-9]{1,6}\\}?)/\n      },\n      {\n        begin: /\\B\\?(\\\\M-\\\\C-|\\\\M-\\\\c|\\\\c\\\\M-|\\\\M-|\\\\C-\\\\M-)[\\x20-\\x7e]/\n      },\n      {\n        begin: /\\B\\?\\\\(c|C-)[\\x20-\\x7e]/\n      },\n      {\n        begin: /\\B\\?\\\\?\\S/\n      },\n      { // heredocs\n        begin: /<<[-~]?'?(\\w+)\\n(?:[^\\n]*\\n)*?\\s*\\1\\b/,\n        returnBegin: true,\n        contains: [\n          {\n            begin: /<<[-~]?'?/\n          },\n          hljs.END_SAME_AS_BEGIN({\n            begin: /(\\w+)/,\n            end: /(\\w+)/,\n            contains: [\n              hljs.BACKSLASH_ESCAPE,\n              SUBST\n            ]\n          })\n        ]\n      }\n    ]\n  };\n\n  // Ruby syntax is underdocumented, but this grammar seems to be accurate\n  // as of version 2.7.2 (confirmed with (irb and `Ripper.sexp(...)`)\n  // https://docs.ruby-lang.org/en/2.7.0/doc/syntax/literals_rdoc.html#label-Numbers\n  const decimal = '[1-9](_?[0-9])*|0';\n  const digits = '[0-9](_?[0-9])*';\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // decimal integer/float, optionally exponential or rational, optionally imaginary\n      {\n        begin: `\\\\b(${decimal})(\\\\.(${digits}))?([eE][+-]?(${digits})|r)?i?\\\\b`\n      },\n\n      // explicit decimal/binary/octal/hexadecimal integer,\n      // optionally rational and/or imaginary\n      {\n        begin: \"\\\\b0[dD][0-9](_?[0-9])*r?i?\\\\b\"\n      },\n      {\n        begin: \"\\\\b0[bB][0-1](_?[0-1])*r?i?\\\\b\"\n      },\n      {\n        begin: \"\\\\b0[oO][0-7](_?[0-7])*r?i?\\\\b\"\n      },\n      {\n        begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*r?i?\\\\b\"\n      },\n\n      // 0-prefixed implicit octal integer, optionally rational and/or imaginary\n      {\n        begin: \"\\\\b0(_?[0-7])+r?i?\\\\b\"\n      }\n    ]\n  };\n\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)',\n    endsParent: true,\n    keywords: RUBY_KEYWORDS\n  };\n\n  const RUBY_DEFAULT_CONTAINS = [\n    STRING,\n    {\n      className: 'class',\n      beginKeywords: 'class module',\n      end: '$|;',\n      illegal: /=/,\n      contains: [\n        hljs.inherit(hljs.TITLE_MODE, {\n          begin: '[A-Za-z_]\\\\w*(::\\\\w+)*(\\\\?|!)?'\n        }),\n        {\n          begin: '<\\\\s*',\n          contains: [\n            {\n              begin: '(' + hljs.IDENT_RE + '::)?' + hljs.IDENT_RE,\n              // we already get points for <, we don't need poitns\n              // for the name also\n              relevance: 0\n            }\n          ]\n        }\n      ].concat(COMMENT_MODES)\n    },\n    {\n      className: 'function',\n      // def method_name(\n      // def method_name;\n      // def method_name (end of line)\n      begin: concat(/def\\s+/, lookahead(RUBY_METHOD_RE + \"\\\\s*(\\\\(|;|$)\")),\n      relevance: 0, // relevance comes from kewords\n      keywords: \"def\",\n      end: '$|;',\n      contains: [\n        hljs.inherit(hljs.TITLE_MODE, {\n          begin: RUBY_METHOD_RE\n        }),\n        PARAMS\n      ].concat(COMMENT_MODES)\n    },\n    {\n      // swallow namespace qualifiers before symbols\n      begin: hljs.IDENT_RE + '::'\n    },\n    {\n      className: 'symbol',\n      begin: hljs.UNDERSCORE_IDENT_RE + '(!|\\\\?)?:',\n      relevance: 0\n    },\n    {\n      className: 'symbol',\n      begin: ':(?!\\\\s)',\n      contains: [\n        STRING,\n        {\n          begin: RUBY_METHOD_RE\n        }\n      ],\n      relevance: 0\n    },\n    NUMBER,\n    {\n      // negative-look forward attemps to prevent false matches like:\n      // @ident@ or $ident$ that might indicate this is not ruby at all\n      className: \"variable\",\n      begin: '(\\\\$\\\\W)|((\\\\$|@@?)(\\\\w+))(?=[^@$?])' + `(?![A-Za-z])(?![@$?'])`\n    },\n    {\n      className: 'params',\n      begin: /\\|/,\n      end: /\\|/,\n      relevance: 0, // this could be a lot of things (in other languages) other than params\n      keywords: RUBY_KEYWORDS\n    },\n    { // regexp container\n      begin: '(' + hljs.RE_STARTERS_RE + '|unless)\\\\s*',\n      keywords: 'unless',\n      contains: [\n        {\n          className: 'regexp',\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ],\n          illegal: /\\n/,\n          variants: [\n            {\n              begin: '/',\n              end: '/[a-z]*'\n            },\n            {\n              begin: /%r\\{/,\n              end: /\\}[a-z]*/\n            },\n            {\n              begin: '%r\\\\(',\n              end: '\\\\)[a-z]*'\n            },\n            {\n              begin: '%r!',\n              end: '![a-z]*'\n            },\n            {\n              begin: '%r\\\\[',\n              end: '\\\\][a-z]*'\n            }\n          ]\n        }\n      ].concat(IRB_OBJECT, COMMENT_MODES),\n      relevance: 0\n    }\n  ].concat(IRB_OBJECT, COMMENT_MODES);\n\n  SUBST.contains = RUBY_DEFAULT_CONTAINS;\n  PARAMS.contains = RUBY_DEFAULT_CONTAINS;\n\n  // >>\n  // ?>\n  const SIMPLE_PROMPT = \"[>?]>\";\n  // irb(main):001:0>\n  const DEFAULT_PROMPT = \"[\\\\w#]+\\\\(\\\\w+\\\\):\\\\d+:\\\\d+>\";\n  const RVM_PROMPT = \"(\\\\w+-)?\\\\d+\\\\.\\\\d+\\\\.\\\\d+(p\\\\d+)?[^\\\\d][^>]+>\";\n\n  const IRB_DEFAULT = [\n    {\n      begin: /^\\s*=>/,\n      starts: {\n        end: '$',\n        contains: RUBY_DEFAULT_CONTAINS\n      }\n    },\n    {\n      className: 'meta',\n      begin: '^(' + SIMPLE_PROMPT + \"|\" + DEFAULT_PROMPT + '|' + RVM_PROMPT + ')(?=[ ])',\n      starts: {\n        end: '$',\n        contains: RUBY_DEFAULT_CONTAINS\n      }\n    }\n  ];\n\n  COMMENT_MODES.unshift(IRB_OBJECT);\n\n  return {\n    name: 'Ruby',\n    aliases: [\n      'rb',\n      'gemspec',\n      'podspec',\n      'thor',\n      'irb'\n    ],\n    keywords: RUBY_KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: [\n      hljs.SHEBANG({\n        binary: \"ruby\"\n      })\n    ]\n      .concat(IRB_DEFAULT)\n      .concat(COMMENT_MODES)\n      .concat(RUBY_DEFAULT_CONTAINS)\n  };\n}\n\nmodule.exports = ruby;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,IAAI;AACrB,aAAO,OAAO,OAAO,IAAI,GAAG;AAAA,IAC9B;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAWA,aAAS,KAAK,MAAM;AAClB,YAAM,iBAAiB;AACvB,YAAM,gBAAgB;AAAA,QACpB,SACE;AAAA,QAIF,UAAU;AAAA,QACV,SACE;AAAA,MACJ;AACA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AACA,YAAM,gBAAgB;AAAA,QACpB,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,YACE,UAAU,CAAE,SAAU;AAAA,UACxB;AAAA,QACF;AAAA,QACA,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,YACE,UAAU,CAAE,SAAU;AAAA,YACtB,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,KAAK,QAAQ,YAAY,MAAM;AAAA,MACjC;AACA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA;AAAA;AAAA,UAGA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,YACP,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA,KAAK,kBAAkB;AAAA,gBACrB,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,kBACR,KAAK;AAAA,kBACL;AAAA,gBACF;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAKA,YAAM,UAAU;AAChB,YAAM,SAAS;AACf,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA;AAAA,UAER;AAAA,YACE,OAAO,OAAO,OAAO,SAAS,MAAM,iBAAiB,MAAM;AAAA,UAC7D;AAAA;AAAA;AAAA,UAIA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA;AAAA,UAGA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ;AAEA,YAAM,wBAAwB;AAAA,QAC5B;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,eAAe;AAAA,UACf,KAAK;AAAA,UACL,SAAS;AAAA,UACT,UAAU;AAAA,YACR,KAAK,QAAQ,KAAK,YAAY;AAAA,cAC5B,OAAO;AAAA,YACT,CAAC;AAAA,YACD;AAAA,cACE,OAAO;AAAA,cACP,UAAU;AAAA,gBACR;AAAA,kBACE,OAAO,MAAM,KAAK,WAAW,SAAS,KAAK;AAAA;AAAA;AAAA,kBAG3C,WAAW;AAAA,gBACb;AAAA,cACF;AAAA,YACF;AAAA,UACF,EAAE,OAAO,aAAa;AAAA,QACxB;AAAA,QACA;AAAA,UACE,WAAW;AAAA;AAAA;AAAA;AAAA,UAIX,OAAO,OAAO,UAAU,UAAU,iBAAiB,eAAe,CAAC;AAAA,UACnE,WAAW;AAAA;AAAA,UACX,UAAU;AAAA,UACV,KAAK;AAAA,UACL,UAAU;AAAA,YACR,KAAK,QAAQ,KAAK,YAAY;AAAA,cAC5B,OAAO;AAAA,YACT,CAAC;AAAA,YACD;AAAA,UACF,EAAE,OAAO,aAAa;AAAA,QACxB;AAAA,QACA;AAAA;AAAA,UAEE,OAAO,KAAK,WAAW;AAAA,QACzB;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,KAAK,sBAAsB;AAAA,UAClC,WAAW;AAAA,QACb;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,UAAU;AAAA,YACR;AAAA,YACA;AAAA,cACE,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA;AAAA;AAAA,UAGE,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA;AAAA,UACX,UAAU;AAAA,QACZ;AAAA,QACA;AAAA;AAAA,UACE,OAAO,MAAM,KAAK,iBAAiB;AAAA,UACnC,UAAU;AAAA,UACV,UAAU;AAAA,YACR;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA,gBACR,KAAK;AAAA,gBACL;AAAA,cACF;AAAA,cACA,SAAS;AAAA,cACT,UAAU;AAAA,gBACR;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,gBACA;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,gBACA;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,gBACA;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,gBACA;AAAA,kBACE,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,YACF;AAAA,UACF,EAAE,OAAO,YAAY,aAAa;AAAA,UAClC,WAAW;AAAA,QACb;AAAA,MACF,EAAE,OAAO,YAAY,aAAa;AAElC,YAAM,WAAW;AACjB,aAAO,WAAW;AAIlB,YAAM,gBAAgB;AAEtB,YAAM,iBAAiB;AACvB,YAAM,aAAa;AAEnB,YAAM,cAAc;AAAA,QAClB;AAAA,UACE,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,KAAK;AAAA,YACL,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,OAAO,gBAAgB,MAAM,iBAAiB,MAAM,aAAa;AAAA,UACxE,QAAQ;AAAA,YACN,KAAK;AAAA,YACL,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAEA,oBAAc,QAAQ,UAAU;AAEhC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK,QAAQ;AAAA,YACX,QAAQ;AAAA,UACV,CAAC;AAAA,QACH,EACG,OAAO,WAAW,EAClB,OAAO,aAAa,EACpB,OAAO,qBAAqB;AAAA,MACjC;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}