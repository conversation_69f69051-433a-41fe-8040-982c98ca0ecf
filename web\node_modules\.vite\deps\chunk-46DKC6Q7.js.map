{"version": 3, "sources": ["../../refractor/lang/pascal.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = pascal\npascal.displayName = 'pascal'\npascal.aliases = ['objectpascal']\nfunction pascal(Prism) {\n  // Based on Free Pascal\n  /* TODO\nSupport inline asm ?\n*/\n  Prism.languages.pascal = {\n    directive: {\n      pattern: /\\{\\$[\\s\\S]*?\\}/,\n      greedy: true,\n      alias: ['marco', 'property']\n    },\n    comment: {\n      pattern: /\\(\\*[\\s\\S]*?\\*\\)|\\{[\\s\\S]*?\\}|\\/\\/.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /(?:'(?:''|[^'\\r\\n])*'(?!')|#[&$%]?[a-f\\d]+)+|\\^[a-z]/i,\n      greedy: true\n    },\n    asm: {\n      pattern: /(\\basm\\b)[\\s\\S]+?(?=\\bend\\s*[;[])/i,\n      lookbehind: true,\n      greedy: true,\n      inside: null // see below\n    },\n    keyword: [\n      {\n        // Turbo Pascal\n        pattern:\n          /(^|[^&])\\b(?:absolute|array|asm|begin|case|const|constructor|destructor|do|downto|else|end|file|for|function|goto|if|implementation|inherited|inline|interface|label|nil|object|of|operator|packed|procedure|program|record|reintroduce|repeat|self|set|string|then|to|type|unit|until|uses|var|while|with)\\b/i,\n        lookbehind: true\n      },\n      {\n        // Free Pascal\n        pattern: /(^|[^&])\\b(?:dispose|exit|false|new|true)\\b/i,\n        lookbehind: true\n      },\n      {\n        // Object Pascal\n        pattern:\n          /(^|[^&])\\b(?:class|dispinterface|except|exports|finalization|finally|initialization|inline|library|on|out|packed|property|raise|resourcestring|threadvar|try)\\b/i,\n        lookbehind: true\n      },\n      {\n        // Modifiers\n        pattern:\n          /(^|[^&])\\b(?:absolute|abstract|alias|assembler|bitpacked|break|cdecl|continue|cppdecl|cvar|default|deprecated|dynamic|enumerator|experimental|export|external|far|far16|forward|generic|helper|implements|index|interrupt|iochecks|local|message|name|near|nodefault|noreturn|nostackframe|oldfpccall|otherwise|overload|override|pascal|platform|private|protected|public|published|read|register|reintroduce|result|safecall|saveregisters|softfloat|specialize|static|stdcall|stored|strict|unaligned|unimplemented|varargs|virtual|write)\\b/i,\n        lookbehind: true\n      }\n    ],\n    number: [\n      // Hexadecimal, octal and binary\n      /(?:[&%]\\d+|\\$[a-f\\d]+)/i, // Decimal\n      /\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?/i\n    ],\n    operator: [\n      /\\.\\.|\\*\\*|:=|<[<=>]?|>[>=]?|[+\\-*\\/]=?|[@^=]/,\n      {\n        pattern:\n          /(^|[^&])\\b(?:and|as|div|exclude|in|include|is|mod|not|or|shl|shr|xor)\\b/,\n        lookbehind: true\n      }\n    ],\n    punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.]/\n  }\n  Prism.languages.pascal.asm.inside = Prism.languages.extend('pascal', {\n    asm: undefined,\n    keyword: undefined,\n    operator: undefined\n  })\n  Prism.languages.objectpascal = Prism.languages.pascal\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC,cAAc;AAChC,aAAS,OAAO,OAAO;AAKrB,YAAM,UAAU,SAAS;AAAA,QACvB,WAAW;AAAA,UACT,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO,CAAC,SAAS,UAAU;AAAA,QAC7B;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,KAAK;AAAA,UACH,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA;AAAA,QACV;AAAA,QACA,SAAS;AAAA,UACP;AAAA;AAAA,YAEE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA;AAAA,YAEE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA;AAAA;AAAA,YAEE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,QAAQ;AAAA;AAAA,UAEN;AAAA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,aAAa;AAAA,MACf;AACA,YAAM,UAAU,OAAO,IAAI,SAAS,MAAM,UAAU,OAAO,UAAU;AAAA,QACnE,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC;AACD,YAAM,UAAU,eAAe,MAAM,UAAU;AAAA,IACjD;AAAA;AAAA;", "names": []}