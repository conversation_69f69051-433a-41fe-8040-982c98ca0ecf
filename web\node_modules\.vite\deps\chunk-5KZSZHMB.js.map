{"version": 3, "sources": ["../../refractor/lang/chaiscript.js"], "sourcesContent": ["'use strict'\nvar refractorCpp = require('./cpp.js')\nmodule.exports = chaiscript\nchaiscript.displayName = 'chaiscript'\nchaiscript.aliases = []\nfunction chaiscript(Prism) {\n  Prism.register(refractorCpp)\n  Prism.languages.chaiscript = Prism.languages.extend('clike', {\n    string: {\n      pattern: /(^|[^\\\\])'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': [\n      {\n        // e.g. class Rectangle { ... }\n        pattern: /(\\bclass\\s+)\\w+/,\n        lookbehind: true\n      },\n      {\n        // e.g. attr Rectangle::height, def Rectangle::area() { ... }\n        pattern: /(\\b(?:attr|def)\\s+)\\w+(?=\\s*::)/,\n        lookbehind: true\n      }\n    ],\n    keyword:\n      /\\b(?:attr|auto|break|case|catch|class|continue|def|default|else|finally|for|fun|global|if|return|switch|this|try|var|while)\\b/,\n    number: [Prism.languages.cpp.number, /\\b(?:Infinity|NaN)\\b/],\n    operator:\n      />>=?|<<=?|\\|\\||&&|:[:=]?|--|\\+\\+|[=!<>+\\-*/%|&^]=?|[?~]|`[^`\\r\\n]{1,4}`/\n  })\n  Prism.languages.insertBefore('chaiscript', 'operator', {\n    'parameter-type': {\n      // e.g. def foo(int x, Vector y) {...}\n      pattern: /([,(]\\s*)\\w+(?=\\s+\\w)/,\n      lookbehind: true,\n      alias: 'class-name'\n    }\n  })\n  Prism.languages.insertBefore('chaiscript', 'string', {\n    'string-interpolation': {\n      pattern:\n        /(^|[^\\\\])\"(?:[^\"$\\\\]|\\\\[\\s\\S]|\\$(?!\\{)|\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\})*\"/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern:\n            /((?:^|[^\\\\])(?:\\\\{2})*)\\$\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\}/,\n          lookbehind: true,\n          inside: {\n            'interpolation-expression': {\n              pattern: /(^\\$\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true,\n              inside: Prism.languages.chaiscript\n            },\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{|\\}$/,\n              alias: 'punctuation'\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,eAAe;AACnB,WAAO,UAAU;AACjB,eAAW,cAAc;AACzB,eAAW,UAAU,CAAC;AACtB,aAAS,WAAW,OAAO;AACzB,YAAM,SAAS,YAAY;AAC3B,YAAM,UAAU,aAAa,MAAM,UAAU,OAAO,SAAS;AAAA,QAC3D,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,SACE;AAAA,QACF,QAAQ,CAAC,MAAM,UAAU,IAAI,QAAQ,sBAAsB;AAAA,QAC3D,UACE;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,aAAa,cAAc,YAAY;AAAA,QACrD,kBAAkB;AAAA;AAAA,UAEhB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,cAAc,UAAU;AAAA,QACnD,wBAAwB;AAAA,UACtB,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,4BAA4B;AAAA,kBAC1B,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQ,MAAM,UAAU;AAAA,gBAC1B;AAAA,gBACA,6BAA6B;AAAA,kBAC3B,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}