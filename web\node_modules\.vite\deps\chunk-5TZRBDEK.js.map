{"version": 3, "sources": ["../../refractor/lang/apex.js"], "sourcesContent": ["'use strict'\nvar refractorSql = require('./sql.js')\nmodule.exports = apex\napex.displayName = 'apex'\napex.aliases = []\nfunction apex(Prism) {\n  Prism.register(refractorSql)\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:(?:after|before)(?=\\s+[a-z])|abstract|activate|and|any|array|as|asc|autonomous|begin|bigdecimal|blob|boolean|break|bulk|by|byte|case|cast|catch|char|class|collect|commit|const|continue|currency|date|datetime|decimal|default|delete|desc|do|double|else|end|enum|exception|exit|export|extends|final|finally|float|for|from|get(?=\\s*[{};])|global|goto|group|having|hint|if|implements|import|in|inner|insert|instanceof|int|integer|interface|into|join|like|limit|list|long|loop|map|merge|new|not|null|nulls|number|object|of|on|or|outer|override|package|parallel|pragma|private|protected|public|retrieve|return|rollback|select|set|short|sObject|sort|static|string|super|switch|synchronized|system|testmethod|then|this|throw|time|transaction|transient|trigger|try|undelete|update|upsert|using|virtual|void|webservice|when|where|while|(?:inherited|with|without)\\s+sharing)\\b/i\n    var className =\n      /\\b(?:(?=[a-z_]\\w*\\s*[<\\[])|(?!<keyword>))[A-Z_]\\w*(?:\\s*\\.\\s*[A-Z_]\\w*)*\\b(?:\\s*(?:\\[\\s*\\]|<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>))*/.source.replace(\n        /<keyword>/g,\n        function () {\n          return keywords.source\n        }\n      )\n    /** @param {string} pattern */\n    function insertClassName(pattern) {\n      return RegExp(\n        pattern.replace(/<CLASS-NAME>/g, function () {\n          return className\n        }),\n        'i'\n      )\n    }\n    var classNameInside = {\n      keyword: keywords,\n      punctuation: /[()\\[\\]{};,:.<>]/\n    }\n    Prism.languages.apex = {\n      comment: Prism.languages.clike.comment,\n      string: Prism.languages.clike.string,\n      sql: {\n        pattern: /((?:[=,({:]|\\breturn)\\s*)\\[[^\\[\\]]*\\]/i,\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-sql',\n        inside: Prism.languages.sql\n      },\n      annotation: {\n        pattern: /@\\w+\\b/,\n        alias: 'punctuation'\n      },\n      'class-name': [\n        {\n          pattern: insertClassName(\n            /(\\b(?:class|enum|extends|implements|instanceof|interface|new|trigger\\s+\\w+\\s+on)\\s+)<CLASS-NAME>/\n              .source\n          ),\n          lookbehind: true,\n          inside: classNameInside\n        },\n        {\n          // cast\n          pattern: insertClassName(\n            /(\\(\\s*)<CLASS-NAME>(?=\\s*\\)\\s*[\\w(])/.source\n          ),\n          lookbehind: true,\n          inside: classNameInside\n        },\n        {\n          // variable/parameter declaration and return types\n          pattern: insertClassName(/<CLASS-NAME>(?=\\s*\\w+\\s*[;=,(){:])/.source),\n          inside: classNameInside\n        }\n      ],\n      trigger: {\n        pattern: /(\\btrigger\\s+)\\w+\\b/i,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      keyword: keywords,\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n      boolean: /\\b(?:false|true)\\b/i,\n      number: /(?:\\B\\.\\d+|\\b\\d+(?:\\.\\d+|L)?)\\b/i,\n      operator:\n        /[!=](?:==?)?|\\?\\.?|&&|\\|\\||--|\\+\\+|[-+*/^&|]=?|:|<<?=?|>{1,3}=?/,\n      punctuation: /[()\\[\\]{};,.]/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,eAAe;AACnB,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,SAAS,YAAY;AAC1B,OAAC,SAAUA,QAAO;AACjB,YAAI,WACF;AACF,YAAI,YACF,mIAAmI,OAAO;AAAA,UACxI;AAAA,UACA,WAAY;AACV,mBAAO,SAAS;AAAA,UAClB;AAAA,QACF;AAEF,iBAAS,gBAAgB,SAAS;AAChC,iBAAO;AAAA,YACL,QAAQ,QAAQ,iBAAiB,WAAY;AAC3C,qBAAO;AAAA,YACT,CAAC;AAAA,YACD;AAAA,UACF;AAAA,QACF;AACA,YAAI,kBAAkB;AAAA,UACpB,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AACA,QAAAA,OAAM,UAAU,OAAO;AAAA,UACrB,SAASA,OAAM,UAAU,MAAM;AAAA,UAC/B,QAAQA,OAAM,UAAU,MAAM;AAAA,UAC9B,KAAK;AAAA,YACH,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,UACA,YAAY;AAAA,YACV,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,cAAc;AAAA,YACZ;AAAA,cACE,SAAS;AAAA,gBACP,mGACG;AAAA,cACL;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,gBACP,uCAAuC;AAAA,cACzC;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA,cAEE,SAAS,gBAAgB,qCAAqC,MAAM;AAAA,cACpE,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,UACT,UAAU;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,UACE;AAAA,UACF,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}