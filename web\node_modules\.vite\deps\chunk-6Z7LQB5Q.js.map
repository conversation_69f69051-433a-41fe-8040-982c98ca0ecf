{"version": 3, "sources": ["../../refractor/lang/solidity.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = solidity\nsolidity.displayName = 'solidity'\nsolidity.aliases = ['sol']\nfunction solidity(Prism) {\n  Prism.languages.solidity = Prism.languages.extend('clike', {\n    'class-name': {\n      pattern:\n        /(\\b(?:contract|enum|interface|library|new|struct|using)\\s+)(?!\\d)[\\w$]+/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:_|anonymous|as|assembly|assert|break|calldata|case|constant|constructor|continue|contract|default|delete|do|else|emit|enum|event|external|for|from|function|if|import|indexed|inherited|interface|internal|is|let|library|mapping|memory|modifier|new|payable|pragma|private|public|pure|require|returns?|revert|selfdestruct|solidity|storage|struct|suicide|switch|this|throw|using|var|view|while)\\b/,\n    operator: /=>|->|:=|=:|\\*\\*|\\+\\+|--|\\|\\||&&|<<=?|>>=?|[-+*/%^&|<>!=]=?|[~?]/\n  })\n  Prism.languages.insertBefore('solidity', 'keyword', {\n    builtin:\n      /\\b(?:address|bool|byte|u?int(?:8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?|string|bytes(?:[1-9]|[12]\\d|3[0-2])?)\\b/\n  })\n  Prism.languages.insertBefore('solidity', 'number', {\n    version: {\n      pattern: /([<>]=?|\\^)\\d+\\.\\d+\\.\\d+\\b/,\n      lookbehind: true,\n      alias: 'number'\n    }\n  })\n  Prism.languages.sol = Prism.languages.solidity\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC,KAAK;AACzB,aAAS,SAAS,OAAO;AACvB,YAAM,UAAU,WAAW,MAAM,UAAU,OAAO,SAAS;AAAA,QACzD,cAAc;AAAA,UACZ,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,MACZ,CAAC;AACD,YAAM,UAAU,aAAa,YAAY,WAAW;AAAA,QAClD,SACE;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,aAAa,YAAY,UAAU;AAAA,QACjD,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,YAAM,UAAU,MAAM,MAAM,UAAU;AAAA,IACxC;AAAA;AAAA;", "names": []}