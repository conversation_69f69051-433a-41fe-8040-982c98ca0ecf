{"version": 3, "sources": ["../../refractor/lang/kumir.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = kumir\nkumir.displayName = 'kumir'\nkumir.aliases = ['kum']\nfunction kumir(Prism) {\n  /* eslint-disable regexp/no-dupe-characters-character-class */\n  ;(function (Prism) {\n    /**\n     * Regular expression for characters that are not allowed in identifiers.\n     *\n     * @type {string}\n     */\n    var nonId = /\\s\\x00-\\x1f\\x22-\\x2f\\x3a-\\x3f\\x5b-\\x5e\\x60\\x7b-\\x7e/.source\n    /**\n     * Surround a regular expression for IDs with patterns for non-ID sequences.\n     *\n     * @param {string} pattern A regular expression for identifiers.\n     * @param {string} [flags] The regular expression flags.\n     * @returns {RegExp} A wrapped regular expression for identifiers.\n     */\n    function wrapId(pattern, flags) {\n      return RegExp(pattern.replace(/<nonId>/g, nonId), flags)\n    }\n    Prism.languages.kumir = {\n      comment: {\n        pattern: /\\|.*/\n      },\n      prolog: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\\n\\r\"]*\"|'[^\\n\\r']*'/,\n        greedy: true\n      },\n      boolean: {\n        pattern: wrapId(/(^|[<nonId>])(?:да|нет)(?=[<nonId>]|$)/.source),\n        lookbehind: true\n      },\n      'operator-word': {\n        pattern: wrapId(/(^|[<nonId>])(?:и|или|не)(?=[<nonId>]|$)/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      'system-variable': {\n        pattern: wrapId(/(^|[<nonId>])знач(?=[<nonId>]|$)/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      type: [\n        {\n          pattern: wrapId(\n            /(^|[<nonId>])(?:вещ|лит|лог|сим|цел)(?:\\x20*таб)?(?=[<nonId>]|$)/\n              .source\n          ),\n          lookbehind: true,\n          alias: 'builtin'\n        },\n        {\n          pattern: wrapId(\n            /(^|[<nonId>])(?:компл|сканкод|файл|цвет)(?=[<nonId>]|$)/.source\n          ),\n          lookbehind: true,\n          alias: 'important'\n        }\n      ],\n      /**\n       * Should be performed after searching for type names because of \"таб\".\n       * \"таб\" is a reserved word, but never used without a preceding type name.\n       * \"НАЗНАЧИТЬ\", \"Фввод\", and \"Фвывод\" are not reserved words.\n       */\n      keyword: {\n        pattern: wrapId(\n          /(^|[<nonId>])(?:алг|арг(?:\\x20*рез)?|ввод|ВКЛЮЧИТЬ|вс[её]|выбор|вывод|выход|дано|для|до|дс|если|иначе|исп|использовать|кон(?:(?:\\x20+|_)исп)?|кц(?:(?:\\x20+|_)при)?|надо|нач|нс|нц|от|пауза|пока|при|раза?|рез|стоп|таб|то|утв|шаг)(?=[<nonId>]|$)/\n            .source\n        ),\n        lookbehind: true\n      },\n      /** Should be performed after searching for reserved words. */\n      name: {\n        // eslint-disable-next-line regexp/no-super-linear-backtracking\n        pattern: wrapId(\n          /(^|[<nonId>])[^\\d<nonId>][^<nonId>]*(?:\\x20+[^<nonId>]+)*(?=[<nonId>]|$)/\n            .source\n        ),\n        lookbehind: true\n      },\n      /** Should be performed after searching for names. */\n      number: {\n        pattern: wrapId(\n          /(^|[<nonId>])(?:\\B\\$[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)(?=[<nonId>]|$)/\n            .source,\n          'i'\n        ),\n        lookbehind: true\n      },\n      /** Should be performed after searching for words. */\n      punctuation: /:=|[(),:;\\[\\]]/,\n      /**\n       * Should be performed after searching for\n       * - numeric constants (because of \"+\" and \"-\");\n       * - punctuation marks (because of \":=\" and \"=\").\n       */\n      'operator-char': {\n        pattern: /\\*\\*?|<[=>]?|>=?|[-+/=]/,\n        alias: 'operator'\n      }\n    }\n    Prism.languages.kum = Prism.languages.kumir\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC,KAAK;AACtB,aAAS,MAAM,OAAO;AAEpB;AAAC,OAAC,SAAUA,QAAO;AAMjB,YAAI,QAAQ,sDAAsD;AAQlE,iBAAS,OAAO,SAAS,OAAO;AAC9B,iBAAO,OAAO,QAAQ,QAAQ,YAAY,KAAK,GAAG,KAAK;AAAA,QACzD;AACA,QAAAA,OAAM,UAAU,QAAQ;AAAA,UACtB,SAAS;AAAA,YACP,SAAS;AAAA,UACX;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,SAAS;AAAA,YACP,SAAS,OAAO,yCAAyC,MAAM;AAAA,YAC/D,YAAY;AAAA,UACd;AAAA,UACA,iBAAiB;AAAA,YACf,SAAS,OAAO,2CAA2C,MAAM;AAAA,YACjE,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,mBAAmB;AAAA,YACjB,SAAS,OAAO,mCAAmC,MAAM;AAAA,YACzD,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,YACJ;AAAA,cACE,SAAS;AAAA,gBACP,mEACG;AAAA,cACL;AAAA,cACA,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP,0DAA0D;AAAA,cAC5D;AAAA,cACA,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,UACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMA,SAAS;AAAA,YACP,SAAS;AAAA,cACP,qPACG;AAAA,YACL;AAAA,YACA,YAAY;AAAA,UACd;AAAA;AAAA,UAEA,MAAM;AAAA;AAAA,YAEJ,SAAS;AAAA,cACP,2EACG;AAAA,YACL;AAAA,YACA,YAAY;AAAA,UACd;AAAA;AAAA,UAEA,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,2FACG;AAAA,cACH;AAAA,YACF;AAAA,YACA,YAAY;AAAA,UACd;AAAA;AAAA,UAEA,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAMb,iBAAiB;AAAA,YACf,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AAAA,MACxC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}