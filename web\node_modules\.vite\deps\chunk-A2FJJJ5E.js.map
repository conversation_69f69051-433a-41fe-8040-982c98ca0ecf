{"version": 3, "sources": ["../../highlight.js/lib/languages/bash.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: Bash\nAuthor: vah <<EMAIL>>\nContributrors: <PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/bash/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction bash(hljs) {\n  const VAR = {};\n  const BRACED_VAR = {\n    begin: /\\$\\{/,\n    end:/\\}/,\n    contains: [\n      \"self\",\n      {\n        begin: /:-/,\n        contains: [ VAR ]\n      } // default values\n    ]\n  };\n  Object.assign(VAR,{\n    className: 'variable',\n    variants: [\n      {begin: concat(/\\$[\\w\\d#@][\\w\\d_]*/,\n        // negative look-ahead tries to avoid matching patterns that are not\n        // Perl at all like $ident$, @ident@, etc.\n        `(?![\\\\w\\\\d])(?![$])`) },\n      BRACED_VAR\n    ]\n  });\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\$\\(/, end: /\\)/,\n    contains: [hljs.BACKSLASH_ESCAPE]\n  };\n  const HERE_DOC = {\n    begin: /<<-?\\s*(?=\\w+)/,\n    starts: {\n      contains: [\n        hljs.END_SAME_AS_BEGIN({\n          begin: /(\\w+)/,\n          end: /(\\w+)/,\n          className: 'string'\n        })\n      ]\n    }\n  };\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/, end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VAR,\n      SUBST\n    ]\n  };\n  SUBST.contains.push(QUOTE_STRING);\n  const ESCAPED_QUOTE = {\n    className: '',\n    begin: /\\\\\"/\n\n  };\n  const APOS_STRING = {\n    className: 'string',\n    begin: /'/, end: /'/\n  };\n  const ARITHMETIC = {\n    begin: /\\$\\(\\(/,\n    end: /\\)\\)/,\n    contains: [\n      { begin: /\\d+#[0-9a-f]+/, className: \"number\" },\n      hljs.NUMBER_MODE,\n      VAR\n    ]\n  };\n  const SH_LIKE_SHELLS = [\n    \"fish\",\n    \"bash\",\n    \"zsh\",\n    \"sh\",\n    \"csh\",\n    \"ksh\",\n    \"tcsh\",\n    \"dash\",\n    \"scsh\",\n  ];\n  const KNOWN_SHEBANG = hljs.SHEBANG({\n    binary: `(${SH_LIKE_SHELLS.join(\"|\")})`,\n    relevance: 10\n  });\n  const FUNCTION = {\n    className: 'function',\n    begin: /\\w[\\w\\d_]*\\s*\\(\\s*\\)\\s*\\{/,\n    returnBegin: true,\n    contains: [hljs.inherit(hljs.TITLE_MODE, {begin: /\\w[\\w\\d_]*/})],\n    relevance: 0\n  };\n\n  return {\n    name: 'Bash',\n    aliases: ['sh', 'zsh'],\n    keywords: {\n      $pattern: /\\b[a-z._-]+\\b/,\n      keyword:\n        'if then else elif fi for while in do done case esac function',\n      literal:\n        'true false',\n      built_in:\n        // Shell built-ins\n        // http://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n        'break cd continue eval exec exit export getopts hash pwd readonly return shift test times ' +\n        'trap umask unset ' +\n        // Bash built-ins\n        'alias bind builtin caller command declare echo enable help let local logout mapfile printf ' +\n        'read readarray source type typeset ulimit unalias ' +\n        // Shell modifiers\n        'set shopt ' +\n        // Zsh built-ins\n        'autoload bg bindkey bye cap chdir clone comparguments compcall compctl compdescribe compfiles ' +\n        'compgroups compquote comptags comptry compvalues dirs disable disown echotc echoti emulate ' +\n        'fc fg float functions getcap getln history integer jobs kill limit log noglob popd print ' +\n        'pushd pushln rehash sched setcap setopt stat suspend ttyctl unfunction unhash unlimit ' +\n        'unsetopt vared wait whence where which zcompile zformat zftp zle zmodload zparseopts zprof ' +\n        'zpty zregexparse zsocket zstyle ztcp'\n    },\n    contains: [\n      KNOWN_SHEBANG, // to catch known shells and boost relevancy\n      hljs.SHEBANG(), // to catch unknown shells but still highlight the shebang\n      FUNCTION,\n      ARITHMETIC,\n      hljs.HASH_COMMENT_MODE,\n      HERE_DOC,\n      QUOTE_STRING,\n      ESCAPED_QUOTE,\n      APOS_STRING,\n      VAR\n    ]\n  };\n}\n\nmodule.exports = bash;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAWA,aAAS,KAAK,MAAM;AAClB,YAAM,MAAM,CAAC;AACb,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,KAAI;AAAA,QACJ,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,UAAU,CAAE,GAAI;AAAA,UAClB;AAAA;AAAA,QACF;AAAA,MACF;AACA,aAAO,OAAO,KAAI;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAC,OAAO;AAAA,YAAO;AAAA;AAAA;AAAA,YAGb;AAAA,UAAqB,EAAE;AAAA,UACzB;AAAA,QACF;AAAA,MACF,CAAC;AAED,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QAAQ,KAAK;AAAA,QACpB,UAAU,CAAC,KAAK,gBAAgB;AAAA,MAClC;AACA,YAAM,WAAW;AAAA,QACf,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,UAAU;AAAA,YACR,KAAK,kBAAkB;AAAA,cACrB,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AACA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QAAK,KAAK;AAAA,QACjB,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS,KAAK,YAAY;AAChC,YAAM,gBAAgB;AAAA,QACpB,WAAW;AAAA,QACX,OAAO;AAAA,MAET;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,QAAK,KAAK;AAAA,MACnB;AACA,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,EAAE,OAAO,iBAAiB,WAAW,SAAS;AAAA,UAC9C,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,YAAM,iBAAiB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,gBAAgB,KAAK,QAAQ;AAAA,QACjC,QAAQ,IAAI,eAAe,KAAK,GAAG,CAAC;AAAA,QACpC,WAAW;AAAA,MACb,CAAC;AACD,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU,CAAC,KAAK,QAAQ,KAAK,YAAY,EAAC,OAAO,aAAY,CAAC,CAAC;AAAA,QAC/D,WAAW;AAAA,MACb;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,MAAM,KAAK;AAAA,QACrB,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SACE;AAAA,UACF,SACE;AAAA,UACF;AAAA;AAAA;AAAA,YAGE;AAAA;AAAA,QAcJ;AAAA,QACA,UAAU;AAAA,UACR;AAAA;AAAA,UACA,KAAK,QAAQ;AAAA;AAAA,UACb;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}