{"version": 3, "sources": ["../../refractor/lang/nix.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = nix\nnix.displayName = 'nix'\nnix.aliases = []\nfunction nix(Prism) {\n  Prism.languages.nix = {\n    comment: {\n      pattern: /\\/\\*[\\s\\S]*?\\*\\/|#.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"|''(?:(?!'')[\\s\\S]|''(?:'|\\\\|\\$\\{))*''/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          // The lookbehind ensures the ${} is not preceded by \\ or ''\n          pattern: /(^|(?:^|(?!'').)[^\\\\])\\$\\{(?:[^{}]|\\{[^}]*\\})*\\}/,\n          lookbehind: true,\n          inside: null // see below\n        }\n      }\n    },\n    url: [\n      /\\b(?:[a-z]{3,7}:\\/\\/)[\\w\\-+%~\\/.:#=?&]+/,\n      {\n        pattern:\n          /([^\\/])(?:[\\w\\-+%~.:#=?&]*(?!\\/\\/)[\\w\\-+%~\\/.:#=?&])?(?!\\/\\/)\\/[\\w\\-+%~\\/.:#=?&]*/,\n        lookbehind: true\n      }\n    ],\n    antiquotation: {\n      pattern: /\\$(?=\\{)/,\n      alias: 'important'\n    },\n    number: /\\b\\d+\\b/,\n    keyword: /\\b(?:assert|builtins|else|if|in|inherit|let|null|or|then|with)\\b/,\n    function:\n      /\\b(?:abort|add|all|any|attrNames|attrValues|baseNameOf|compareVersions|concatLists|currentSystem|deepSeq|derivation|dirOf|div|elem(?:At)?|fetch(?:Tarball|url)|filter(?:Source)?|fromJSON|genList|getAttr|getEnv|hasAttr|hashString|head|import|intersectAttrs|is(?:Attrs|Bool|Function|Int|List|Null|String)|length|lessThan|listToAttrs|map|mul|parseDrvName|pathExists|read(?:Dir|File)|removeAttrs|replaceStrings|seq|sort|stringLength|sub(?:string)?|tail|throw|to(?:File|JSON|Path|String|XML)|trace|typeOf)\\b|\\bfoldl'\\B/,\n    boolean: /\\b(?:false|true)\\b/,\n    operator: /[=!<>]=?|\\+\\+?|\\|\\||&&|\\/\\/|->?|[?@]/,\n    punctuation: /[{}()[\\].,:;]/\n  }\n  Prism.languages.nix.string.inside.interpolation.inside = Prism.languages.nix\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,UAAU,MAAM;AAAA,QACpB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA;AAAA,cAEb,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAAA,QACA,KAAK;AAAA,UACH;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,eAAe;AAAA,UACb,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UACE;AAAA,QACF,SAAS;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AACA,YAAM,UAAU,IAAI,OAAO,OAAO,cAAc,SAAS,MAAM,UAAU;AAAA,IAC3E;AAAA;AAAA;", "names": []}