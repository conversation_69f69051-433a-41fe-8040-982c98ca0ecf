{"version": 3, "sources": ["../../highlight.js/lib/languages/scheme.js"], "sourcesContent": ["/*\nLanguage: Scheme\nDescription: Scheme is a programming language in the Lisp family.\n             (keywords based on http://community.schemewiki.org/?scheme-keywords)\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nOrigin: clojure.js\nWebsite: http://community.schemewiki.org/?what-is-scheme\nCategory: lisp\n*/\n\nfunction scheme(hljs) {\n  const SCHEME_IDENT_RE = '[^\\\\(\\\\)\\\\[\\\\]\\\\{\\\\}\",\\'`;#|\\\\\\\\\\\\s]+';\n  const SCHEME_SIMPLE_NUMBER_RE = '(-|\\\\+)?\\\\d+([./]\\\\d+)?';\n  const SCHEME_COMPLEX_NUMBER_RE = SCHEME_SIMPLE_NUMBER_RE + '[+\\\\-]' + SCHEME_SIMPLE_NUMBER_RE + 'i';\n  const KEYWORDS = {\n    $pattern: SCHEME_IDENT_RE,\n    'builtin-name':\n      'case-lambda call/cc class define-class exit-handler field import ' +\n      'inherit init-field interface let*-values let-values let/ec mixin ' +\n      'opt-lambda override protect provide public rename require ' +\n      'require-for-syntax syntax syntax-case syntax-error unit/sig unless ' +\n      'when with-syntax and begin call-with-current-continuation ' +\n      'call-with-input-file call-with-output-file case cond define ' +\n      'define-syntax delay do dynamic-wind else for-each if lambda let let* ' +\n      'let-syntax letrec letrec-syntax map or syntax-rules \\' * + , ,@ - ... / ' +\n      '; < <= = => > >= ` abs acos angle append apply asin assoc assq assv atan ' +\n      'boolean? caar cadr call-with-input-file call-with-output-file ' +\n      'call-with-values car cdddar cddddr cdr ceiling char->integer ' +\n      'char-alphabetic? char-ci<=? char-ci<? char-ci=? char-ci>=? char-ci>? ' +\n      'char-downcase char-lower-case? char-numeric? char-ready? char-upcase ' +\n      'char-upper-case? char-whitespace? char<=? char<? char=? char>=? char>? ' +\n      'char? close-input-port close-output-port complex? cons cos ' +\n      'current-input-port current-output-port denominator display eof-object? ' +\n      'eq? equal? eqv? eval even? exact->inexact exact? exp expt floor ' +\n      'force gcd imag-part inexact->exact inexact? input-port? integer->char ' +\n      'integer? interaction-environment lcm length list list->string ' +\n      'list->vector list-ref list-tail list? load log magnitude make-polar ' +\n      'make-rectangular make-string make-vector max member memq memv min ' +\n      'modulo negative? newline not null-environment null? number->string ' +\n      'number? numerator odd? open-input-file open-output-file output-port? ' +\n      'pair? peek-char port? positive? procedure? quasiquote quote quotient ' +\n      'rational? rationalize read read-char real-part real? remainder reverse ' +\n      'round scheme-report-environment set! set-car! set-cdr! sin sqrt string ' +\n      'string->list string->number string->symbol string-append string-ci<=? ' +\n      'string-ci<? string-ci=? string-ci>=? string-ci>? string-copy ' +\n      'string-fill! string-length string-ref string-set! string<=? string<? ' +\n      'string=? string>=? string>? string? substring symbol->string symbol? ' +\n      'tan transcript-off transcript-on truncate values vector ' +\n      'vector->list vector-fill! vector-length vector-ref vector-set! ' +\n      'with-input-from-file with-output-to-file write write-char zero?'\n  };\n\n  const LITERAL = {\n    className: 'literal',\n    begin: '(#t|#f|#\\\\\\\\' + SCHEME_IDENT_RE + '|#\\\\\\\\.)'\n  };\n\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      {\n        begin: SCHEME_SIMPLE_NUMBER_RE,\n        relevance: 0\n      },\n      {\n        begin: SCHEME_COMPLEX_NUMBER_RE,\n        relevance: 0\n      },\n      {\n        begin: '#b[0-1]+(/[0-1]+)?'\n      },\n      {\n        begin: '#o[0-7]+(/[0-7]+)?'\n      },\n      {\n        begin: '#x[0-9a-f]+(/[0-9a-f]+)?'\n      }\n    ]\n  };\n\n  const STRING = hljs.QUOTE_STRING_MODE;\n\n  const COMMENT_MODES = [\n    hljs.COMMENT(\n      ';',\n      '$',\n      {\n        relevance: 0\n      }\n    ),\n    hljs.COMMENT('#\\\\|', '\\\\|#')\n  ];\n\n  const IDENT = {\n    begin: SCHEME_IDENT_RE,\n    relevance: 0\n  };\n\n  const QUOTED_IDENT = {\n    className: 'symbol',\n    begin: '\\'' + SCHEME_IDENT_RE\n  };\n\n  const BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n\n  const QUOTED_LIST = {\n    variants: [\n      {\n        begin: /'/\n      },\n      {\n        begin: '`'\n      }\n    ],\n    contains: [\n      {\n        begin: '\\\\(',\n        end: '\\\\)',\n        contains: [\n          'self',\n          LITERAL,\n          STRING,\n          NUMBER,\n          IDENT,\n          QUOTED_IDENT\n        ]\n      }\n    ]\n  };\n\n  const NAME = {\n    className: 'name',\n    relevance: 0,\n    begin: SCHEME_IDENT_RE,\n    keywords: KEYWORDS\n  };\n\n  const LAMBDA = {\n    begin: /lambda/,\n    endsWithParent: true,\n    returnBegin: true,\n    contains: [\n      NAME,\n      {\n        endsParent: true,\n        variants: [\n          {\n            begin: /\\(/,\n            end: /\\)/\n          },\n          {\n            begin: /\\[/,\n            end: /\\]/\n          }\n        ],\n        contains: [ IDENT ]\n      }\n    ]\n  };\n\n  const LIST = {\n    variants: [\n      {\n        begin: '\\\\(',\n        end: '\\\\)'\n      },\n      {\n        begin: '\\\\[',\n        end: '\\\\]'\n      }\n    ],\n    contains: [\n      LAMBDA,\n      NAME,\n      BODY\n    ]\n  };\n\n  BODY.contains = [\n    LITERAL,\n    NUMBER,\n    STRING,\n    IDENT,\n    QUOTED_IDENT,\n    QUOTED_LIST,\n    LIST\n  ].concat(COMMENT_MODES);\n\n  return {\n    name: 'Scheme',\n    illegal: /\\S/,\n    contains: [\n      hljs.SHEBANG(),\n      NUMBER,\n      STRING,\n      QUOTED_IDENT,\n      QUOTED_LIST,\n      LIST\n    ].concat(COMMENT_MODES)\n  };\n}\n\nmodule.exports = scheme;\n"], "mappings": ";;;;;AAAA;AAAA;AAWA,aAAS,OAAO,MAAM;AACpB,YAAM,kBAAkB;AACxB,YAAM,0BAA0B;AAChC,YAAM,2BAA2B,0BAA0B,WAAW,0BAA0B;AAChG,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,QACV,gBACE;AAAA,MAiCJ;AAEA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,OAAO,iBAAiB,kBAAkB;AAAA,MAC5C;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS,KAAK;AAEpB,YAAM,gBAAgB;AAAA,QACpB,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,KAAK,QAAQ,QAAQ,MAAM;AAAA,MAC7B;AAEA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO,MAAO;AAAA,MAChB;AAEA,YAAM,OAAO;AAAA,QACX,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACb;AAEA,YAAM,cAAc;AAAA,QAClB,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAEA,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,YACA,UAAU,CAAE,KAAM;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAEA,YAAM,OAAO;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,WAAK,WAAW;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAE,OAAO,aAAa;AAEtB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK,QAAQ;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,EAAE,OAAO,aAAa;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}