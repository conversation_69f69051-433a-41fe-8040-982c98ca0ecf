{"version": 3, "sources": ["../../refractor/lang/tcl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = tcl\ntcl.displayName = 'tcl'\ntcl.aliases = []\nfunction tcl(Prism) {\n  Prism.languages.tcl = {\n    comment: {\n      pattern: /(^|[^\\\\])#.*/,\n      lookbehind: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\"/,\n      greedy: true\n    },\n    variable: [\n      {\n        pattern: /(\\$)(?:::)?(?:[a-zA-Z0-9]+::)*\\w+/,\n        lookbehind: true\n      },\n      {\n        pattern: /(\\$)\\{[^}]+\\}/,\n        lookbehind: true\n      },\n      {\n        pattern: /(^[\\t ]*set[ \\t]+)(?:::)?(?:[a-zA-Z0-9]+::)*\\w+/m,\n        lookbehind: true\n      }\n    ],\n    function: {\n      pattern: /(^[\\t ]*proc[ \\t]+)\\S+/m,\n      lookbehind: true\n    },\n    builtin: [\n      {\n        pattern:\n          /(^[\\t ]*)(?:break|class|continue|error|eval|exit|for|foreach|if|proc|return|switch|while)\\b/m,\n        lookbehind: true\n      },\n      /\\b(?:else|elseif)\\b/\n    ],\n    scope: {\n      pattern: /(^[\\t ]*)(?:global|upvar|variable)\\b/m,\n      lookbehind: true,\n      alias: 'constant'\n    },\n    keyword: {\n      pattern:\n        /(^[\\t ]*|\\[)(?:Safe_Base|Tcl|after|append|apply|array|auto_(?:execok|import|load|mkindex|qualify|reset)|automkindex_old|bgerror|binary|catch|cd|chan|clock|close|concat|dde|dict|encoding|eof|exec|expr|fblocked|fconfigure|fcopy|file(?:event|name)?|flush|gets|glob|history|http|incr|info|interp|join|lappend|lassign|lindex|linsert|list|llength|load|lrange|lrepeat|lreplace|lreverse|lsearch|lset|lsort|math(?:func|op)|memory|msgcat|namespace|open|package|parray|pid|pkg_mkIndex|platform|puts|pwd|re_syntax|read|refchan|regexp|registry|regsub|rename|scan|seek|set|socket|source|split|string|subst|tcl(?:_endOfWord|_findLibrary|startOf(?:Next|Previous)Word|test|vars|wordBreak(?:After|Before))|tell|time|tm|trace|unknown|unload|unset|update|uplevel|vwait)\\b/m,\n      lookbehind: true\n    },\n    operator:\n      /!=?|\\*\\*?|==|&&?|\\|\\|?|<[=<]?|>[=>]?|[-+~\\/%?^]|\\b(?:eq|in|ne|ni)\\b/,\n    punctuation: /[{}()\\[\\]]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,UAAU,MAAM;AAAA,QACpB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,SAAS;AAAA,UACP;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,UACP,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,UACE;AAAA,QACF,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}