{"version": 3, "sources": ["../../highlight.js/lib/languages/hy.js"], "sourcesContent": ["/*\nLanguage: Hy\nDescription: Hy is a wonderful dialect of Lisp that’s embedded in Python.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://docs.hylang.org/en/stable/\nCategory: lisp\n*/\n\nfunction hy(hljs) {\n  var SYMBOLSTART = 'a-zA-Z_\\\\-!.?+*=<>&#\\'';\n  var SYMBOL_RE = '[' + SYMBOLSTART + '][' + SYMBOLSTART + '0-9/;:]*';\n  var keywords = {\n    $pattern: SYMBOL_RE,\n    'builtin-name':\n      // keywords\n      '!= % %= & &= * ** **= *= *map ' +\n      '+ += , --build-class-- --import-- -= . / // //= ' +\n      '/= < << <<= <= = > >= >> >>= ' +\n      '@ @= ^ ^= abs accumulate all and any ap-compose ' +\n      'ap-dotimes ap-each ap-each-while ap-filter ap-first ap-if ap-last ap-map ap-map-when ap-pipe ' +\n      'ap-reduce ap-reject apply as-> ascii assert assoc bin break butlast ' +\n      'callable calling-module-name car case cdr chain chr coll? combinations compile ' +\n      'compress cond cons cons? continue count curry cut cycle dec ' +\n      'def default-method defclass defmacro defmacro-alias defmacro/g! defmain defmethod defmulti defn ' +\n      'defn-alias defnc defnr defreader defseq del delattr delete-route dict-comp dir ' +\n      'disassemble dispatch-reader-macro distinct divmod do doto drop drop-last drop-while empty? ' +\n      'end-sequence eval eval-and-compile eval-when-compile even? every? except exec filter first ' +\n      'flatten float? fn fnc fnr for for* format fraction genexpr ' +\n      'gensym get getattr global globals group-by hasattr hash hex id ' +\n      'identity if if* if-not if-python2 import in inc input instance? ' +\n      'integer integer-char? integer? interleave interpose is is-coll is-cons is-empty is-even ' +\n      'is-every is-float is-instance is-integer is-integer-char is-iterable is-iterator is-keyword is-neg is-none ' +\n      'is-not is-numeric is-odd is-pos is-string is-symbol is-zero isinstance islice issubclass ' +\n      'iter iterable? iterate iterator? keyword keyword? lambda last len let ' +\n      'lif lif-not list* list-comp locals loop macro-error macroexpand macroexpand-1 macroexpand-all ' +\n      'map max merge-with method-decorator min multi-decorator multicombinations name neg? next ' +\n      'none? nonlocal not not-in not? nth numeric? oct odd? open ' +\n      'or ord partition permutations pos? post-route postwalk pow prewalk print ' +\n      'product profile/calls profile/cpu put-route quasiquote quote raise range read read-str ' +\n      'recursive-replace reduce remove repeat repeatedly repr require rest round route ' +\n      'route-with-methods rwm second seq set-comp setattr setv some sorted string ' +\n      'string? sum switch symbol? take take-nth take-while tee try unless ' +\n      'unquote unquote-splicing vars walk when while with with* with-decorator with-gensyms ' +\n      'xi xor yield yield-from zero? zip zip-longest | |= ~'\n   };\n\n  var SIMPLE_NUMBER_RE = '[-+]?\\\\d+(\\\\.\\\\d+)?';\n\n  var SYMBOL = {\n    begin: SYMBOL_RE,\n    relevance: 0\n  };\n  var NUMBER = {\n    className: 'number', begin: SIMPLE_NUMBER_RE,\n    relevance: 0\n  };\n  var STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {illegal: null});\n  var COMMENT = hljs.COMMENT(\n    ';',\n    '$',\n    {\n      relevance: 0\n    }\n  );\n  var LITERAL = {\n    className: 'literal',\n    begin: /\\b([Tt]rue|[Ff]alse|nil|None)\\b/\n  };\n  var COLLECTION = {\n    begin: '[\\\\[\\\\{]', end: '[\\\\]\\\\}]'\n  };\n  var HINT = {\n    className: 'comment',\n    begin: '\\\\^' + SYMBOL_RE\n  };\n  var HINT_COL = hljs.COMMENT('\\\\^\\\\{', '\\\\}');\n  var KEY = {\n    className: 'symbol',\n    begin: '[:]{1,2}' + SYMBOL_RE\n  };\n  var LIST = {\n    begin: '\\\\(', end: '\\\\)'\n  };\n  var BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n  var NAME = {\n    className: 'name',\n    relevance: 0,\n    keywords: keywords,\n    begin: SYMBOL_RE,\n    starts: BODY\n  };\n  var DEFAULT_CONTAINS = [LIST, STRING, HINT, HINT_COL, COMMENT, KEY, COLLECTION, NUMBER, LITERAL, SYMBOL];\n\n  LIST.contains = [hljs.COMMENT('comment', ''), NAME, BODY];\n  BODY.contains = DEFAULT_CONTAINS;\n  COLLECTION.contains = DEFAULT_CONTAINS;\n\n  return {\n    name: 'Hy',\n    aliases: ['hylang'],\n    illegal: /\\S/,\n    contains: [hljs.SHEBANG(), LIST, STRING, HINT, HINT_COL, COMMENT, KEY, COLLECTION, NUMBER, LITERAL]\n  };\n}\n\nmodule.exports = hy;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,GAAG,MAAM;AAChB,UAAI,cAAc;AAClB,UAAI,YAAY,MAAM,cAAc,OAAO,cAAc;AACzD,UAAI,WAAW;AAAA,QACb,UAAU;AAAA,QACV;AAAA;AAAA,UAEE;AAAA;AAAA,MA6BH;AAED,UAAI,mBAAmB;AAEvB,UAAI,SAAS;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,UAAI,SAAS;AAAA,QACX,WAAW;AAAA,QAAU,OAAO;AAAA,QAC5B,WAAW;AAAA,MACb;AACA,UAAI,SAAS,KAAK,QAAQ,KAAK,mBAAmB,EAAC,SAAS,KAAI,CAAC;AACjE,UAAI,UAAU,KAAK;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW;AAAA,QACb;AAAA,MACF;AACA,UAAI,UAAU;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,UAAI,aAAa;AAAA,QACf,OAAO;AAAA,QAAY,KAAK;AAAA,MAC1B;AACA,UAAI,OAAO;AAAA,QACT,WAAW;AAAA,QACX,OAAO,QAAQ;AAAA,MACjB;AACA,UAAI,WAAW,KAAK,QAAQ,UAAU,KAAK;AAC3C,UAAI,MAAM;AAAA,QACR,WAAW;AAAA,QACX,OAAO,aAAa;AAAA,MACtB;AACA,UAAI,OAAO;AAAA,QACT,OAAO;AAAA,QAAO,KAAK;AAAA,MACrB;AACA,UAAI,OAAO;AAAA,QACT,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACb;AACA,UAAI,OAAO;AAAA,QACT,WAAW;AAAA,QACX,WAAW;AAAA,QACX;AAAA,QACA,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,UAAI,mBAAmB,CAAC,MAAM,QAAQ,MAAM,UAAU,SAAS,KAAK,YAAY,QAAQ,SAAS,MAAM;AAEvG,WAAK,WAAW,CAAC,KAAK,QAAQ,WAAW,EAAE,GAAG,MAAM,IAAI;AACxD,WAAK,WAAW;AAChB,iBAAW,WAAW;AAEtB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;AAAA,QAClB,SAAS;AAAA,QACT,UAAU,CAAC,KAAK,QAAQ,GAAG,MAAM,QAAQ,MAAM,UAAU,SAAS,KAAK,YAAY,QAAQ,OAAO;AAAA,MACpG;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}