{"version": 3, "sources": ["../../refractor/lang/dart.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = dart\ndart.displayName = 'dart'\ndart.aliases = []\nfunction dart(Prism) {\n  ;(function (Prism) {\n    var keywords = [\n      /\\b(?:async|sync|yield)\\*/,\n      /\\b(?:abstract|assert|async|await|break|case|catch|class|const|continue|covariant|default|deferred|do|dynamic|else|enum|export|extends|extension|external|factory|final|finally|for|get|hide|if|implements|import|in|interface|library|mixin|new|null|on|operator|part|rethrow|return|set|show|static|super|switch|sync|this|throw|try|typedef|var|void|while|with|yield)\\b/\n    ] // Handles named imports, such as http.Client\n    var packagePrefix = /(^|[^\\w.])(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/\n      .source // based on the dart naming conventions\n    var className = {\n      pattern: RegExp(packagePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n      lookbehind: true,\n      inside: {\n        namespace: {\n          pattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n          inside: {\n            punctuation: /\\./\n          }\n        }\n      }\n    }\n    Prism.languages.dart = Prism.languages.extend('clike', {\n      'class-name': [\n        className,\n        {\n          // variables and parameters\n          // this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n          pattern: RegExp(\n            packagePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()])/.source\n          ),\n          lookbehind: true,\n          inside: className.inside\n        }\n      ],\n      keyword: keywords,\n      operator:\n        /\\bis!|\\b(?:as|is)\\b|\\+\\+|--|&&|\\|\\||<<=?|>>=?|~(?:\\/=?)?|[+\\-*\\/%&^|=!<>]=?|\\?/\n    })\n    Prism.languages.insertBefore('dart', 'string', {\n      'string-literal': {\n        pattern:\n          /r?(?:(\"\"\"|''')[\\s\\S]*?\\1|([\"'])(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2(?!\\2))/,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern:\n              /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\w+|\\{(?:[^{}]|\\{[^{}]*\\})*\\})/,\n            lookbehind: true,\n            inside: {\n              punctuation: /^\\$\\{?|\\}$/,\n              expression: {\n                pattern: /[\\s\\S]+/,\n                inside: Prism.languages.dart\n              }\n            }\n          },\n          string: /[\\s\\S]+/\n        }\n      },\n      string: undefined\n    })\n    Prism.languages.insertBefore('dart', 'class-name', {\n      metadata: {\n        pattern: /@\\w+/,\n        alias: 'function'\n      }\n    })\n    Prism.languages.insertBefore('dart', 'class-name', {\n      generics: {\n        pattern:\n          /<(?:[\\w\\s,.&?]|<(?:[\\w\\s,.&?]|<(?:[\\w\\s,.&?]|<[\\w\\s,.&?]*>)*>)*>)*>/,\n        inside: {\n          'class-name': className,\n          keyword: keywords,\n          punctuation: /[<>(),.:]/,\n          operator: /[?&|]/\n        }\n      }\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,WAAW;AAAA,UACb;AAAA,UACA;AAAA,QACF;AACA,YAAI,gBAAgB,uDACjB;AACH,YAAI,YAAY;AAAA,UACd,SAAS,OAAO,gBAAgB,gCAAgC,MAAM;AAAA,UACtE,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,WAAW;AAAA,cACT,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,OAAO,SAAS;AAAA,UACrD,cAAc;AAAA,YACZ;AAAA,YACA;AAAA;AAAA;AAAA,cAGE,SAAS;AAAA,gBACP,gBAAgB,+BAA+B;AAAA,cACjD;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ,UAAU;AAAA,YACpB;AAAA,UACF;AAAA,UACA,SAAS;AAAA,UACT,UACE;AAAA,QACJ,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,UAC7C,kBAAkB;AAAA,YAChB,SACE;AAAA,YACF,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,eAAe;AAAA,gBACb,SACE;AAAA,gBACF,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,aAAa;AAAA,kBACb,YAAY;AAAA,oBACV,SAAS;AAAA,oBACT,QAAQA,OAAM,UAAU;AAAA,kBAC1B;AAAA,gBACF;AAAA,cACF;AAAA,cACA,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,QAAQ,cAAc;AAAA,UACjD,UAAU;AAAA,YACR,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,QAAQ,cAAc;AAAA,UACjD,UAAU;AAAA,YACR,SACE;AAAA,YACF,QAAQ;AAAA,cACN,cAAc;AAAA,cACd,SAAS;AAAA,cACT,aAAa;AAAA,cACb,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}