{"version": 3, "sources": ["../../refractor/lang/crystal.js"], "sourcesContent": ["'use strict'\nvar refractorRuby = require('./ruby.js')\nmodule.exports = crystal\ncrystal.displayName = 'crystal'\ncrystal.aliases = []\nfunction crystal(Prism) {\n  Prism.register(refractorRuby)\n  ;(function (Prism) {\n    Prism.languages.crystal = Prism.languages.extend('ruby', {\n      keyword: [\n        /\\b(?:__DIR__|__END_LINE__|__FILE__|__LINE__|abstract|alias|annotation|as|asm|begin|break|case|class|def|do|else|elsif|end|ensure|enum|extend|for|fun|if|ifdef|include|instance_sizeof|lib|macro|module|next|of|out|pointerof|private|protected|ptr|require|rescue|return|select|self|sizeof|struct|super|then|type|typeof|undef|uninitialized|union|unless|until|when|while|with|yield)\\b/,\n        {\n          pattern: /(\\.\\s*)(?:is_a|responds_to)\\?/,\n          lookbehind: true\n        }\n      ],\n      number:\n        /\\b(?:0b[01_]*[01]|0o[0-7_]*[0-7]|0x[\\da-fA-F_]*[\\da-fA-F]|(?:\\d(?:[\\d_]*\\d)?)(?:\\.[\\d_]*\\d)?(?:[eE][+-]?[\\d_]*\\d)?)(?:_(?:[uif](?:8|16|32|64))?)?\\b/,\n      operator: [/->/, Prism.languages.ruby.operator],\n      punctuation: /[(){}[\\].,;\\\\]/\n    })\n    Prism.languages.insertBefore('crystal', 'string-literal', {\n      attribute: {\n        pattern: /@\\[.*?\\]/,\n        inside: {\n          delimiter: {\n            pattern: /^@\\[|\\]$/,\n            alias: 'punctuation'\n          },\n          attribute: {\n            pattern: /^(\\s*)\\w+/,\n            lookbehind: true,\n            alias: 'class-name'\n          },\n          args: {\n            pattern: /\\S(?:[\\s\\S]*\\S)?/,\n            inside: Prism.languages.crystal\n          }\n        }\n      },\n      expansion: {\n        pattern: /\\{(?:\\{.*?\\}|%.*?%)\\}/,\n        inside: {\n          content: {\n            pattern: /^(\\{.)[\\s\\S]+(?=.\\}$)/,\n            lookbehind: true,\n            inside: Prism.languages.crystal\n          },\n          delimiter: {\n            pattern: /^\\{[\\{%]|[\\}%]\\}$/,\n            alias: 'operator'\n          }\n        }\n      },\n      char: {\n        pattern:\n          /'(?:[^\\\\\\r\\n]{1,2}|\\\\(?:.|u(?:[A-Fa-f0-9]{1,4}|\\{[A-Fa-f0-9]{1,6}\\})))'/,\n        greedy: true\n      }\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,gBAAgB;AACpB,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AACtB,YAAM,SAAS,aAAa;AAC3B,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,UAAUA,OAAM,UAAU,OAAO,QAAQ;AAAA,UACvD,SAAS;AAAA,YACP;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,QACE;AAAA,UACF,UAAU,CAAC,MAAMA,OAAM,UAAU,KAAK,QAAQ;AAAA,UAC9C,aAAa;AAAA,QACf,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,WAAW,kBAAkB;AAAA,UACxD,WAAW;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,cACA,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,QAAQA,OAAM,UAAU;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,UACA,WAAW;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQA,OAAM,UAAU;AAAA,cAC1B;AAAA,cACA,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,MAAM;AAAA,YACJ,SACE;AAAA,YACF,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}