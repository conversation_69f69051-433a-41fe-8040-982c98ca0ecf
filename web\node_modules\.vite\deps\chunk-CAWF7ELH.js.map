{"version": 3, "sources": ["../../highlight.js/lib/languages/asciidoc.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: AsciiDoc\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://asciidoc.org\nDescription: A semantic, text-based document format that can be exported to HTML, DocBook and other backends.\nCategory: markup\n*/\n\n/** @type LanguageFn */\nfunction asciidoc(hljs) {\n  const HORIZONTAL_RULE = {\n    begin: '^\\'{3,}[ \\\\t]*$',\n    relevance: 10\n  };\n  const ESCAPED_FORMATTING = [\n    // escaped constrained formatting marks (i.e., \\* \\_ or \\`)\n    {\n      begin: /\\\\[*_`]/\n    },\n    // escaped unconstrained formatting marks (i.e., \\\\** \\\\__ or \\\\``)\n    // must ignore until the next formatting marks\n    // this rule might not be 100% compliant with Asciidoctor 2.0 but we are entering undefined behavior territory...\n    {\n      begin: /\\\\\\\\\\*{2}[^\\n]*?\\*{2}/\n    },\n    {\n      begin: /\\\\\\\\_{2}[^\\n]*_{2}/\n    },\n    {\n      begin: /\\\\\\\\`{2}[^\\n]*`{2}/\n    },\n    // guard: constrained formatting mark may not be preceded by \":\", \";\" or\n    // \"}\". match these so the constrained rule doesn't see them\n    {\n      begin: /[:;}][*_`](?![*_`])/\n    }\n  ];\n  const STRONG = [\n    // inline unconstrained strong (single line)\n    {\n      className: 'strong',\n      begin: /\\*{2}([^\\n]+?)\\*{2}/\n    },\n    // inline unconstrained strong (multi-line)\n    {\n      className: 'strong',\n      begin: concat(\n        /\\*\\*/,\n        /((\\*(?!\\*)|\\\\[^\\n]|[^*\\n\\\\])+\\n)+/,\n        /(\\*(?!\\*)|\\\\[^\\n]|[^*\\n\\\\])*/,\n        /\\*\\*/\n      ),\n      relevance: 0\n    },\n    // inline constrained strong (single line)\n    {\n      className: 'strong',\n      // must not precede or follow a word character\n      begin: /\\B\\*(\\S|\\S[^\\n]*?\\S)\\*(?!\\w)/\n    },\n    // inline constrained strong (multi-line)\n    {\n      className: 'strong',\n      // must not precede or follow a word character\n      begin: /\\*[^\\s]([^\\n]+\\n)+([^\\n]+)\\*/\n    }\n  ];\n  const EMPHASIS = [\n    // inline unconstrained emphasis (single line)\n    {\n      className: 'emphasis',\n      begin: /_{2}([^\\n]+?)_{2}/\n    },\n    // inline unconstrained emphasis (multi-line)\n    {\n      className: 'emphasis',\n      begin: concat(\n        /__/,\n        /((_(?!_)|\\\\[^\\n]|[^_\\n\\\\])+\\n)+/,\n        /(_(?!_)|\\\\[^\\n]|[^_\\n\\\\])*/,\n        /__/\n      ),\n      relevance: 0\n    },\n    // inline constrained emphasis (single line)\n    {\n      className: 'emphasis',\n      // must not precede or follow a word character\n      begin: /\\b_(\\S|\\S[^\\n]*?\\S)_(?!\\w)/\n    },\n    // inline constrained emphasis (multi-line)\n    {\n      className: 'emphasis',\n      // must not precede or follow a word character\n      begin: /_[^\\s]([^\\n]+\\n)+([^\\n]+)_/\n    },\n    // inline constrained emphasis using single quote (legacy)\n    {\n      className: 'emphasis',\n      // must not follow a word character or be followed by a single quote or space\n      begin: '\\\\B\\'(?![\\'\\\\s])',\n      end: '(\\\\n{2}|\\')',\n      // allow escaped single quote followed by word char\n      contains: [{\n        begin: '\\\\\\\\\\'\\\\w',\n        relevance: 0\n      }],\n      relevance: 0\n    }\n  ];\n  const ADMONITION = {\n    className: 'symbol',\n    begin: '^(NOTE|TIP|IMPORTANT|WARNING|CAUTION):\\\\s+',\n    relevance: 10\n  };\n  const BULLET_LIST = {\n    className: 'bullet',\n    begin: '^(\\\\*+|-+|\\\\.+|[^\\\\n]+?::)\\\\s+'\n  };\n\n  return {\n    name: 'AsciiDoc',\n    aliases: ['adoc'],\n    contains: [\n      // block comment\n      hljs.COMMENT(\n        '^/{4,}\\\\n',\n        '\\\\n/{4,}$',\n        // can also be done as...\n        // '^/{4,}$',\n        // '^/{4,}$',\n        {\n          relevance: 10\n        }\n      ),\n      // line comment\n      hljs.COMMENT(\n        '^//',\n        '$',\n        {\n          relevance: 0\n        }\n      ),\n      // title\n      {\n        className: 'title',\n        begin: '^\\\\.\\\\w.*$'\n      },\n      // example, admonition & sidebar blocks\n      {\n        begin: '^[=\\\\*]{4,}\\\\n',\n        end: '\\\\n^[=\\\\*]{4,}$',\n        relevance: 10\n      },\n      // headings\n      {\n        className: 'section',\n        relevance: 10,\n        variants: [\n          {\n            begin: '^(={1,6})[ \\t].+?([ \\t]\\\\1)?$'\n          },\n          {\n            begin: '^[^\\\\[\\\\]\\\\n]+?\\\\n[=\\\\-~\\\\^\\\\+]{2,}$'\n          }\n        ]\n      },\n      // document attributes\n      {\n        className: 'meta',\n        begin: '^:.+?:',\n        end: '\\\\s',\n        excludeEnd: true,\n        relevance: 10\n      },\n      // block attributes\n      {\n        className: 'meta',\n        begin: '^\\\\[.+?\\\\]$',\n        relevance: 0\n      },\n      // quoteblocks\n      {\n        className: 'quote',\n        begin: '^_{4,}\\\\n',\n        end: '\\\\n_{4,}$',\n        relevance: 10\n      },\n      // listing and literal blocks\n      {\n        className: 'code',\n        begin: '^[\\\\-\\\\.]{4,}\\\\n',\n        end: '\\\\n[\\\\-\\\\.]{4,}$',\n        relevance: 10\n      },\n      // passthrough blocks\n      {\n        begin: '^\\\\+{4,}\\\\n',\n        end: '\\\\n\\\\+{4,}$',\n        contains: [{\n          begin: '<',\n          end: '>',\n          subLanguage: 'xml',\n          relevance: 0\n        }],\n        relevance: 10\n      },\n\n      BULLET_LIST,\n      ADMONITION,\n      ...ESCAPED_FORMATTING,\n      ...STRONG,\n      ...EMPHASIS,\n\n      // inline smart quotes\n      {\n        className: 'string',\n        variants: [\n          {\n            begin: \"``.+?''\"\n          },\n          {\n            begin: \"`.+?'\"\n          }\n        ]\n      },\n      // inline unconstrained emphasis\n      {\n        className: 'code',\n        begin: /`{2}/,\n        end: /(\\n{2}|`{2})/\n      },\n      // inline code snippets (TODO should get same treatment as strong and emphasis)\n      {\n        className: 'code',\n        begin: '(`.+?`|\\\\+.+?\\\\+)',\n        relevance: 0\n      },\n      // indented literal block\n      {\n        className: 'code',\n        begin: '^[ \\\\t]',\n        end: '$',\n        relevance: 0\n      },\n      HORIZONTAL_RULE,\n      // images and links\n      {\n        begin: '(link:)?(http|https|ftp|file|irc|image:?):\\\\S+?\\\\[[^[]*?\\\\]',\n        returnBegin: true,\n        contains: [\n          {\n            begin: '(link|image:?):',\n            relevance: 0\n          },\n          {\n            className: 'link',\n            begin: '\\\\w',\n            end: '[^\\\\[]+',\n            relevance: 0\n          },\n          {\n            className: 'string',\n            begin: '\\\\[',\n            end: '\\\\]',\n            excludeBegin: true,\n            excludeEnd: true,\n            relevance: 0\n          }\n        ],\n        relevance: 10\n      }\n    ]\n  };\n}\n\nmodule.exports = asciidoc;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAYA,aAAS,SAAS,MAAM;AACtB,YAAM,kBAAkB;AAAA,QACtB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,qBAAqB;AAAA;AAAA,QAEzB;AAAA,UACE,OAAO;AAAA,QACT;AAAA;AAAA;AAAA;AAAA,QAIA;AAAA,UACE,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,OAAO;AAAA,QACT;AAAA,QACA;AAAA,UACE,OAAO;AAAA,QACT;AAAA;AAAA;AAAA,QAGA;AAAA,UACE,OAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,SAAS;AAAA;AAAA,QAEb;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA;AAAA,QAEA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,UACA,WAAW;AAAA,QACb;AAAA;AAAA,QAEA;AAAA,UACE,WAAW;AAAA;AAAA,UAEX,OAAO;AAAA,QACT;AAAA;AAAA,QAEA;AAAA,UACE,WAAW;AAAA;AAAA,UAEX,OAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,WAAW;AAAA;AAAA,QAEf;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA;AAAA,QAEA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,UACA,WAAW;AAAA,QACb;AAAA;AAAA,QAEA;AAAA,UACE,WAAW;AAAA;AAAA,UAEX,OAAO;AAAA,QACT;AAAA;AAAA,QAEA;AAAA,UACE,WAAW;AAAA;AAAA,UAEX,OAAO;AAAA,QACT;AAAA;AAAA,QAEA;AAAA,UACE,WAAW;AAAA;AAAA,UAEX,OAAO;AAAA,UACP,KAAK;AAAA;AAAA,UAEL,UAAU,CAAC;AAAA,YACT,OAAO;AAAA,YACP,WAAW;AAAA,UACb,CAAC;AAAA,UACD,WAAW;AAAA,QACb;AAAA,MACF;AACA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,MAAM;AAAA,QAChB,UAAU;AAAA;AAAA,UAER,KAAK;AAAA,YACH;AAAA,YACA;AAAA;AAAA;AAAA;AAAA,YAIA;AAAA,cACE,WAAW;AAAA,YACb;AAAA,UACF;AAAA;AAAA,UAEA,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,YACb;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC;AAAA,cACT,OAAO;AAAA,cACP,KAAK;AAAA,cACL,aAAa;AAAA,cACb,WAAW;AAAA,YACb,CAAC;AAAA,YACD,WAAW;AAAA,UACb;AAAA,UAEA;AAAA,UACA;AAAA,UACA,GAAG;AAAA,UACH,GAAG;AAAA,UACH,GAAG;AAAA;AAAA,UAGH;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,WAAW;AAAA,cACb;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}