{"version": 3, "sources": ["../../highlight.js/lib/languages/smalltalk.js"], "sourcesContent": ["/*\nLanguage: Smalltalk\nDescription: Smalltalk is an object-oriented, dynamically typed reflective programming language.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Smalltalk\n*/\n\nfunction smalltalk(hljs) {\n  const VAR_IDENT_RE = '[a-z][a-zA-Z0-9_]*';\n  const CHAR = {\n    className: 'string',\n    begin: '\\\\$.{1}'\n  };\n  const SYMBOL = {\n    className: 'symbol',\n    begin: '#' + hljs.UNDERSCORE_IDENT_RE\n  };\n  return {\n    name: 'Smalltalk',\n    aliases: [ 'st' ],\n    keywords: 'self super nil true false thisContext', // only 6\n    contains: [\n      hljs.COMMENT('\"', '\"'),\n      hljs.APOS_STRING_MODE,\n      {\n        className: 'type',\n        begin: '\\\\b[A-Z][A-Za-z0-9_]*',\n        relevance: 0\n      },\n      {\n        begin: VAR_IDENT_RE + ':',\n        relevance: 0\n      },\n      hljs.C_NUMBER_MODE,\n      SYMBOL,\n      CHAR,\n      {\n        // This looks more complicated than needed to avoid combinatorial\n        // explosion under V8. It effectively means `| var1 var2 ... |` with\n        // whitespace adjacent to `|` being optional.\n        begin: '\\\\|[ ]*' + VAR_IDENT_RE + '([ ]+' + VAR_IDENT_RE + ')*[ ]*\\\\|',\n        returnBegin: true,\n        end: /\\|/,\n        illegal: /\\S/,\n        contains: [ {\n          begin: '(\\\\|[ ]*)?' + VAR_IDENT_RE\n        } ]\n      },\n      {\n        begin: '#\\\\(',\n        end: '\\\\)',\n        contains: [\n          hljs.APOS_STRING_MODE,\n          CHAR,\n          hljs.C_NUMBER_MODE,\n          SYMBOL\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = smalltalk;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,UAAU,MAAM;AACvB,YAAM,eAAe;AACrB,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO,MAAM,KAAK;AAAA,MACpB;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,UAAU;AAAA;AAAA,QACV,UAAU;AAAA,UACR,KAAK,QAAQ,KAAK,GAAG;AAAA,UACrB,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO,eAAe;AAAA,YACtB,WAAW;AAAA,UACb;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,YAIE,OAAO,YAAY,eAAe,UAAU,eAAe;AAAA,YAC3D,aAAa;AAAA,YACb,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAE;AAAA,cACV,OAAO,eAAe;AAAA,YACxB,CAAE;AAAA,UACJ;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA,KAAK;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}