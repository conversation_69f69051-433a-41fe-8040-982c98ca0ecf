{"version": 3, "sources": ["../../refractor/lang/coffeescript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = coffeescript\ncoffeescript.displayName = 'coffeescript'\ncoffeescript.aliases = ['coffee']\nfunction coffeescript(Prism) {\n  ;(function (Prism) {\n    // Ignore comments starting with { to privilege string interpolation highlighting\n    var comment = /#(?!\\{).+/\n    var interpolation = {\n      pattern: /#\\{[^}]+\\}/,\n      alias: 'variable'\n    }\n    Prism.languages.coffeescript = Prism.languages.extend('javascript', {\n      comment: comment,\n      string: [\n        // Strings are multiline\n        {\n          pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n          greedy: true\n        },\n        {\n          // Strings are multiline\n          pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n          greedy: true,\n          inside: {\n            interpolation: interpolation\n          }\n        }\n      ],\n      keyword:\n        /\\b(?:and|break|by|catch|class|continue|debugger|delete|do|each|else|extend|extends|false|finally|for|if|in|instanceof|is|isnt|let|loop|namespace|new|no|not|null|of|off|on|or|own|return|super|switch|then|this|throw|true|try|typeof|undefined|unless|until|when|while|window|with|yes|yield)\\b/,\n      'class-member': {\n        pattern: /@(?!\\d)\\w+/,\n        alias: 'variable'\n      }\n    })\n    Prism.languages.insertBefore('coffeescript', 'comment', {\n      'multiline-comment': {\n        pattern: /###[\\s\\S]+?###/,\n        alias: 'comment'\n      },\n      // Block regexp can contain comments and interpolation\n      'block-regex': {\n        pattern: /\\/{3}[\\s\\S]*?\\/{3}/,\n        alias: 'regex',\n        inside: {\n          comment: comment,\n          interpolation: interpolation\n        }\n      }\n    })\n    Prism.languages.insertBefore('coffeescript', 'string', {\n      'inline-javascript': {\n        pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n        inside: {\n          delimiter: {\n            pattern: /^`|`$/,\n            alias: 'punctuation'\n          },\n          script: {\n            pattern: /[\\s\\S]+/,\n            alias: 'language-javascript',\n            inside: Prism.languages.javascript\n          }\n        }\n      },\n      // Block strings\n      'multiline-string': [\n        {\n          pattern: /'''[\\s\\S]*?'''/,\n          greedy: true,\n          alias: 'string'\n        },\n        {\n          pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n          greedy: true,\n          alias: 'string',\n          inside: {\n            interpolation: interpolation\n          }\n        }\n      ]\n    })\n    Prism.languages.insertBefore('coffeescript', 'keyword', {\n      // Object property\n      property: /(?!\\d)\\w+(?=\\s*:(?!:))/\n    })\n    delete Prism.languages.coffeescript['template-string']\n    Prism.languages.coffee = Prism.languages.coffeescript\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,iBAAa,cAAc;AAC3B,iBAAa,UAAU,CAAC,QAAQ;AAChC,aAAS,aAAa,OAAO;AAC3B;AAAC,OAAC,SAAUA,QAAO;AAEjB,YAAI,UAAU;AACd,YAAI,gBAAgB;AAAA,UAClB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AACA,QAAAA,OAAM,UAAU,eAAeA,OAAM,UAAU,OAAO,cAAc;AAAA,UAClE;AAAA,UACA,QAAQ;AAAA;AAAA,YAEN;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,SACE;AAAA,UACF,gBAAgB;AAAA,YACd,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,gBAAgB,WAAW;AAAA,UACtD,qBAAqB;AAAA,YACnB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA;AAAA,UAEA,eAAe;AAAA,YACb,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,cACN;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,gBAAgB,UAAU;AAAA,UACrD,qBAAqB;AAAA,YACnB,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQA,OAAM,UAAU;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,oBAAoB;AAAA,YAClB;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,gBAAgB,WAAW;AAAA;AAAA,UAEtD,UAAU;AAAA,QACZ,CAAC;AACD,eAAOA,OAAM,UAAU,aAAa,iBAAiB;AACrD,QAAAA,OAAM,UAAU,SAASA,OAAM,UAAU;AAAA,MAC3C,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}