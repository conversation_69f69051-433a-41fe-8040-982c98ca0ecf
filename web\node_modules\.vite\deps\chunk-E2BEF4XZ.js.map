{"version": 3, "sources": ["../../highlight.js/lib/languages/reasonml.js"], "sourcesContent": ["/*\nLanguage: ReasonML\nDescription: Reason lets you write simple, fast and quality type safe code while leveraging both the JavaScript & OCaml ecosystems.\nWebsite: https://reasonml.github.io\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nCategory: functional\n*/\nfunction reasonml(hljs) {\n  function orReValues(ops) {\n    return ops\n      .map(function(op) {\n        return op\n          .split('')\n          .map(function(char) {\n            return '\\\\' + char;\n          })\n          .join('');\n      })\n      .join('|');\n  }\n\n  const RE_IDENT = '~?[a-z$_][0-9a-zA-Z$_]*';\n  const RE_MODULE_IDENT = '`?[A-Z$_][0-9a-zA-Z$_]*';\n\n  const RE_PARAM_TYPEPARAM = '\\'?[a-z$_][0-9a-z$_]*';\n  const RE_PARAM_TYPE = '\\\\s*:\\\\s*[a-z$_][0-9a-z$_]*(\\\\(\\\\s*(' + RE_PARAM_TYPEPARAM + '\\\\s*(,' + RE_PARAM_TYPEPARAM + '\\\\s*)*)?\\\\))?';\n  const RE_PARAM = RE_IDENT + '(' + RE_PARAM_TYPE + '){0,2}';\n  const RE_OPERATOR = \"(\" + orReValues([\n    '||',\n    '++',\n    '**',\n    '+.',\n    '*',\n    '/',\n    '*.',\n    '/.',\n    '...'\n  ]) + \"|\\\\|>|&&|==|===)\";\n  const RE_OPERATOR_SPACED = \"\\\\s+\" + RE_OPERATOR + \"\\\\s+\";\n\n  const KEYWORDS = {\n    keyword:\n      'and as asr assert begin class constraint do done downto else end exception external ' +\n      'for fun function functor if in include inherit initializer ' +\n      'land lazy let lor lsl lsr lxor match method mod module mutable new nonrec ' +\n      'object of open or private rec sig struct then to try type val virtual when while with',\n    built_in:\n      'array bool bytes char exn|5 float int int32 int64 list lazy_t|5 nativeint|5 ref string unit ',\n    literal:\n      'true false'\n  };\n\n  const RE_NUMBER = '\\\\b(0[xX][a-fA-F0-9_]+[Lln]?|' +\n    '0[oO][0-7_]+[Lln]?|' +\n    '0[bB][01_]+[Lln]?|' +\n    '[0-9][0-9_]*([Lln]|(\\\\.[0-9_]*)?([eE][-+]?[0-9_]+)?)?)';\n\n  const NUMBER_MODE = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      {\n        begin: RE_NUMBER\n      },\n      {\n        begin: '\\\\(-' + RE_NUMBER + '\\\\)'\n      }\n    ]\n  };\n\n  const OPERATOR_MODE = {\n    className: 'operator',\n    relevance: 0,\n    begin: RE_OPERATOR\n  };\n  const LIST_CONTENTS_MODES = [\n    {\n      className: 'identifier',\n      relevance: 0,\n      begin: RE_IDENT\n    },\n    OPERATOR_MODE,\n    NUMBER_MODE\n  ];\n\n  const MODULE_ACCESS_CONTENTS = [\n    hljs.QUOTE_STRING_MODE,\n    OPERATOR_MODE,\n    {\n      className: 'module',\n      begin: \"\\\\b\" + RE_MODULE_IDENT,\n      returnBegin: true,\n      end: \"\\.\",\n      contains: [\n        {\n          className: 'identifier',\n          begin: RE_MODULE_IDENT,\n          relevance: 0\n        }\n      ]\n    }\n  ];\n\n  const PARAMS_CONTENTS = [\n    {\n      className: 'module',\n      begin: \"\\\\b\" + RE_MODULE_IDENT,\n      returnBegin: true,\n      end: \"\\.\",\n      relevance: 0,\n      contains: [\n        {\n          className: 'identifier',\n          begin: RE_MODULE_IDENT,\n          relevance: 0\n        }\n      ]\n    }\n  ];\n\n  const PARAMS_MODE = {\n    begin: RE_IDENT,\n    end: '(,|\\\\n|\\\\))',\n    relevance: 0,\n    contains: [\n      OPERATOR_MODE,\n      {\n        className: 'typing',\n        begin: ':',\n        end: '(,|\\\\n)',\n        returnBegin: true,\n        relevance: 0,\n        contains: PARAMS_CONTENTS\n      }\n    ]\n  };\n\n  const FUNCTION_BLOCK_MODE = {\n    className: 'function',\n    relevance: 0,\n    keywords: KEYWORDS,\n    variants: [\n      {\n        begin: '\\\\s(\\\\(\\\\.?.*?\\\\)|' + RE_IDENT + ')\\\\s*=>',\n        end: '\\\\s*=>',\n        returnBegin: true,\n        relevance: 0,\n        contains: [\n          {\n            className: 'params',\n            variants: [\n              {\n                begin: RE_IDENT\n              },\n              {\n                begin: RE_PARAM\n              },\n              {\n                begin: /\\(\\s*\\)/\n              }\n            ]\n          }\n        ]\n      },\n      {\n        begin: '\\\\s\\\\(\\\\.?[^;\\\\|]*\\\\)\\\\s*=>',\n        end: '\\\\s=>',\n        returnBegin: true,\n        relevance: 0,\n        contains: [\n          {\n            className: 'params',\n            relevance: 0,\n            variants: [ PARAMS_MODE ]\n          }\n        ]\n      },\n      {\n        begin: '\\\\(\\\\.\\\\s' + RE_IDENT + '\\\\)\\\\s*=>'\n      }\n    ]\n  };\n  MODULE_ACCESS_CONTENTS.push(FUNCTION_BLOCK_MODE);\n\n  const CONSTRUCTOR_MODE = {\n    className: 'constructor',\n    begin: RE_MODULE_IDENT + '\\\\(',\n    end: '\\\\)',\n    illegal: '\\\\n',\n    keywords: KEYWORDS,\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      OPERATOR_MODE,\n      {\n        className: 'params',\n        begin: '\\\\b' + RE_IDENT\n      }\n    ]\n  };\n\n  const PATTERN_MATCH_BLOCK_MODE = {\n    className: 'pattern-match',\n    begin: '\\\\|',\n    returnBegin: true,\n    keywords: KEYWORDS,\n    end: '=>',\n    relevance: 0,\n    contains: [\n      CONSTRUCTOR_MODE,\n      OPERATOR_MODE,\n      {\n        relevance: 0,\n        className: 'constructor',\n        begin: RE_MODULE_IDENT\n      }\n    ]\n  };\n\n  const MODULE_ACCESS_MODE = {\n    className: 'module-access',\n    keywords: KEYWORDS,\n    returnBegin: true,\n    variants: [\n      {\n        begin: \"\\\\b(\" + RE_MODULE_IDENT + \"\\\\.)+\" + RE_IDENT\n      },\n      {\n        begin: \"\\\\b(\" + RE_MODULE_IDENT + \"\\\\.)+\\\\(\",\n        end: \"\\\\)\",\n        returnBegin: true,\n        contains: [\n          FUNCTION_BLOCK_MODE,\n          {\n            begin: '\\\\(',\n            end: '\\\\)',\n            skip: true\n          }\n        ].concat(MODULE_ACCESS_CONTENTS)\n      },\n      {\n        begin: \"\\\\b(\" + RE_MODULE_IDENT + \"\\\\.)+\\\\{\",\n        end: /\\}/\n      }\n    ],\n    contains: MODULE_ACCESS_CONTENTS\n  };\n\n  PARAMS_CONTENTS.push(MODULE_ACCESS_MODE);\n\n  return {\n    name: 'ReasonML',\n    aliases: [ 're' ],\n    keywords: KEYWORDS,\n    illegal: '(:-|:=|\\\\$\\\\{|\\\\+=)',\n    contains: [\n      hljs.COMMENT('/\\\\*', '\\\\*/', {\n        illegal: '^(#,\\\\/\\\\/)'\n      }),\n      {\n        className: 'character',\n        begin: '\\'(\\\\\\\\[^\\']+|[^\\'])\\'',\n        illegal: '\\\\n',\n        relevance: 0\n      },\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'literal',\n        begin: '\\\\(\\\\)',\n        relevance: 0\n      },\n      {\n        className: 'literal',\n        begin: '\\\\[\\\\|',\n        end: '\\\\|\\\\]',\n        relevance: 0,\n        contains: LIST_CONTENTS_MODES\n      },\n      {\n        className: 'literal',\n        begin: '\\\\[',\n        end: '\\\\]',\n        relevance: 0,\n        contains: LIST_CONTENTS_MODES\n      },\n      CONSTRUCTOR_MODE,\n      {\n        className: 'operator',\n        begin: RE_OPERATOR_SPACED,\n        illegal: '-->',\n        relevance: 0\n      },\n      NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      PATTERN_MATCH_BLOCK_MODE,\n      FUNCTION_BLOCK_MODE,\n      {\n        className: 'module-def',\n        begin: \"\\\\bmodule\\\\s+\" + RE_IDENT + \"\\\\s+\" + RE_MODULE_IDENT + \"\\\\s+=\\\\s+\\\\{\",\n        end: /\\}/,\n        returnBegin: true,\n        keywords: KEYWORDS,\n        relevance: 0,\n        contains: [\n          {\n            className: 'module',\n            relevance: 0,\n            begin: RE_MODULE_IDENT\n          },\n          {\n            begin: /\\{/,\n            end: /\\}/,\n            skip: true\n          }\n        ].concat(MODULE_ACCESS_CONTENTS)\n      },\n      MODULE_ACCESS_MODE\n    ]\n  };\n}\n\nmodule.exports = reasonml;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,SAAS,MAAM;AACtB,eAAS,WAAW,KAAK;AACvB,eAAO,IACJ,IAAI,SAAS,IAAI;AAChB,iBAAO,GACJ,MAAM,EAAE,EACR,IAAI,SAAS,MAAM;AAClB,mBAAO,OAAO;AAAA,UAChB,CAAC,EACA,KAAK,EAAE;AAAA,QACZ,CAAC,EACA,KAAK,GAAG;AAAA,MACb;AAEA,YAAM,WAAW;AACjB,YAAM,kBAAkB;AAExB,YAAM,qBAAqB;AAC3B,YAAM,gBAAgB,yCAAyC,qBAAqB,WAAW,qBAAqB;AACpH,YAAM,WAAW,WAAW,MAAM,gBAAgB;AAClD,YAAM,cAAc,MAAM,WAAW;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC,IAAI;AACL,YAAM,qBAAqB,SAAS,cAAc;AAElD,YAAM,WAAW;AAAA,QACf,SACE;AAAA,QAIF,UACE;AAAA,QACF,SACE;AAAA,MACJ;AAEA,YAAM,YAAY;AAKlB,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO,SAAS,YAAY;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAEA,YAAM,gBAAgB;AAAA,QACpB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,sBAAsB;AAAA,QAC1B;AAAA,UACE,WAAW;AAAA,UACX,WAAW;AAAA,UACX,OAAO;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,yBAAyB;AAAA,QAC7B,KAAK;AAAA,QACL;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,QAAQ;AAAA,UACf,aAAa;AAAA,UACb,KAAK;AAAA,UACL,UAAU;AAAA,YACR;AAAA,cACE,WAAW;AAAA,cACX,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,kBAAkB;AAAA,QACtB;AAAA,UACE,WAAW;AAAA,UACX,OAAO,QAAQ;AAAA,UACf,aAAa;AAAA,UACb,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,WAAW;AAAA,cACX,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AAAA,QAClB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,aAAa;AAAA,YACb,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAEA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,OAAO,uBAAuB,WAAW;AAAA,YACzC,KAAK;AAAA,YACL,aAAa;AAAA,YACb,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,kBACT;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,kBACT;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,aAAa;AAAA,YACb,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,WAAW;AAAA,gBACX,UAAU,CAAE,WAAY;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO,cAAc,WAAW;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AACA,6BAAuB,KAAK,mBAAmB;AAE/C,YAAM,mBAAmB;AAAA,QACvB,WAAW;AAAA,QACX,OAAO,kBAAkB;AAAA,QACzB,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,QAAQ;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAEA,YAAM,2BAA2B;AAAA,QAC/B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,qBAAqB;AAAA,QACzB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,OAAO,SAAS,kBAAkB,UAAU;AAAA,UAC9C;AAAA,UACA;AAAA,YACE,OAAO,SAAS,kBAAkB;AAAA,YAClC,KAAK;AAAA,YACL,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,UACjC;AAAA,UACA;AAAA,YACE,OAAO,SAAS,kBAAkB;AAAA,YAClC,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ;AAEA,sBAAgB,KAAK,kBAAkB;AAEvC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK,QAAQ,QAAQ,QAAQ;AAAA,YAC3B,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA,UACA,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,kBAAkB,WAAW,SAAS,kBAAkB;AAAA,YAC/D,KAAK;AAAA,YACL,aAAa;AAAA,YACb,UAAU;AAAA,YACV,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,YACF,EAAE,OAAO,sBAAsB;AAAA,UACjC;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}