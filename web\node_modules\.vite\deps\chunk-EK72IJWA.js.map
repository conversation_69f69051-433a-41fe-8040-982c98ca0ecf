{"version": 3, "sources": ["../../refractor/lang/pure.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = pure\npure.displayName = 'pure'\npure.aliases = []\nfunction pure(Prism) {\n  ;(function (Prism) {\n    // https://agraef.github.io/pure-docs/pure.html#lexical-matters\n    Prism.languages.pure = {\n      comment: [\n        {\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n          lookbehind: true\n        },\n        {\n          pattern: /(^|[^\\\\:])\\/\\/.*/,\n          lookbehind: true\n        },\n        /#!.+/\n      ],\n      'inline-lang': {\n        pattern: /%<[\\s\\S]+?%>/,\n        greedy: true,\n        inside: {\n          lang: {\n            pattern: /(^%< *)-\\*-.+?-\\*-/,\n            lookbehind: true,\n            alias: 'comment'\n          },\n          delimiter: {\n            pattern: /^%<.*|%>$/,\n            alias: 'punctuation'\n          }\n        }\n      },\n      string: {\n        pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n        greedy: true\n      },\n      number: {\n        // The look-behind prevents wrong highlighting of the .. operator\n        pattern:\n          /((?:\\.\\.)?)(?:\\b(?:inf|nan)\\b|\\b0x[\\da-f]+|(?:\\b(?:0b)?\\d+(?:\\.\\d+)?|\\B\\.\\d+)(?:e[+-]?\\d+)?L?)/i,\n        lookbehind: true\n      },\n      keyword:\n        /\\b(?:NULL|ans|break|bt|case|catch|cd|clear|const|def|del|dump|else|end|exit|extern|false|force|help|if|infix[lr]?|interface|let|ls|mem|namespace|nonfix|of|otherwise|outfix|override|postfix|prefix|private|public|pwd|quit|run|save|show|stats|then|throw|trace|true|type|underride|using|when|with)\\b/,\n      function:\n        /\\b(?:abs|add_(?:addr|constdef|(?:fundef|interface|macdef|typedef)(?:_at)?|vardef)|all|any|applp?|arity|bigintp?|blob(?:_crc|_size|p)?|boolp?|byte_c?string(?:_pointer)?|byte_(?:matrix|pointer)|calloc|cat|catmap|ceil|char[ps]?|check_ptrtag|chr|clear_sentry|clearsym|closurep?|cmatrixp?|cols?|colcat(?:map)?|colmap|colrev|colvector(?:p|seq)?|complex(?:_float_(?:matrix|pointer)|_matrix(?:_view)?|_pointer|p)?|conj|cookedp?|cst|cstring(?:_(?:dup|list|vector))?|curry3?|cyclen?|del_(?:constdef|fundef|interface|macdef|typedef|vardef)|delete|diag(?:mat)?|dim|dmatrixp?|do|double(?:_matrix(?:_view)?|_pointer|p)?|dowith3?|drop|dropwhile|eval(?:cmd)?|exactp|filter|fix|fixity|flip|float(?:_matrix|_pointer)|floor|fold[lr]1?|frac|free|funp?|functionp?|gcd|get(?:_(?:byte|constdef|double|float|fundef|int(?:64)?|interface(?:_typedef)?|long|macdef|pointer|ptrtag|sentry|short|string|typedef|vardef))?|globsym|hash|head|id|im|imatrixp?|index|inexactp|infp|init|insert|int(?:_matrix(?:_view)?|_pointer|p)?|int64_(?:matrix|pointer)|integerp?|iteraten?|iterwhile|join|keys?|lambdap?|last(?:err(?:pos)?)?|lcd|list[2p]?|listmap|make_ptrtag|malloc|map|matcat|matrixp?|max|member|min|nanp|nargs|nmatrixp?|null|numberp?|ord|pack(?:ed)?|pointer(?:_cast|_tag|_type|p)?|pow|pred|ptrtag|put(?:_(?:byte|double|float|int(?:64)?|long|pointer|short|string))?|rationalp?|re|realp?|realloc|recordp?|redim|reduce(?:_with)?|refp?|repeatn?|reverse|rlistp?|round|rows?|rowcat(?:map)?|rowmap|rowrev|rowvector(?:p|seq)?|same|scan[lr]1?|sentry|sgn|short_(?:matrix|pointer)|slice|smatrixp?|sort|split|str|strcat|stream|stride|string(?:_(?:dup|list|vector)|p)?|subdiag(?:mat)?|submat|subseq2?|substr|succ|supdiag(?:mat)?|symbolp?|tail|take|takewhile|thunkp?|transpose|trunc|tuplep?|typep|ubyte|uint(?:64)?|ulong|uncurry3?|unref|unzip3?|update|ushort|vals?|varp?|vector(?:p|seq)?|void|zip3?|zipwith3?)\\b/,\n      special: {\n        pattern: /\\b__[a-z]+__\\b/i,\n        alias: 'builtin'\n      },\n      // Any combination of operator chars can be an operator\n      // eslint-disable-next-line no-misleading-character-class\n      operator:\n        /(?:[!\"#$%&'*+,\\-.\\/:<=>?@\\\\^`|~\\u00a1-\\u00bf\\u00d7-\\u00f7\\u20d0-\\u2bff]|\\b_+\\b)+|\\b(?:and|div|mod|not|or)\\b/,\n      // FIXME: How can we prevent | and , to be highlighted as operator when they are used alone?\n      punctuation: /[(){}\\[\\];,|]/\n    }\n    var inlineLanguages = [\n      'c',\n      {\n        lang: 'c++',\n        alias: 'cpp'\n      },\n      'fortran'\n    ]\n    var inlineLanguageRe = /%< *-\\*- *<lang>\\d* *-\\*-[\\s\\S]+?%>/.source\n    inlineLanguages.forEach(function (lang) {\n      var alias = lang\n      if (typeof lang !== 'string') {\n        alias = lang.alias\n        lang = lang.lang\n      }\n      if (Prism.languages[alias]) {\n        var o = {}\n        o['inline-lang-' + alias] = {\n          pattern: RegExp(\n            inlineLanguageRe.replace(\n              '<lang>',\n              lang.replace(/([.+*?\\/\\\\(){}\\[\\]])/g, '\\\\$1')\n            ),\n            'i'\n          ),\n          inside: Prism.util.clone(Prism.languages.pure['inline-lang'].inside)\n        }\n        o['inline-lang-' + alias].inside.rest = Prism.util.clone(\n          Prism.languages[alias]\n        )\n        Prism.languages.insertBefore('pure', 'inline-lang', o)\n      }\n    }) // C is the default inline language\n    if (Prism.languages.c) {\n      Prism.languages.pure['inline-lang'].inside.rest = Prism.util.clone(\n        Prism.languages.c\n      )\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AAEjB,QAAAA,OAAM,UAAU,OAAO;AAAA,UACrB,SAAS;AAAA,YACP;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,eAAe;AAAA,YACb,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,cACA,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA;AAAA,YAEN,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,SACE;AAAA,UACF,UACE;AAAA,UACF,SAAS;AAAA,YACP,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA;AAAA;AAAA,UAGA,UACE;AAAA;AAAA,UAEF,aAAa;AAAA,QACf;AACA,YAAI,kBAAkB;AAAA,UACpB;AAAA,UACA;AAAA,YACE,MAAM;AAAA,YACN,OAAO;AAAA,UACT;AAAA,UACA;AAAA,QACF;AACA,YAAI,mBAAmB,sCAAsC;AAC7D,wBAAgB,QAAQ,SAAU,MAAM;AACtC,cAAI,QAAQ;AACZ,cAAI,OAAO,SAAS,UAAU;AAC5B,oBAAQ,KAAK;AACb,mBAAO,KAAK;AAAA,UACd;AACA,cAAIA,OAAM,UAAU,KAAK,GAAG;AAC1B,gBAAI,IAAI,CAAC;AACT,cAAE,iBAAiB,KAAK,IAAI;AAAA,cAC1B,SAAS;AAAA,gBACP,iBAAiB;AAAA,kBACf;AAAA,kBACA,KAAK,QAAQ,yBAAyB,MAAM;AAAA,gBAC9C;AAAA,gBACA;AAAA,cACF;AAAA,cACA,QAAQA,OAAM,KAAK,MAAMA,OAAM,UAAU,KAAK,aAAa,EAAE,MAAM;AAAA,YACrE;AACA,cAAE,iBAAiB,KAAK,EAAE,OAAO,OAAOA,OAAM,KAAK;AAAA,cACjDA,OAAM,UAAU,KAAK;AAAA,YACvB;AACA,YAAAA,OAAM,UAAU,aAAa,QAAQ,eAAe,CAAC;AAAA,UACvD;AAAA,QACF,CAAC;AACD,YAAIA,OAAM,UAAU,GAAG;AACrB,UAAAA,OAAM,UAAU,KAAK,aAAa,EAAE,OAAO,OAAOA,OAAM,KAAK;AAAA,YAC3DA,OAAM,UAAU;AAAA,UAClB;AAAA,QACF;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}