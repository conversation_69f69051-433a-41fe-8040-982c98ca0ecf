{"version": 3, "sources": ["../../refractor/lang/dataweave.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = dataweave\ndataweave.displayName = 'dataweave'\ndataweave.aliases = []\nfunction dataweave(Prism) {\n  ;(function (Prism) {\n    Prism.languages.dataweave = {\n      url: /\\b[A-Za-z]+:\\/\\/[\\w/:.?=&-]+|\\burn:[\\w:.?=&-]+/,\n      property: {\n        pattern: /(?:\\b\\w+#)?(?:\"(?:\\\\.|[^\\\\\"\\r\\n])*\"|\\b\\w+)(?=\\s*[:@])/,\n        greedy: true\n      },\n      string: {\n        pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n        greedy: true\n      },\n      'mime-type':\n        /\\b(?:application|audio|image|multipart|text|video)\\/[\\w+-]+/,\n      date: {\n        pattern: /\\|[\\w:+-]+\\|/,\n        greedy: true\n      },\n      comment: [\n        {\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: /(^|[^\\\\:])\\/\\/.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      regex: {\n        pattern: /\\/(?:[^\\\\\\/\\r\\n]|\\\\[^\\r\\n])+\\//,\n        greedy: true\n      },\n      keyword:\n        /\\b(?:and|as|at|case|do|else|fun|if|input|is|match|not|ns|null|or|output|type|unless|update|using|var)\\b/,\n      function: /\\b[A-Z_]\\w*(?=\\s*\\()/i,\n      number: /-?\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?\\b/i,\n      punctuation: /[{}[\\];(),.:@]/,\n      operator: /<<|>>|->|[<>~=]=?|!=|--?-?|\\+\\+?|!|\\?/,\n      boolean: /\\b(?:false|true)\\b/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,cAAU,cAAc;AACxB,cAAU,UAAU,CAAC;AACrB,aAAS,UAAU,OAAO;AACxB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,YAAY;AAAA,UAC1B,KAAK;AAAA,UACL,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,aACE;AAAA,UACF,MAAM;AAAA,YACJ,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,OAAO;AAAA,YACL,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,SACE;AAAA,UACF,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,aAAa;AAAA,UACb,UAAU;AAAA,UACV,SAAS;AAAA,QACX;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}