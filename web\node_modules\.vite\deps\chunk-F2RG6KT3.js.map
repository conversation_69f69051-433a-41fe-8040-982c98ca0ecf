{"version": 3, "sources": ["../../refractor/lang/protobuf.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = protobuf\nprotobuf.displayName = 'protobuf'\nprotobuf.aliases = []\nfunction protobuf(Prism) {\n  ;(function (Prism) {\n    var builtinTypes =\n      /\\b(?:bool|bytes|double|s?fixed(?:32|64)|float|[su]?int(?:32|64)|string)\\b/\n    Prism.languages.protobuf = Prism.languages.extend('clike', {\n      'class-name': [\n        {\n          pattern:\n            /(\\b(?:enum|extend|message|service)\\s+)[A-Za-z_]\\w*(?=\\s*\\{)/,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /(\\b(?:rpc\\s+\\w+|returns)\\s*\\(\\s*(?:stream\\s+)?)\\.?[A-Za-z_]\\w*(?:\\.[A-Za-z_]\\w*)*(?=\\s*\\))/,\n          lookbehind: true\n        }\n      ],\n      keyword:\n        /\\b(?:enum|extend|extensions|import|message|oneof|option|optional|package|public|repeated|required|reserved|returns|rpc(?=\\s+\\w)|service|stream|syntax|to)\\b(?!\\s*=\\s*\\d)/,\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i\n    })\n    Prism.languages.insertBefore('protobuf', 'operator', {\n      map: {\n        pattern: /\\bmap<\\s*[\\w.]+\\s*,\\s*[\\w.]+\\s*>(?=\\s+[a-z_]\\w*\\s*[=;])/i,\n        alias: 'class-name',\n        inside: {\n          punctuation: /[<>.,]/,\n          builtin: builtinTypes\n        }\n      },\n      builtin: builtinTypes,\n      'positional-class-name': {\n        pattern: /(?:\\b|\\B\\.)[a-z_]\\w*(?:\\.[a-z_]\\w*)*(?=\\s+[a-z_]\\w*\\s*[=;])/i,\n        alias: 'class-name',\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      annotation: {\n        pattern: /(\\[\\s*)[a-z_]\\w*(?=\\s*=)/i,\n        lookbehind: true\n      }\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC;AACpB,aAAS,SAAS,OAAO;AACvB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,eACF;AACF,QAAAA,OAAM,UAAU,WAAWA,OAAM,UAAU,OAAO,SAAS;AAAA,UACzD,cAAc;AAAA,YACZ;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,SACE;AAAA,UACF,UAAU;AAAA,QACZ,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,YAAY,YAAY;AAAA,UACnD,KAAK;AAAA,YACH,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,aAAa;AAAA,cACb,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA,SAAS;AAAA,UACT,yBAAyB;AAAA,YACvB,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,YAAY;AAAA,YACV,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}