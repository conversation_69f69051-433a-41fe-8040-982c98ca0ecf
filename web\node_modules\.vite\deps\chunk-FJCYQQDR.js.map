{"version": 3, "sources": ["../../highlight.js/lib/languages/cpp.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: C++\nCategory: common, system\nWebsite: https://isocpp.org\n*/\n\n/** @type LanguageFn */\nfunction cpp(hljs) {\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  const C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', {\n    contains: [\n      {\n        begin: /\\\\\\n/\n      }\n    ]\n  });\n  const DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  const NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  const TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  const FUNCTION_TYPE_RE = '(' +\n    DECLTYPE_AUTO_RE + '|' +\n    optional(NAMESPACE_RE) +\n    '[a-zA-Z_]\\\\w*' + optional(TEMPLATE_ARGUMENT_RE) +\n  ')';\n  const CPP_PRIMITIVE_TYPES = {\n    className: 'keyword',\n    begin: '\\\\b[a-z\\\\d_]*_t\\\\b'\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  const CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      {\n        begin: '(u8?|U|L)?\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + \"|.)\",\n        end: '\\'',\n        illegal: '.'\n      },\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n        end: /\\)([^()\\\\ ]{0,16})\"/\n      })\n    ]\n  };\n\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      {\n        begin: '\\\\b(0b[01\\']+)'\n      },\n      {\n        begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)'\n      },\n      {\n        begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)'\n      }\n    ],\n    relevance: 0\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: {\n      'meta-keyword':\n        'if else elif endif define undef warning error line ' +\n        'pragma _Pragma ifdef ifndef include'\n    },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      hljs.inherit(STRINGS, {\n        className: 'meta-string'\n      }),\n      {\n        className: 'meta-string',\n        begin: /<.*?>/\n      },\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  const TITLE_MODE = {\n    className: 'title',\n    begin: optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  const FUNCTION_TITLE = optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n\n  const COMMON_CPP_HINTS = [\n    'asin',\n    'atan2',\n    'atan',\n    'calloc',\n    'ceil',\n    'cosh',\n    'cos',\n    'exit',\n    'exp',\n    'fabs',\n    'floor',\n    'fmod',\n    'fprintf',\n    'fputs',\n    'free',\n    'frexp',\n    'auto_ptr',\n    'deque',\n    'list',\n    'queue',\n    'stack',\n    'vector',\n    'map',\n    'set',\n    'pair',\n    'bitset',\n    'multiset',\n    'multimap',\n    'unordered_set',\n    'fscanf',\n    'future',\n    'isalnum',\n    'isalpha',\n    'iscntrl',\n    'isdigit',\n    'isgraph',\n    'islower',\n    'isprint',\n    'ispunct',\n    'isspace',\n    'isupper',\n    'isxdigit',\n    'tolower',\n    'toupper',\n    'labs',\n    'ldexp',\n    'log10',\n    'log',\n    'malloc',\n    'realloc',\n    'memchr',\n    'memcmp',\n    'memcpy',\n    'memset',\n    'modf',\n    'pow',\n    'printf',\n    'putchar',\n    'puts',\n    'scanf',\n    'sinh',\n    'sin',\n    'snprintf',\n    'sprintf',\n    'sqrt',\n    'sscanf',\n    'strcat',\n    'strchr',\n    'strcmp',\n    'strcpy',\n    'strcspn',\n    'strlen',\n    'strncat',\n    'strncmp',\n    'strncpy',\n    'strpbrk',\n    'strrchr',\n    'strspn',\n    'strstr',\n    'tanh',\n    'tan',\n    'unordered_map',\n    'unordered_multiset',\n    'unordered_multimap',\n    'priority_queue',\n    'make_pair',\n    'array',\n    'shared_ptr',\n    'abort',\n    'terminate',\n    'abs',\n    'acos',\n    'vfprintf',\n    'vprintf',\n    'vsprintf',\n    'endl',\n    'initializer_list',\n    'unique_ptr',\n    'complex',\n    'imaginary',\n    'std',\n    'string',\n    'wstring',\n    'cin',\n    'cout',\n    'cerr',\n    'clog',\n    'stdin',\n    'stdout',\n    'stderr',\n    'stringstream',\n    'istringstream',\n    'ostringstream'\n  ];\n\n  const CPP_KEYWORDS = {\n    keyword: 'int float while private char char8_t char16_t char32_t catch import module export virtual operator sizeof ' +\n      'dynamic_cast|10 typedef const_cast|10 const for static_cast|10 union namespace ' +\n      'unsigned long volatile static protected bool template mutable if public friend ' +\n      'do goto auto void enum else break extern using asm case typeid wchar_t ' +\n      'short reinterpret_cast|10 default double register explicit signed typename try this ' +\n      'switch continue inline delete alignas alignof constexpr consteval constinit decltype ' +\n      'concept co_await co_return co_yield requires ' +\n      'noexcept static_assert thread_local restrict final override ' +\n      'atomic_bool atomic_char atomic_schar ' +\n      'atomic_uchar atomic_short atomic_ushort atomic_int atomic_uint atomic_long atomic_ulong atomic_llong ' +\n      'atomic_ullong new throw return ' +\n      'and and_eq bitand bitor compl not not_eq or or_eq xor xor_eq',\n    built_in: '_Bool _Complex _Imaginary',\n    _relevance_hints: COMMON_CPP_HINTS,\n    literal: 'true false nullptr NULL'\n  };\n\n  const FUNCTION_DISPATCH = {\n    className: \"function.dispatch\",\n    relevance: 0,\n    keywords: CPP_KEYWORDS,\n    begin: concat(\n      /\\b/,\n      /(?!decltype)/,\n      /(?!if)/,\n      /(?!for)/,\n      /(?!while)/,\n      hljs.IDENT_RE,\n      lookahead(/\\s*\\(/))\n  };\n\n  const EXPRESSION_CONTAINS = [\n    FUNCTION_DISPATCH,\n    PREPROCESSOR,\n    CPP_PRIMITIVE_TYPES,\n    C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    NUMBERS,\n    STRINGS\n  ];\n\n\n  const EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [\n      {\n        begin: /=/,\n        end: /;/\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/\n      },\n      {\n        beginKeywords: 'new throw return else',\n        end: /;/\n      }\n    ],\n    keywords: CPP_KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        contains: EXPRESSION_CONTAINS.concat([ 'self' ]),\n        relevance: 0\n      }\n    ]),\n    relevance: 0\n  };\n\n  const FUNCTION_DECLARATION = {\n    className: 'function',\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: CPP_KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [\n      { // to prevent it from being confused as the function title\n        begin: DECLTYPE_AUTO_RE,\n        keywords: CPP_KEYWORDS,\n        relevance: 0\n      },\n      {\n        begin: FUNCTION_TITLE,\n        returnBegin: true,\n        contains: [ TITLE_MODE ],\n        relevance: 0\n      },\n      // needed because we do not have look-behind on the below rule\n      // to prevent it from grabbing the final : in a :: pair\n      {\n        begin: /::/,\n        relevance: 0\n      },\n      // initializers\n      {\n        begin: /:/,\n        endsWithParent: true,\n        contains: [\n          STRINGS,\n          NUMBERS\n        ]\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        relevance: 0,\n        contains: [\n          C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          STRINGS,\n          NUMBERS,\n          CPP_PRIMITIVE_TYPES,\n          // Count matching parentheses.\n          {\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: CPP_KEYWORDS,\n            relevance: 0,\n            contains: [\n              'self',\n              C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRINGS,\n              NUMBERS,\n              CPP_PRIMITIVE_TYPES\n            ]\n          }\n        ]\n      },\n      CPP_PRIMITIVE_TYPES,\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      PREPROCESSOR\n    ]\n  };\n\n  return {\n    name: 'C++',\n    aliases: [\n      'cc',\n      'c++',\n      'h++',\n      'hpp',\n      'hh',\n      'hxx',\n      'cxx'\n    ],\n    keywords: CPP_KEYWORDS,\n    illegal: '</',\n    classNameAliases: {\n      \"function.dispatch\": \"built_in\"\n    },\n    contains: [].concat(\n      EXPRESSION_CONTEXT,\n      FUNCTION_DECLARATION,\n      FUNCTION_DISPATCH,\n      EXPRESSION_CONTAINS,\n      [\n        PREPROCESSOR,\n        { // containers: ie, `vector <int> rooms (9);`\n          begin: '\\\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array)\\\\s*<',\n          end: '>',\n          keywords: CPP_KEYWORDS,\n          contains: [\n            'self',\n            CPP_PRIMITIVE_TYPES\n          ]\n        },\n        {\n          begin: hljs.IDENT_RE + '::',\n          keywords: CPP_KEYWORDS\n        },\n        {\n          className: 'class',\n          beginKeywords: 'enum class struct union',\n          end: /[{;:<>=]/,\n          contains: [\n            {\n              beginKeywords: \"final class struct\"\n            },\n            hljs.TITLE_MODE\n          ]\n        }\n      ]),\n    exports: {\n      preprocessor: PREPROCESSOR,\n      strings: STRINGS,\n      keywords: CPP_KEYWORDS\n    }\n  };\n}\n\nmodule.exports = cpp;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,IAAI;AACrB,aAAO,OAAO,OAAO,IAAI,GAAG;AAAA,IAC9B;AAMA,aAAS,SAAS,IAAI;AACpB,aAAO,OAAO,KAAK,IAAI,IAAI;AAAA,IAC7B;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AASA,aAAS,IAAI,MAAM;AAIjB,YAAM,sBAAsB,KAAK,QAAQ,MAAM,KAAK;AAAA,QAClD,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,mBAAmB;AACzB,YAAM,eAAe;AACrB,YAAM,uBAAuB;AAC7B,YAAM,mBAAmB,MACvB,mBAAmB,MACnB,SAAS,YAAY,IACrB,kBAAkB,SAAS,oBAAoB,IACjD;AACA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAIA,YAAM,oBAAoB;AAC1B,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAE,KAAK,gBAAiB;AAAA,UACpC;AAAA,UACA;AAAA,YACE,OAAO,iBAAkB,oBAAoB;AAAA,YAC7C,KAAK;AAAA,YACL,SAAS;AAAA,UACX;AAAA,UACA,KAAK,kBAAkB;AAAA,YACrB,OAAO;AAAA,YACP,KAAK;AAAA,UACP,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,gBACE;AAAA,QAEJ;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA,KAAK,QAAQ,SAAS;AAAA,YACpB,WAAW;AAAA,UACb,CAAC;AAAA,UACD;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAEA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO,SAAS,YAAY,IAAI,KAAK;AAAA,QACrC,WAAW;AAAA,MACb;AAEA,YAAM,iBAAiB,SAAS,YAAY,IAAI,KAAK,WAAW;AAEhE,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,eAAe;AAAA,QACnB,SAAS;AAAA,QAYT,UAAU;AAAA,QACV,kBAAkB;AAAA,QAClB,SAAS;AAAA,MACX;AAEA,YAAM,oBAAoB;AAAA,QACxB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,QACV,OAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,UAAU,OAAO;AAAA,QAAC;AAAA,MACtB;AAEA,YAAM,sBAAsB;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAGA,YAAM,qBAAqB;AAAA;AAAA;AAAA;AAAA,QAIzB,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,UAAU,oBAAoB,OAAO;AAAA,UACnC;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU,oBAAoB,OAAO,CAAE,MAAO,CAAC;AAAA,YAC/C,WAAW;AAAA,UACb;AAAA,QACF,CAAC;AAAA,QACD,WAAW;AAAA,MACb;AAEA,YAAM,uBAAuB;AAAA,QAC3B,WAAW;AAAA,QACX,OAAO,MAAM,mBAAmB,iBAAiB;AAAA,QACjD,aAAa;AAAA,QACb,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,aAAa;AAAA,YACb,UAAU,CAAE,UAAW;AAAA,YACvB,WAAW;AAAA,UACb;AAAA;AAAA;AAAA,UAGA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,gBAAgB;AAAA,YAChB,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,cACA,KAAK;AAAA,cACL;AAAA,cACA;AAAA,cACA;AAAA;AAAA,cAEA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,kBACA;AAAA,kBACA,KAAK;AAAA,kBACL;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,kBAAkB;AAAA,UAChB,qBAAqB;AAAA,QACvB;AAAA,QACA,UAAU,CAAC,EAAE;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE;AAAA,YACA;AAAA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU;AAAA,cACV,UAAU;AAAA,gBACR;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,OAAO,KAAK,WAAW;AAAA,cACvB,UAAU;AAAA,YACZ;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,eAAe;AAAA,cACf,KAAK;AAAA,cACL,UAAU;AAAA,gBACR;AAAA,kBACE,eAAe;AAAA,gBACjB;AAAA,gBACA,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,QAAC;AAAA,QACH,SAAS;AAAA,UACP,cAAc;AAAA,UACd,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}