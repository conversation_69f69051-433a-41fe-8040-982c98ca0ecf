{"version": 3, "sources": ["../../highlight.js/lib/languages/puppet.js"], "sourcesContent": ["/*\nLanguage: Puppet\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://puppet.com/docs\nCategory: config\n*/\n\nfunction puppet(hljs) {\n  const PUPPET_KEYWORDS = {\n    keyword:\n    /* language keywords */\n      'and case default else elsif false if in import enherits node or true undef unless main settings $string ',\n    literal:\n    /* metaparameters */\n      'alias audit before loglevel noop require subscribe tag ' +\n      /* normal attributes */\n      'owner ensure group mode name|0 changes context force incl lens load_path onlyif provider returns root show_diff type_check ' +\n      'en_address ip_address realname command environment hour monute month monthday special target weekday ' +\n      'creates cwd ogoutput refresh refreshonly tries try_sleep umask backup checksum content ctime force ignore ' +\n      'links mtime purge recurse recurselimit replace selinux_ignore_defaults selrange selrole seltype seluser source ' +\n      'souirce_permissions sourceselect validate_cmd validate_replacement allowdupe attribute_membership auth_membership forcelocal gid ' +\n      'ia_load_module members system host_aliases ip allowed_trunk_vlans description device_url duplex encapsulation etherchannel ' +\n      'native_vlan speed principals allow_root auth_class auth_type authenticate_user k_of_n mechanisms rule session_owner shared options ' +\n      'device fstype enable hasrestart directory present absent link atboot blockdevice device dump pass remounts poller_tag use ' +\n      'message withpath adminfile allow_virtual allowcdrom category configfiles flavor install_options instance package_settings platform ' +\n      'responsefile status uninstall_options vendor unless_system_user unless_uid binary control flags hasstatus manifest pattern restart running ' +\n      'start stop allowdupe auths expiry gid groups home iterations key_membership keys managehome membership password password_max_age ' +\n      'password_min_age profile_membership profiles project purge_ssh_keys role_membership roles salt shell uid baseurl cost descr enabled ' +\n      'enablegroups exclude failovermethod gpgcheck gpgkey http_caching include includepkgs keepalive metadata_expire metalink mirrorlist ' +\n      'priority protect proxy proxy_password proxy_username repo_gpgcheck s3_enabled skip_if_unavailable sslcacert sslclientcert sslclientkey ' +\n      'sslverify mounted',\n    built_in:\n    /* core facts */\n      'architecture augeasversion blockdevices boardmanufacturer boardproductname boardserialnumber cfkey dhcp_servers ' +\n      'domain ec2_ ec2_userdata facterversion filesystems ldom fqdn gid hardwareisa hardwaremodel hostname id|0 interfaces ' +\n      'ipaddress ipaddress_ ipaddress6 ipaddress6_ iphostnumber is_virtual kernel kernelmajversion kernelrelease kernelversion ' +\n      'kernelrelease kernelversion lsbdistcodename lsbdistdescription lsbdistid lsbdistrelease lsbmajdistrelease lsbminordistrelease ' +\n      'lsbrelease macaddress macaddress_ macosx_buildversion macosx_productname macosx_productversion macosx_productverson_major ' +\n      'macosx_productversion_minor manufacturer memoryfree memorysize netmask metmask_ network_ operatingsystem operatingsystemmajrelease ' +\n      'operatingsystemrelease osfamily partitions path physicalprocessorcount processor processorcount productname ps puppetversion ' +\n      'rubysitedir rubyversion selinux selinux_config_mode selinux_config_policy selinux_current_mode selinux_current_mode selinux_enforced ' +\n      'selinux_policyversion serialnumber sp_ sshdsakey sshecdsakey sshrsakey swapencrypted swapfree swapsize timezone type uniqueid uptime ' +\n      'uptime_days uptime_hours uptime_seconds uuid virtual vlans xendomains zfs_version zonenae zones zpool_version'\n  };\n\n  const COMMENT = hljs.COMMENT('#', '$');\n\n  const IDENT_RE = '([A-Za-z_]|::)(\\\\w|::)*';\n\n  const TITLE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: IDENT_RE\n  });\n\n  const VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$' + IDENT_RE\n  };\n\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VARIABLE\n    ],\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      }\n    ]\n  };\n\n  return {\n    name: 'Puppet',\n    aliases: [ 'pp' ],\n    contains: [\n      COMMENT,\n      VARIABLE,\n      STRING,\n      {\n        beginKeywords: 'class',\n        end: '\\\\{|;',\n        illegal: /=/,\n        contains: [\n          TITLE,\n          COMMENT\n        ]\n      },\n      {\n        beginKeywords: 'define',\n        end: /\\{/,\n        contains: [\n          {\n            className: 'section',\n            begin: hljs.IDENT_RE,\n            endsParent: true\n          }\n        ]\n      },\n      {\n        begin: hljs.IDENT_RE + '\\\\s+\\\\{',\n        returnBegin: true,\n        end: /\\S/,\n        contains: [\n          {\n            className: 'keyword',\n            begin: hljs.IDENT_RE\n          },\n          {\n            begin: /\\{/,\n            end: /\\}/,\n            keywords: PUPPET_KEYWORDS,\n            relevance: 0,\n            contains: [\n              STRING,\n              COMMENT,\n              {\n                begin: '[a-zA-Z_]+\\\\s*=>',\n                returnBegin: true,\n                end: '=>',\n                contains: [\n                  {\n                    className: 'attr',\n                    begin: hljs.IDENT_RE\n                  }\n                ]\n              },\n              {\n                className: 'number',\n                begin: '(\\\\b0[0-7_]+)|(\\\\b0x[0-9a-fA-F_]+)|(\\\\b[1-9][0-9_]*(\\\\.[0-9_]+)?)|[0_]\\\\b',\n                relevance: 0\n              },\n              VARIABLE\n            ]\n          }\n        ],\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = puppet;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,OAAO,MAAM;AACpB,YAAM,kBAAkB;AAAA,QACtB;AAAA;AAAA,UAEE;AAAA;AAAA,QACF;AAAA;AAAA,UAEE;AAAA;AAAA,QAiBF;AAAA;AAAA,UAEE;AAAA;AAAA,MAUJ;AAEA,YAAM,UAAU,KAAK,QAAQ,KAAK,GAAG;AAErC,YAAM,WAAW;AAEjB,YAAM,QAAQ,KAAK,QAAQ,KAAK,YAAY;AAAA,QAC1C,OAAO;AAAA,MACT,CAAC;AAED,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO,QAAQ;AAAA,MACjB;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO,KAAK;AAAA,gBACZ,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO,KAAK,WAAW;AAAA,YACvB,aAAa;AAAA,YACb,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO,KAAK;AAAA,cACd;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,kBACA;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,aAAa;AAAA,oBACb,KAAK;AAAA,oBACL,UAAU;AAAA,sBACR;AAAA,wBACE,WAAW;AAAA,wBACX,OAAO,KAAK;AAAA,sBACd;AAAA,oBACF;AAAA,kBACF;AAAA,kBACA;AAAA,oBACE,WAAW;AAAA,oBACX,OAAO;AAAA,oBACP,WAAW;AAAA,kBACb;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}