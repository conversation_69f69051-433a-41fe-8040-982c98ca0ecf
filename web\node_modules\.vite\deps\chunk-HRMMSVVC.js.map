{"version": 3, "sources": ["../../highlight.js/lib/languages/actionscript.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: ActionScript\nAuthor: <PERSON> <<EMAIL>>\nCategory: scripting\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction actionscript(hljs) {\n  const IDENT_RE = /[a-zA-Z_$][a-zA-Z0-9_$]*/;\n  const IDENT_FUNC_RETURN_TYPE_RE = /([*]|[a-zA-Z_$][a-zA-Z0-9_$]*)/;\n\n  const AS3_REST_ARG_MODE = {\n    className: 'rest_arg',\n    begin: /[.]{3}/,\n    end: IDENT_RE,\n    relevance: 10\n  };\n\n  return {\n    name: 'ActionScript',\n    aliases: [ 'as' ],\n    keywords: {\n      keyword: 'as break case catch class const continue default delete do dynamic each ' +\n        'else extends final finally for function get if implements import in include ' +\n        'instanceof interface internal is namespace native new override package private ' +\n        'protected public return set static super switch this throw try typeof use var void ' +\n        'while with',\n      literal: 'true false null undefined'\n    },\n    contains: [\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'package',\n        end: /\\{/,\n        contains: [ hljs.TITLE_MODE ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          { beginKeywords: 'extends implements' },\n          hljs.TITLE_MODE\n        ]\n      },\n      {\n        className: 'meta',\n        beginKeywords: 'import include',\n        end: /;/,\n        keywords: { 'meta-keyword': 'import include' }\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: /[{;]/,\n        excludeEnd: true,\n        illegal: /\\S/,\n        contains: [\n          hljs.TITLE_MODE,\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            contains: [\n              hljs.APOS_STRING_MODE,\n              hljs.QUOTE_STRING_MODE,\n              hljs.C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              AS3_REST_ARG_MODE\n            ]\n          },\n          { begin: concat(/:\\s*/, IDENT_FUNC_RETURN_TYPE_RE) }\n        ]\n      },\n      hljs.METHOD_GUARD\n    ],\n    illegal: /#/\n  };\n}\n\nmodule.exports = actionscript;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAUA,aAAS,aAAa,MAAM;AAC1B,YAAM,WAAW;AACjB,YAAM,4BAA4B;AAElC,YAAM,oBAAoB;AAAA,QACxB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MACb;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,UAAU;AAAA,UACR,SAAS;AAAA,UAKT,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU,CAAE,KAAK,UAAW;AAAA,UAC9B;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,cACR,EAAE,eAAe,qBAAqB;AAAA,cACtC,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU,EAAE,gBAAgB,iBAAiB;AAAA,UAC/C;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,kBACR,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL;AAAA,gBACF;AAAA,cACF;AAAA,cACA,EAAE,OAAO,OAAO,QAAQ,yBAAyB,EAAE;AAAA,YACrD;AAAA,UACF;AAAA,UACA,KAAK;AAAA,QACP;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}