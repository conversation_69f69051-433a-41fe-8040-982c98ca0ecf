{"version": 3, "sources": ["../../refractor/lang/log.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = log\nlog.displayName = 'log'\nlog.aliases = []\nfunction log(Prism) {\n  // This is a language definition for generic log files.\n  // Since there is no one log format, this language definition has to support all formats to some degree.\n  //\n  // Based on https://github.com/MTDL9/vim-log-highlighting\n  Prism.languages.log = {\n    string: {\n      // Single-quoted strings must not be confused with plain text. E.g. Can't isn't Susan's Chris' toy\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?![st] | \\w)(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    exception: {\n      pattern:\n        /(^|[^\\w.])[a-z][\\w.]*(?:Error|Exception):.*(?:(?:\\r\\n?|\\n)[ \\t]*(?:at[ \\t].+|\\.{3}.*|Caused by:.*))+(?:(?:\\r\\n?|\\n)[ \\t]*\\.\\.\\. .*)?/,\n      lookbehind: true,\n      greedy: true,\n      alias: ['javastacktrace', 'language-javastacktrace'],\n      inside: Prism.languages['javastacktrace'] || {\n        keyword: /\\bat\\b/,\n        function: /[a-z_][\\w$]*(?=\\()/,\n        punctuation: /[.:()]/\n      }\n    },\n    level: [\n      {\n        pattern:\n          /\\b(?:ALERT|CRIT|CRITICAL|EMERG|EMERGENCY|ERR|ERROR|FAILURE|FATAL|SEVERE)\\b/,\n        alias: ['error', 'important']\n      },\n      {\n        pattern: /\\b(?:WARN|WARNING|WRN)\\b/,\n        alias: ['warning', 'important']\n      },\n      {\n        pattern: /\\b(?:DISPLAY|INF|INFO|NOTICE|STATUS)\\b/,\n        alias: ['info', 'keyword']\n      },\n      {\n        pattern: /\\b(?:DBG|DEBUG|FINE)\\b/,\n        alias: ['debug', 'keyword']\n      },\n      {\n        pattern: /\\b(?:FINER|FINEST|TRACE|TRC|VERBOSE|VRB)\\b/,\n        alias: ['trace', 'comment']\n      }\n    ],\n    property: {\n      pattern:\n        /((?:^|[\\]|])[ \\t]*)[a-z_](?:[\\w-]|\\b\\/\\b)*(?:[. ]\\(?\\w(?:[\\w-]|\\b\\/\\b)*\\)?)*:(?=\\s)/im,\n      lookbehind: true\n    },\n    separator: {\n      pattern: /(^|[^-+])-{3,}|={3,}|\\*{3,}|- - /m,\n      lookbehind: true,\n      alias: 'comment'\n    },\n    url: /\\b(?:file|ftp|https?):\\/\\/[^\\s|,;'\"]*[^\\s|,;'\">.]/,\n    email: {\n      pattern: /(^|\\s)[-\\w+.]+@[a-z][a-z0-9-]*(?:\\.[a-z][a-z0-9-]*)+(?=\\s)/,\n      lookbehind: true,\n      alias: 'url'\n    },\n    'ip-address': {\n      pattern: /\\b(?:\\d{1,3}(?:\\.\\d{1,3}){3})\\b/,\n      alias: 'constant'\n    },\n    'mac-address': {\n      pattern: /\\b[a-f0-9]{2}(?::[a-f0-9]{2}){5}\\b/i,\n      alias: 'constant'\n    },\n    domain: {\n      pattern:\n        /(^|\\s)[a-z][a-z0-9-]*(?:\\.[a-z][a-z0-9-]*)*\\.[a-z][a-z0-9-]+(?=\\s)/,\n      lookbehind: true,\n      alias: 'constant'\n    },\n    uuid: {\n      pattern:\n        /\\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\\b/i,\n      alias: 'constant'\n    },\n    hash: {\n      pattern: /\\b(?:[a-f0-9]{32}){1,2}\\b/i,\n      alias: 'constant'\n    },\n    'file-path': {\n      pattern:\n        /\\b[a-z]:[\\\\/][^\\s|,;:(){}\\[\\]\"']+|(^|[\\s:\\[\\](>|])\\.{0,2}\\/\\w[^\\s|,;:(){}\\[\\]\"']*/i,\n      lookbehind: true,\n      greedy: true,\n      alias: 'string'\n    },\n    date: {\n      pattern: RegExp(\n        /\\b\\d{4}[-/]\\d{2}[-/]\\d{2}(?:T(?=\\d{1,2}:)|(?=\\s\\d{1,2}:))/.source +\n          '|' +\n          /\\b\\d{1,4}[-/ ](?:\\d{1,2}|Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)[-/ ]\\d{2,4}T?\\b/\n            .source +\n          '|' +\n          /\\b(?:(?:Fri|Mon|Sat|Sun|Thu|Tue|Wed)(?:\\s{1,2}(?:Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep))?|Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)\\s{1,2}\\d{1,2}\\b/\n            .source,\n        'i'\n      ),\n      alias: 'number'\n    },\n    time: {\n      pattern:\n        /\\b\\d{1,2}:\\d{1,2}:\\d{1,2}(?:[.,:]\\d+)?(?:\\s?[+-]\\d{2}:?\\d{2}|Z)?\\b/,\n      alias: 'number'\n    },\n    boolean: /\\b(?:false|null|true)\\b/i,\n    number: {\n      pattern:\n        /(^|[^.\\w])(?:0x[a-f0-9]+|0o[0-7]+|0b[01]+|v?\\d[\\da-f]*(?:\\.\\d+)*(?:e[+-]?\\d+)?[a-z]{0,3}\\b)\\b(?!\\.\\w)/i,\n      lookbehind: true\n    },\n    operator: /[;:?<=>~/@!$%&+\\-|^(){}*#]/,\n    punctuation: /[\\[\\].,]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAKlB,YAAM,UAAU,MAAM;AAAA,QACpB,QAAQ;AAAA;AAAA,UAEN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,WAAW;AAAA,UACT,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,OAAO,CAAC,kBAAkB,yBAAyB;AAAA,UACnD,QAAQ,MAAM,UAAU,gBAAgB,KAAK;AAAA,YAC3C,SAAS;AAAA,YACT,UAAU;AAAA,YACV,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL;AAAA,YACE,SACE;AAAA,YACF,OAAO,CAAC,SAAS,WAAW;AAAA,UAC9B;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO,CAAC,WAAW,WAAW;AAAA,UAChC;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO,CAAC,QAAQ,SAAS;AAAA,UAC3B;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO,CAAC,SAAS,SAAS;AAAA,UAC5B;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO,CAAC,SAAS,SAAS;AAAA,UAC5B;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,KAAK;AAAA,QACL,OAAO;AAAA,UACL,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,eAAe;AAAA,UACb,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,MAAM;AAAA,UACJ,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,aAAa;AAAA,UACX,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,MAAM;AAAA,UACJ,SAAS;AAAA,YACP,4DAA4D,SAC1D,MACA,4FACG,SACH,MACA,uKACG;AAAA,YACL;AAAA,UACF;AAAA,UACA,OAAO;AAAA,QACT;AAAA,QACA,MAAM;AAAA,UACJ,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}