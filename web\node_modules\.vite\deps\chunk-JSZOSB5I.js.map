{"version": 3, "sources": ["../../highlight.js/lib/languages/dust.js"], "sourcesContent": ["/*\nLanguage: Dust\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Matcher for dust.js templates.\nWebsite: https://www.dustjs.com\nCategory: template\n*/\n\n/** @type LanguageFn */\nfunction dust(hljs) {\n  const EXPRESSION_KEYWORDS = 'if eq ne lt lte gt gte select default math sep';\n  return {\n    name: 'Dust',\n    aliases: ['dst'],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [\n      {\n        className: 'template-tag',\n        begin: /\\{[#\\/]/,\n        end: /\\}/,\n        illegal: /;/,\n        contains: [{\n          className: 'name',\n          begin: /[a-zA-Z\\.-]+/,\n          starts: {\n            endsWithParent: true,\n            relevance: 0,\n            contains: [hljs.QUOTE_STRING_MODE]\n          }\n        }]\n      },\n      {\n        className: 'template-variable',\n        begin: /\\{/,\n        end: /\\}/,\n        illegal: /;/,\n        keywords: EXPRESSION_KEYWORDS\n      }\n    ]\n  };\n}\n\nmodule.exports = dust;\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,aAAS,KAAK,MAAM;AAClB,YAAM,sBAAsB;AAC5B,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,KAAK;AAAA,QACf,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAC;AAAA,cACT,WAAW;AAAA,cACX,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,gBAAgB;AAAA,gBAChB,WAAW;AAAA,gBACX,UAAU,CAAC,KAAK,iBAAiB;AAAA,cACnC;AAAA,YACF,CAAC;AAAA,UACH;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}