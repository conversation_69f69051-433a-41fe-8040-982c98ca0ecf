{"version": 3, "sources": ["../../refractor/lang/parser.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = parser\nparser.displayName = 'parser'\nparser.aliases = []\nfunction parser(Prism) {\n  ;(function (Prism) {\n    var parser = (Prism.languages.parser = Prism.languages.extend('markup', {\n      keyword: {\n        pattern:\n          /(^|[^^])(?:\\^(?:case|eval|for|if|switch|throw)\\b|@(?:BASE|CLASS|GET(?:_DEFAULT)?|OPTIONS|SET_DEFAULT|USE)\\b)/,\n        lookbehind: true\n      },\n      variable: {\n        pattern: /(^|[^^])\\B\\$(?:\\w+|(?=[.{]))(?:(?:\\.|::?)\\w+)*(?:\\.|::?)?/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\.|:+/\n        }\n      },\n      function: {\n        pattern: /(^|[^^])\\B[@^]\\w+(?:(?:\\.|::?)\\w+)*(?:\\.|::?)?/,\n        lookbehind: true,\n        inside: {\n          keyword: {\n            pattern: /(^@)(?:GET_|SET_)/,\n            lookbehind: true\n          },\n          punctuation: /\\.|:+/\n        }\n      },\n      escape: {\n        pattern: /\\^(?:[$^;@()\\[\\]{}\"':]|#[a-f\\d]*)/i,\n        alias: 'builtin'\n      },\n      punctuation: /[\\[\\](){};]/\n    }))\n    parser = Prism.languages.insertBefore('parser', 'keyword', {\n      'parser-comment': {\n        pattern: /(\\s)#.*/,\n        lookbehind: true,\n        alias: 'comment'\n      },\n      expression: {\n        // Allow for 3 levels of depth\n        pattern: /(^|[^^])\\((?:[^()]|\\((?:[^()]|\\((?:[^()])*\\))*\\))*\\)/,\n        greedy: true,\n        lookbehind: true,\n        inside: {\n          string: {\n            pattern: /(^|[^^])([\"'])(?:(?!\\2)[^^]|\\^[\\s\\S])*\\2/,\n            lookbehind: true\n          },\n          keyword: parser.keyword,\n          variable: parser.variable,\n          function: parser.function,\n          boolean: /\\b(?:false|true)\\b/,\n          number: /\\b(?:0x[a-f\\d]+|\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?)\\b/i,\n          escape: parser.escape,\n          operator:\n            /[~+*\\/\\\\%]|!(?:\\|\\|?|=)?|&&?|\\|\\|?|==|<[<=]?|>[>=]?|-[fd]?|\\b(?:def|eq|ge|gt|in|is|le|lt|ne)\\b/,\n          punctuation: parser.punctuation\n        }\n      }\n    })\n    Prism.languages.insertBefore(\n      'inside',\n      'punctuation',\n      {\n        expression: parser.expression,\n        keyword: parser.keyword,\n        variable: parser.variable,\n        function: parser.function,\n        escape: parser.escape,\n        'parser-punctuation': {\n          pattern: parser.punctuation,\n          alias: 'punctuation'\n        }\n      },\n      parser['tag'].inside['attr-value']\n    )\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAIC,UAAUD,OAAM,UAAU,SAASA,OAAM,UAAU,OAAO,UAAU;AAAA,UACtE,SAAS;AAAA,YACP,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,aAAa;AAAA,QACf,CAAC;AACD,QAAAC,UAASD,OAAM,UAAU,aAAa,UAAU,WAAW;AAAA,UACzD,kBAAkB;AAAA,YAChB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA;AAAA,YAEV,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA,SAASC,QAAO;AAAA,cAChB,UAAUA,QAAO;AAAA,cACjB,UAAUA,QAAO;AAAA,cACjB,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,QAAQA,QAAO;AAAA,cACf,UACE;AAAA,cACF,aAAaA,QAAO;AAAA,YACtB;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAD,OAAM,UAAU;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,YACE,YAAYC,QAAO;AAAA,YACnB,SAASA,QAAO;AAAA,YAChB,UAAUA,QAAO;AAAA,YACjB,UAAUA,QAAO;AAAA,YACjB,QAAQA,QAAO;AAAA,YACf,sBAAsB;AAAA,cACpB,SAASA,QAAO;AAAA,cAChB,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACAA,QAAO,KAAK,EAAE,OAAO,YAAY;AAAA,QACnC;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism", "parser"]}