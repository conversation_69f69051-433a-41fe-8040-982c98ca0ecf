{"version": 3, "sources": ["../../highlight.js/lib/languages/routeros.js"], "sourcesContent": ["/*\nLanguage: <PERSON><PERSON>k RouterOS script\nAuthor: <PERSON> <<EMAIL>>\nDescription: Scripting host provides a way to automate some router maintenance tasks by means of executing user-defined scripts bounded to some event occurrence\nWebsite: https://wiki.mikrotik.com/wiki/Manual:Scripting\n*/\n\n// Colors from RouterOS terminal:\n//   green        - #0E9A00\n//   teal         - #0C9A9A\n//   purple       - #99069A\n//   light-brown  - #9A9900\n\nfunction routeros(hljs) {\n  const STATEMENTS = 'foreach do while for if from to step else on-error and or not in';\n\n  // Global commands: Every global command should start with \":\" token, otherwise it will be treated as variable.\n  const GLOBAL_COMMANDS = 'global local beep delay put len typeof pick log time set find environment terminal error execute parse resolve toarray tobool toid toip toip6 tonum tostr totime';\n\n  // Common commands: Following commands available from most sub-menus:\n  const COMMON_COMMANDS = 'add remove enable disable set get print export edit find run debug error info warning';\n\n  const LITERALS = 'true false yes no nothing nil null';\n\n  const OBJECTS = 'traffic-flow traffic-generator firewall scheduler aaa accounting address-list address align area bandwidth-server bfd bgp bridge client clock community config connection console customer default dhcp-client dhcp-server discovery dns e-mail ethernet filter firmware gps graphing group hardware health hotspot identity igmp-proxy incoming instance interface ip ipsec ipv6 irq l2tp-server lcd ldp logging mac-server mac-winbox mangle manual mirror mme mpls nat nd neighbor network note ntp ospf ospf-v3 ovpn-server page peer pim ping policy pool port ppp pppoe-client pptp-server prefix profile proposal proxy queue radius resource rip ripng route routing screen script security-profiles server service service-port settings shares smb sms sniffer snmp snooper socks sstp-server system tool tracking type upgrade upnp user-manager users user vlan secret vrrp watchdog web-access wireless pptp pppoe lan wan layer7-protocol lease simple raw';\n\n  const VAR = {\n    className: 'variable',\n    variants: [\n      {\n        begin: /\\$[\\w\\d#@][\\w\\d_]*/\n      },\n      {\n        begin: /\\$\\{(.*?)\\}/\n      }\n    ]\n  };\n\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VAR,\n      {\n        className: 'variable',\n        begin: /\\$\\(/,\n        end: /\\)/,\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      }\n    ]\n  };\n\n  const APOS_STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n\n  return {\n    name: 'Microtik RouterOS script',\n    aliases: [\n      'mikrotik'\n    ],\n    case_insensitive: true,\n    keywords: {\n      $pattern: /:?[\\w-]+/,\n      literal: LITERALS,\n      keyword: STATEMENTS + ' :' + STATEMENTS.split(' ').join(' :') + ' :' + GLOBAL_COMMANDS.split(' ').join(' :')\n    },\n    contains: [\n      { // illegal syntax\n        variants: [\n          { // -- comment\n            begin: /\\/\\*/,\n            end: /\\*\\//\n          },\n          { // Stan comment\n            begin: /\\/\\//,\n            end: /$/\n          },\n          { // HTML tags\n            begin: /<\\//,\n            end: />/\n          }\n        ],\n        illegal: /./\n      },\n      hljs.COMMENT('^#', '$'),\n      QUOTE_STRING,\n      APOS_STRING,\n      VAR,\n      // attribute=value\n      {\n        // > is to avoid matches with => in other grammars\n        begin: /[\\w-]+=([^\\s{}[\\]()>]+)/,\n        relevance: 0,\n        returnBegin: true,\n        contains: [\n          {\n            className: 'attribute',\n            begin: /[^=]+/\n          },\n          {\n            begin: /=/,\n            endsWithParent: true,\n            relevance: 0,\n            contains: [\n              QUOTE_STRING,\n              APOS_STRING,\n              VAR,\n              {\n                className: 'literal',\n                begin: '\\\\b(' + LITERALS.split(' ').join('|') + ')\\\\b'\n              },\n              {\n                // Do not format unclassified values. Needed to exclude highlighting of values as built_in.\n                begin: /(\"[^\"]*\"|[^\\s{}[\\]]+)/\n              }\n              /*\n              {\n                // IPv4 addresses and subnets\n                className: 'number',\n                variants: [\n                  {begin: IPADDR_wBITMASK+'(,'+IPADDR_wBITMASK+')*'}, //***********/24,*******/24\n                  {begin: IPADDR+'-'+IPADDR},       // ***********-***********\n                  {begin: IPADDR+'(,'+IPADDR+')*'}, // ***********,***********4,************,***********\n                ]\n              },\n              {\n                // MAC addresses and DHCP Client IDs\n                className: 'number',\n                begin: /\\b(1:)?([0-9A-Fa-f]{1,2}[:-]){5}([0-9A-Fa-f]){1,2}\\b/,\n              },\n              */\n            ]\n          }\n        ]\n      },\n      {\n        // HEX values\n        className: 'number',\n        begin: /\\*[0-9a-fA-F]+/\n      },\n      {\n        begin: '\\\\b(' + COMMON_COMMANDS.split(' ').join('|') + ')([\\\\s[(\\\\]|])',\n        returnBegin: true,\n        contains: [\n          {\n            className: 'builtin-name', // 'function',\n            begin: /\\w+/\n          }\n        ]\n      },\n      {\n        className: 'built_in',\n        variants: [\n          {\n            begin: '(\\\\.\\\\./|/|\\\\s)((' + OBJECTS.split(' ').join('|') + ');?\\\\s)+'\n          },\n          {\n            begin: /\\.\\./,\n            relevance: 0\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = routeros;\n"], "mappings": ";;;;;AAAA;AAAA;AAaA,aAAS,SAAS,MAAM;AACtB,YAAM,aAAa;AAGnB,YAAM,kBAAkB;AAGxB,YAAM,kBAAkB;AAExB,YAAM,WAAW;AAEjB,YAAM,UAAU;AAEhB,YAAM,MAAM;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,QACF;AAAA,QACA,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SAAS;AAAA,UACT,SAAS,aAAa,OAAO,WAAW,MAAM,GAAG,EAAE,KAAK,IAAI,IAAI,OAAO,gBAAgB,MAAM,GAAG,EAAE,KAAK,IAAI;AAAA,QAC7G;AAAA,QACA,UAAU;AAAA,UACR;AAAA;AAAA,YACE,UAAU;AAAA,cACR;AAAA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,cACA;AAAA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,cACA;AAAA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,YACA,SAAS;AAAA,UACX;AAAA,UACA,KAAK,QAAQ,MAAM,GAAG;AAAA,UACtB;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UAEA;AAAA;AAAA,YAEE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,gBAAgB;AAAA,gBAChB,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,oBACE,WAAW;AAAA,oBACX,OAAO,SAAS,SAAS,MAAM,GAAG,EAAE,KAAK,GAAG,IAAI;AAAA,kBAClD;AAAA,kBACA;AAAA;AAAA,oBAEE,OAAO;AAAA,kBACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAiBF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO,SAAS,gBAAgB,MAAM,GAAG,EAAE,KAAK,GAAG,IAAI;AAAA,YACvD,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,sBAAsB,QAAQ,MAAM,GAAG,EAAE,KAAK,GAAG,IAAI;AAAA,cAC9D;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}