{"version": 3, "sources": ["../../refractor/lang/erlang.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = erlang\nerlang.displayName = 'erlang'\nerlang.aliases = []\nfunction erlang(Prism) {\n  Prism.languages.erlang = {\n    comment: /%.+/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n      greedy: true\n    },\n    'quoted-function': {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])+'(?=\\()/,\n      alias: 'function'\n    },\n    'quoted-atom': {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])+'/,\n      alias: 'atom'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    keyword: /\\b(?:after|case|catch|end|fun|if|of|receive|try|when)\\b/,\n    number: [\n      /\\$\\\\?./,\n      /\\b\\d+#[a-z0-9]+/i,\n      /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i\n    ],\n    function: /\\b[a-z][\\w@]*(?=\\()/,\n    variable: {\n      // Look-behind is used to prevent wrong highlighting of atoms containing \"@\"\n      pattern: /(^|[^@])(?:\\b|\\?)[A-Z_][\\w@]*/,\n      lookbehind: true\n    },\n    operator: [\n      /[=\\/<>:]=|=[:\\/]=|\\+\\+?|--?|[=*\\/!]|\\b(?:and|andalso|band|bnot|bor|bsl|bsr|bxor|div|not|or|orelse|rem|xor)\\b/,\n      {\n        // We don't want to match <<\n        pattern: /(^|[^<])<(?!<)/,\n        lookbehind: true\n      },\n      {\n        // We don't want to match >>\n        pattern: /(^|[^>])>(?!>)/,\n        lookbehind: true\n      }\n    ],\n    atom: /\\b[a-z][\\w@]*/,\n    punctuation: /[()[\\]{}:;,.#|]|<<|>>/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS;AAAA,QACvB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,mBAAmB;AAAA,UACjB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,eAAe;AAAA,UACb,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA;AAAA,UAER,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}