{"version": 3, "sources": ["../../refractor/lang/rip.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = rip\nrip.displayName = 'rip'\nrip.aliases = []\nfunction rip(Prism) {\n  Prism.languages.rip = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    char: {\n      pattern: /\\B`[^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]\\b/,\n      greedy: true\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    regex: {\n      pattern:\n        /(^|[^/])\\/(?!\\/)(?:\\[[^\\n\\r\\]]*\\]|\\\\.|[^/\\\\\\r\\n\\[])+\\/(?=\\s*(?:$|[\\r\\n,.;})]))/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword:\n      /(?:=>|->)|\\b(?:case|catch|class|else|exit|finally|if|raise|return|switch|try)\\b/,\n    builtin: /@|\\bSystem\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    date: /\\b\\d{4}-\\d{2}-\\d{2}\\b/,\n    time: /\\b\\d{2}:\\d{2}:\\d{2}\\b/,\n    datetime: /\\b\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\b/,\n    symbol: /:[^\\d\\s`'\",.:;#\\/\\\\()<>\\[\\]{}][^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]*/,\n    number: /[+-]?\\b(?:\\d+\\.\\d+|\\d+)\\b/,\n    punctuation: /(?:\\.{2,3})|[`,.:;=\\/\\\\()<>\\[\\]{}]/,\n    reference: /[^\\d\\s`'\",.:;#\\/\\\\()<>\\[\\]{}][^\\s`'\",.:;#\\/\\\\()<>\\[\\]{}]*/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,UAAU,MAAM;AAAA,QACpB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,OAAO;AAAA,UACL,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,WAAW;AAAA,MACb;AAAA,IACF;AAAA;AAAA;", "names": []}