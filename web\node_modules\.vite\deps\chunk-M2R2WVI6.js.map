{"version": 3, "sources": ["../../refractor/lang/kusto.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = kusto\nkusto.displayName = 'kusto'\nkusto.aliases = []\nfunction kusto(Prism) {\n  Prism.languages.kusto = {\n    comment: {\n      pattern: /\\/\\/.*/,\n      greedy: true\n    },\n    string: {\n      pattern:\n        /```[\\s\\S]*?```|[hH]?(?:\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"|'(?:[^\\r\\n\\\\']|\\\\.)*'|@(?:\"[^\\r\\n\"]*\"|'[^\\r\\n']*'))/,\n      greedy: true\n    },\n    verb: {\n      pattern: /(\\|\\s*)[a-z][\\w-]*/i,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    command: {\n      pattern: /\\.[a-z][a-z\\d-]*\\b/,\n      alias: 'keyword'\n    },\n    'class-name':\n      /\\b(?:bool|datetime|decimal|dynamic|guid|int|long|real|string|timespan)\\b/,\n    keyword:\n      /\\b(?:access|alias|and|anti|as|asc|auto|between|by|(?:contains|(?:ends|starts)with|has(?:perfix|suffix)?)(?:_cs)?|database|declare|desc|external|from|fullouter|has_all|in|ingestion|inline|inner|innerunique|into|(?:left|right)(?:anti(?:semi)?|inner|outer|semi)?|let|like|local|not|of|on|or|pattern|print|query_parameters|range|restrict|schema|set|step|table|tables|to|view|where|with|matches\\s+regex|nulls\\s+(?:first|last))(?![\\w-])/,\n    boolean: /\\b(?:false|null|true)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/,\n    datetime: [\n      {\n        // RFC 822 + RFC 850\n        pattern:\n          /\\b(?:(?:Fri|Friday|Mon|Monday|Sat|Saturday|Sun|Sunday|Thu|Thursday|Tue|Tuesday|Wed|Wednesday)\\s*,\\s*)?\\d{1,2}(?:\\s+|-)(?:Apr|Aug|Dec|Feb|Jan|Jul|Jun|Mar|May|Nov|Oct|Sep)(?:\\s+|-)\\d{2}\\s+\\d{2}:\\d{2}(?::\\d{2})?(?:\\s*(?:\\b(?:[A-Z]|(?:[ECMT][DS]|GM|U)T)|[+-]\\d{4}))?\\b/,\n        alias: 'number'\n      },\n      {\n        // ISO 8601\n        pattern:\n          /[+-]?\\b(?:\\d{4}-\\d{2}-\\d{2}(?:[ T]\\d{2}:\\d{2}(?::\\d{2}(?:\\.\\d+)?)?)?|\\d{2}:\\d{2}(?::\\d{2}(?:\\.\\d+)?)?)Z?/,\n        alias: 'number'\n      }\n    ],\n    number:\n      /\\b(?:0x[0-9A-Fa-f]+|\\d+(?:\\.\\d+)?(?:[Ee][+-]?\\d+)?)(?:(?:min|sec|[mnµ]s|[dhms]|microsecond|tick)\\b)?|[+-]?\\binf\\b/,\n    operator: /=>|[!=]~|[!=<>]=?|[-+*/%|]|\\.\\./,\n    punctuation: /[()\\[\\]{},;.:]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB,YAAM,UAAU,QAAQ;AAAA,QACtB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SACE;AAAA,UACF,QAAQ;AAAA,QACV;AAAA,QACA,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,cACE;AAAA,QACF,SACE;AAAA,QACF,SAAS;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,SACE;AAAA,YACF,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,SACE;AAAA,YACF,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,QACE;AAAA,QACF,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}