{"version": 3, "sources": ["../../highlight.js/lib/languages/coffeescript.js"], "sourcesContent": ["const KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // JS handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\nconst TYPES = [\n  \"Intl\",\n  \"DataView\",\n  \"Number\",\n  \"Math\",\n  \"Date\",\n  \"String\",\n  \"RegExp\",\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Error\",\n  \"Symbol\",\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  \"Proxy\",\n  \"Reflect\",\n  \"JSON\",\n  \"Promise\",\n  \"Float64Array\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Int8Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"Float32Array\",\n  \"Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"ArrayBuffer\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  \"BigInt\"\n];\n\nconst ERROR_TYPES = [\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  BUILT_IN_VARIABLES,\n  TYPES,\n  ERROR_TYPES\n);\n\n/*\nLanguage: CoffeeScript\nAuthor: Dmytrii Nagirniak <<EMAIL>>\nContributors: Oleg Efimov <<EMAIL>>, Cédric Néhémie <<EMAIL>>\nDescription: CoffeeScript is a programming language that transcompiles to JavaScript. For info about language see http://coffeescript.org/\nCategory: common, scripting\nWebsite: https://coffeescript.org\n*/\n\n/** @type LanguageFn */\nfunction coffeescript(hljs) {\n  const COFFEE_BUILT_INS = [\n    'npm',\n    'print'\n  ];\n  const COFFEE_LITERALS = [\n    'yes',\n    'no',\n    'on',\n    'off'\n  ];\n  const COFFEE_KEYWORDS = [\n    'then',\n    'unless',\n    'until',\n    'loop',\n    'by',\n    'when',\n    'and',\n    'or',\n    'is',\n    'isnt',\n    'not'\n  ];\n  const NOT_VALID_KEYWORDS = [\n    \"var\",\n    \"const\",\n    \"let\",\n    \"function\",\n    \"static\"\n  ];\n  const excluding = (list) =>\n    (kw) => !list.includes(kw);\n  const KEYWORDS$1 = {\n    keyword: KEYWORDS.concat(COFFEE_KEYWORDS).filter(excluding(NOT_VALID_KEYWORDS)),\n    literal: LITERALS.concat(COFFEE_LITERALS),\n    built_in: BUILT_INS.concat(COFFEE_BUILT_INS)\n  };\n  const JS_IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS$1\n  };\n  const EXPRESSIONS = [\n    hljs.BINARY_NUMBER_MODE,\n    hljs.inherit(hljs.C_NUMBER_MODE, {\n      starts: {\n        end: '(\\\\s*/)?',\n        relevance: 0\n      }\n    }), // a number tries to eat the following slash to prevent treating it as a regexp\n    {\n      className: 'string',\n      variants: [\n        {\n          begin: /'''/,\n          end: /'''/,\n          contains: [hljs.BACKSLASH_ESCAPE]\n        },\n        {\n          begin: /'/,\n          end: /'/,\n          contains: [hljs.BACKSLASH_ESCAPE]\n        },\n        {\n          begin: /\"\"\"/,\n          end: /\"\"\"/,\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ]\n        },\n        {\n          begin: /\"/,\n          end: /\"/,\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ]\n        }\n      ]\n    },\n    {\n      className: 'regexp',\n      variants: [\n        {\n          begin: '///',\n          end: '///',\n          contains: [\n            SUBST,\n            hljs.HASH_COMMENT_MODE\n          ]\n        },\n        {\n          begin: '//[gim]{0,3}(?=\\\\W)',\n          relevance: 0\n        },\n        {\n          // regex can't start with space to parse x / 2 / 3 as two divisions\n          // regex can't start with *, and it supports an \"illegal\" in the main mode\n          begin: /\\/(?![ *]).*?(?![\\\\]).\\/[gim]{0,3}(?=\\W)/\n        }\n      ]\n    },\n    {\n      begin: '@' + JS_IDENT_RE // relevance booster\n    },\n    {\n      subLanguage: 'javascript',\n      excludeBegin: true,\n      excludeEnd: true,\n      variants: [\n        {\n          begin: '```',\n          end: '```'\n        },\n        {\n          begin: '`',\n          end: '`'\n        }\n      ]\n    }\n  ];\n  SUBST.contains = EXPRESSIONS;\n\n  const TITLE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: JS_IDENT_RE\n  });\n  const POSSIBLE_PARAMS_RE = '(\\\\(.*\\\\)\\\\s*)?\\\\B[-=]>';\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\([^\\\\(]',\n    returnBegin: true,\n    /* We need another contained nameless mode to not have every nested\n    pair of parens to be called \"params\" */\n    contains: [{\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: ['self'].concat(EXPRESSIONS)\n    }]\n  };\n\n  return {\n    name: 'CoffeeScript',\n    aliases: [\n      'coffee',\n      'cson',\n      'iced'\n    ],\n    keywords: KEYWORDS$1,\n    illegal: /\\/\\*/,\n    contains: EXPRESSIONS.concat([\n      hljs.COMMENT('###', '###'),\n      hljs.HASH_COMMENT_MODE,\n      {\n        className: 'function',\n        begin: '^\\\\s*' + JS_IDENT_RE + '\\\\s*=\\\\s*' + POSSIBLE_PARAMS_RE,\n        end: '[-=]>',\n        returnBegin: true,\n        contains: [\n          TITLE,\n          PARAMS\n        ]\n      },\n      {\n        // anonymous function start\n        begin: /[:\\(,=]\\s*/,\n        relevance: 0,\n        contains: [{\n          className: 'function',\n          begin: POSSIBLE_PARAMS_RE,\n          end: '[-=]>',\n          returnBegin: true,\n          contains: [PARAMS]\n        }]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class',\n        end: '$',\n        illegal: /[:=\"\\[\\]]/,\n        contains: [\n          {\n            beginKeywords: 'extends',\n            endsWithParent: true,\n            illegal: /[:=\"\\[\\]]/,\n            contains: [TITLE]\n          },\n          TITLE\n        ]\n      },\n      {\n        begin: JS_IDENT_RE + ':',\n        end: ':',\n        returnBegin: true,\n        returnEnd: true,\n        relevance: 0\n      }\n    ])\n  };\n}\n\nmodule.exports = coffeescript;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,QAAM,WAAW;AAAA,MACf;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,MAIA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,QAAQ;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,cAAc;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,qBAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IACF;AAEA,QAAM,YAAY,CAAC,EAAE;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAYA,aAAS,aAAa,MAAM;AAC1B,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,qBAAqB;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,CAAC,SACjB,CAAC,OAAO,CAAC,KAAK,SAAS,EAAE;AAC3B,YAAM,aAAa;AAAA,QACjB,SAAS,SAAS,OAAO,eAAe,EAAE,OAAO,UAAU,kBAAkB,CAAC;AAAA,QAC9E,SAAS,SAAS,OAAO,eAAe;AAAA,QACxC,UAAU,UAAU,OAAO,gBAAgB;AAAA,MAC7C;AACA,YAAM,cAAc;AACpB,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,cAAc;AAAA,QAClB,KAAK;AAAA,QACL,KAAK,QAAQ,KAAK,eAAe;AAAA,UAC/B,QAAQ;AAAA,YACN,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QACF,CAAC;AAAA;AAAA,QACD;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU,CAAC,KAAK,gBAAgB;AAAA,YAClC;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU,CAAC,KAAK,gBAAgB;AAAA,YAClC;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU;AAAA,gBACR,KAAK;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU;AAAA,gBACR,KAAK;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU;AAAA,gBACR;AAAA,gBACA,KAAK;AAAA,cACP;AAAA,YACF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,YACA;AAAA;AAAA;AAAA,cAGE,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO,MAAM;AAAA;AAAA,QACf;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,YACP;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,YACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW;AAEjB,YAAM,QAAQ,KAAK,QAAQ,KAAK,YAAY;AAAA,QAC1C,OAAO;AAAA,MACT,CAAC;AACD,YAAM,qBAAqB;AAC3B,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,aAAa;AAAA;AAAA;AAAA,QAGb,UAAU,CAAC;AAAA,UACT,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,UACV,UAAU,CAAC,MAAM,EAAE,OAAO,WAAW;AAAA,QACvC,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU,YAAY,OAAO;AAAA,UAC3B,KAAK,QAAQ,OAAO,KAAK;AAAA,UACzB,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO,UAAU,cAAc,cAAc;AAAA,YAC7C,KAAK;AAAA,YACL,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,UAAU,CAAC;AAAA,cACT,WAAW;AAAA,cACX,OAAO;AAAA,cACP,KAAK;AAAA,cACL,aAAa;AAAA,cACb,UAAU,CAAC,MAAM;AAAA,YACnB,CAAC;AAAA,UACH;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,gBACf,gBAAgB;AAAA,gBAChB,SAAS;AAAA,gBACT,UAAU,CAAC,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO,cAAc;AAAA,YACrB,KAAK;AAAA,YACL,aAAa;AAAA,YACb,WAAW;AAAA,YACX,WAAW;AAAA,UACb;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}