{"version": 3, "sources": ["../../highlight.js/lib/languages/kotlin.js"], "sourcesContent": ["// https://docs.oracle.com/javase/specs/jls/se15/html/jls-3.html#jls-3.10\nvar decimalDigits = '[0-9](_*[0-9])*';\nvar frac = `\\\\.(${decimalDigits})`;\nvar hexDigits = '[0-9a-fA-F](_*[0-9a-fA-F])*';\nvar NUMERIC = {\n  className: 'number',\n  variants: [\n    // DecimalFloatingPointLiteral\n    // including ExponentPart\n    { begin: `(\\\\b(${decimalDigits})((${frac})|\\\\.)?|(${frac}))` +\n      `[eE][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n    // excluding ExponentPart\n    { begin: `\\\\b(${decimalDigits})((${frac})[fFdD]?\\\\b|\\\\.([fFdD]\\\\b)?)` },\n    { begin: `(${frac})[fFdD]?\\\\b` },\n    { begin: `\\\\b(${decimalDigits})[fFdD]\\\\b` },\n\n    // HexadecimalFloatingPointLiteral\n    { begin: `\\\\b0[xX]((${hexDigits})\\\\.?|(${hexDigits})?\\\\.(${hexDigits}))` +\n      `[pP][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n\n    // DecimalIntegerLiteral\n    { begin: '\\\\b(0|[1-9](_*[0-9])*)[lL]?\\\\b' },\n\n    // HexIntegerLiteral\n    { begin: `\\\\b0[xX](${hexDigits})[lL]?\\\\b` },\n\n    // OctalIntegerLiteral\n    { begin: '\\\\b0(_*[0-7])*[lL]?\\\\b' },\n\n    // BinaryIntegerLiteral\n    { begin: '\\\\b0[bB][01](_*[01])*[lL]?\\\\b' },\n  ],\n  relevance: 0\n};\n\n/*\n Language: Kotlin\n Description: Kotlin is an OSS statically typed programming language that targets the JVM, Android, JavaScript and Native.\n Author: Sergey Mashkov <<EMAIL>>\n Website: https://kotlinlang.org\n Category: common\n */\n\nfunction kotlin(hljs) {\n  const KEYWORDS = {\n    keyword:\n      'abstract as val var vararg get set class object open private protected public noinline ' +\n      'crossinline dynamic final enum if else do while for when throw try catch finally ' +\n      'import package is in fun override companion reified inline lateinit init ' +\n      'interface annotation data sealed internal infix operator out by constructor super ' +\n      'tailrec where const inner suspend typealias external expect actual',\n    built_in:\n      'Byte Short Char Int Long Boolean Float Double Void Unit Nothing',\n    literal:\n      'true false null'\n  };\n  const KEYWORDS_WITH_LABEL = {\n    className: 'keyword',\n    begin: /\\b(break|continue|return|this)\\b/,\n    starts: {\n      contains: [\n        {\n          className: 'symbol',\n          begin: /@\\w+/\n        }\n      ]\n    }\n  };\n  const LABEL = {\n    className: 'symbol',\n    begin: hljs.UNDERSCORE_IDENT_RE + '@'\n  };\n\n  // for string templates\n  const SUBST = {\n    className: 'subst',\n    begin: /\\$\\{/,\n    end: /\\}/,\n    contains: [ hljs.C_NUMBER_MODE ]\n  };\n  const VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$' + hljs.UNDERSCORE_IDENT_RE\n  };\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: '\"\"\"',\n        end: '\"\"\"(?=[^\"])',\n        contains: [\n          VARIABLE,\n          SUBST\n        ]\n      },\n      // Can't use built-in modes easily, as we want to use STRING in the meta\n      // context as 'meta-string' and there's no syntax to remove explicitly set\n      // classNames in built-in modes.\n      {\n        begin: '\\'',\n        end: '\\'',\n        illegal: /\\n/,\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '\"',\n        end: '\"',\n        illegal: /\\n/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          VARIABLE,\n          SUBST\n        ]\n      }\n    ]\n  };\n  SUBST.contains.push(STRING);\n\n  const ANNOTATION_USE_SITE = {\n    className: 'meta',\n    begin: '@(?:file|property|field|get|set|receiver|param|setparam|delegate)\\\\s*:(?:\\\\s*' + hljs.UNDERSCORE_IDENT_RE + ')?'\n  };\n  const ANNOTATION = {\n    className: 'meta',\n    begin: '@' + hljs.UNDERSCORE_IDENT_RE,\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\n          hljs.inherit(STRING, {\n            className: 'meta-string'\n          })\n        ]\n      }\n    ]\n  };\n\n  // https://kotlinlang.org/docs/reference/whatsnew11.html#underscores-in-numeric-literals\n  // According to the doc above, the number mode of kotlin is the same as java 8,\n  // so the code below is copied from java.js\n  const KOTLIN_NUMBER_MODE = NUMERIC;\n  const KOTLIN_NESTED_COMMENT = hljs.COMMENT(\n    '/\\\\*', '\\\\*/',\n    {\n      contains: [ hljs.C_BLOCK_COMMENT_MODE ]\n    }\n  );\n  const KOTLIN_PAREN_TYPE = {\n    variants: [\n      {\n        className: 'type',\n        begin: hljs.UNDERSCORE_IDENT_RE\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [] // defined later\n      }\n    ]\n  };\n  const KOTLIN_PAREN_TYPE2 = KOTLIN_PAREN_TYPE;\n  KOTLIN_PAREN_TYPE2.variants[1].contains = [ KOTLIN_PAREN_TYPE ];\n  KOTLIN_PAREN_TYPE.variants[1].contains = [ KOTLIN_PAREN_TYPE2 ];\n\n  return {\n    name: 'Kotlin',\n    aliases: [ 'kt', 'kts' ],\n    keywords: KEYWORDS,\n    contains: [\n      hljs.COMMENT(\n        '/\\\\*\\\\*',\n        '\\\\*/',\n        {\n          relevance: 0,\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      KOTLIN_NESTED_COMMENT,\n      KEYWORDS_WITH_LABEL,\n      LABEL,\n      ANNOTATION_USE_SITE,\n      ANNOTATION,\n      {\n        className: 'function',\n        beginKeywords: 'fun',\n        end: '[(]|$',\n        returnBegin: true,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        relevance: 5,\n        contains: [\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n            returnBegin: true,\n            relevance: 0,\n            contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n          },\n          {\n            className: 'type',\n            begin: /</,\n            end: />/,\n            keywords: 'reified',\n            relevance: 0\n          },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            endsParent: true,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              {\n                begin: /:/,\n                end: /[=,\\/]/,\n                endsWithParent: true,\n                contains: [\n                  KOTLIN_PAREN_TYPE,\n                  hljs.C_LINE_COMMENT_MODE,\n                  KOTLIN_NESTED_COMMENT\n                ],\n                relevance: 0\n              },\n              hljs.C_LINE_COMMENT_MODE,\n              KOTLIN_NESTED_COMMENT,\n              ANNOTATION_USE_SITE,\n              ANNOTATION,\n              STRING,\n              hljs.C_NUMBER_MODE\n            ]\n          },\n          KOTLIN_NESTED_COMMENT\n        ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface trait', // remove 'trait' when removed from KEYWORDS\n        end: /[:\\{(]|$/,\n        excludeEnd: true,\n        illegal: 'extends implements',\n        contains: [\n          {\n            beginKeywords: 'public protected internal private constructor'\n          },\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            className: 'type',\n            begin: /</,\n            end: />/,\n            excludeBegin: true,\n            excludeEnd: true,\n            relevance: 0\n          },\n          {\n            className: 'type',\n            begin: /[,:]\\s*/,\n            end: /[<\\(,]|$/,\n            excludeBegin: true,\n            returnEnd: true\n          },\n          ANNOTATION_USE_SITE,\n          ANNOTATION\n        ]\n      },\n      STRING,\n      {\n        className: 'meta',\n        begin: \"^#!/usr/bin/env\",\n        end: '$',\n        illegal: '\\n'\n      },\n      KOTLIN_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = kotlin;\n"], "mappings": ";;;;;AAAA;AAAA;AACA,QAAI,gBAAgB;AACpB,QAAI,OAAO,OAAO,aAAa;AAC/B,QAAI,YAAY;AAChB,QAAI,UAAU;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA;AAAA;AAAA,QAGR,EAAE,OAAO,QAAQ,aAAa,MAAM,IAAI,YAAY,IAAI,eACzC,aAAa,cAAc;AAAA;AAAA,QAE1C,EAAE,OAAO,OAAO,aAAa,MAAM,IAAI,+BAA+B;AAAA,QACtE,EAAE,OAAO,IAAI,IAAI,cAAc;AAAA,QAC/B,EAAE,OAAO,OAAO,aAAa,aAAa;AAAA;AAAA,QAG1C,EAAE,OAAO,aAAa,SAAS,UAAU,SAAS,SAAS,SAAS,eACrD,aAAa,cAAc;AAAA;AAAA,QAG1C,EAAE,OAAO,iCAAiC;AAAA;AAAA,QAG1C,EAAE,OAAO,YAAY,SAAS,YAAY;AAAA;AAAA,QAG1C,EAAE,OAAO,yBAAyB;AAAA;AAAA,QAGlC,EAAE,OAAO,gCAAgC;AAAA,MAC3C;AAAA,MACA,WAAW;AAAA,IACb;AAUA,aAAS,OAAO,MAAM;AACpB,YAAM,WAAW;AAAA,QACf,SACE;AAAA,QAKF,UACE;AAAA,QACF,SACE;AAAA,MACJ;AACA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,UACN,UAAU;AAAA,YACR;AAAA,cACE,WAAW;AAAA,cACX,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO,KAAK,sBAAsB;AAAA,MACpC;AAGA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,KAAK,aAAc;AAAA,MACjC;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO,QAAQ,KAAK;AAAA,MACtB;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAE,KAAK,gBAAiB;AAAA,UACpC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS,KAAK,MAAM;AAE1B,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO,kFAAkF,KAAK,sBAAsB;AAAA,MACtH;AACA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO,MAAM,KAAK;AAAA,QAClB,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK,QAAQ,QAAQ;AAAA,gBACnB,WAAW;AAAA,cACb,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAKA,YAAM,qBAAqB;AAC3B,YAAM,wBAAwB,KAAK;AAAA,QACjC;AAAA,QAAQ;AAAA,QACR;AAAA,UACE,UAAU,CAAE,KAAK,oBAAqB;AAAA,QACxC;AAAA,MACF;AACA,YAAM,oBAAoB;AAAA,QACxB,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO,KAAK;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC;AAAA;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,YAAM,qBAAqB;AAC3B,yBAAmB,SAAS,CAAC,EAAE,WAAW,CAAE,iBAAkB;AAC9D,wBAAkB,SAAS,CAAC,EAAE,WAAW,CAAE,kBAAmB;AAE9D,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,MAAM,KAAM;AAAA,QACvB,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA,gBACR;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,KAAK,sBAAsB;AAAA,gBAClC,aAAa;AAAA,gBACb,WAAW;AAAA,gBACX,UAAU,CAAE,KAAK,qBAAsB;AAAA,cACzC;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,gBACV,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,oBACL,gBAAgB;AAAA,oBAChB,UAAU;AAAA,sBACR;AAAA,sBACA,KAAK;AAAA,sBACL;AAAA,oBACF;AAAA,oBACA,WAAW;AAAA,kBACb;AAAA,kBACA,KAAK;AAAA,kBACL;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,cACjB;AAAA,cACA,KAAK;AAAA,cACL;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,WAAW;AAAA,cACb;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,UACX;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}