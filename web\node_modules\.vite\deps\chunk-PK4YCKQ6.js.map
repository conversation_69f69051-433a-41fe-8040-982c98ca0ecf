{"version": 3, "sources": ["../../refractor/lang/avro-idl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = avroIdl\navroIdl.displayName = 'avroIdl'\navroIdl.aliases = []\nfunction avroIdl(Prism) {\n  // GitHub: https://github.com/apache/avro\n  // Docs: https://avro.apache.org/docs/current/idl.html\n  Prism.languages['avro-idl'] = {\n    comment: {\n      pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n      greedy: true\n    },\n    string: {\n      pattern: /(^|[^\\\\])\"(?:[^\\r\\n\"\\\\]|\\\\.)*\"/,\n      lookbehind: true,\n      greedy: true\n    },\n    annotation: {\n      pattern: /@(?:[$\\w.-]|`[^\\r\\n`]+`)+/,\n      greedy: true,\n      alias: 'function'\n    },\n    'function-identifier': {\n      pattern: /`[^\\r\\n`]+`(?=\\s*\\()/,\n      greedy: true,\n      alias: 'function'\n    },\n    identifier: {\n      pattern: /`[^\\r\\n`]+`/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\b(?:enum|error|protocol|record|throws)\\b\\s+)[$\\w]+/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:array|boolean|bytes|date|decimal|double|enum|error|false|fixed|float|idl|import|int|local_timestamp_ms|long|map|null|oneway|protocol|record|schema|string|throws|time_ms|timestamp_ms|true|union|uuid|void)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number: [\n      {\n        pattern:\n          /(^|[^\\w.])-?(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|0x(?:[a-f0-9]+(?:\\.[a-f0-9]*)?|\\.[a-f0-9]+)(?:p[+-]?\\d+)?)[dfl]?(?![\\w.])/i,\n        lookbehind: true\n      },\n      /-?\\b(?:Infinity|NaN)\\b/\n    ],\n    operator: /=/,\n    punctuation: /[()\\[\\]{}<>.:,;-]/\n  }\n  Prism.languages.avdl = Prism.languages['avro-idl']\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AAGtB,YAAM,UAAU,UAAU,IAAI;AAAA,QAC5B,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,YAAY;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,uBAAuB;AAAA,UACrB,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,YAAY;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,QACV,QAAQ;AAAA,UACN;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AACA,YAAM,UAAU,OAAO,MAAM,UAAU,UAAU;AAAA,IACnD;AAAA;AAAA;", "names": []}