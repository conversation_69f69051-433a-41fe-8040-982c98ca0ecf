{"version": 3, "sources": ["../../refractor/lang/erb.js"], "sourcesContent": ["'use strict'\nvar refractorRuby = require('./ruby.js')\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = erb\nerb.displayName = 'erb'\nerb.aliases = []\nfunction erb(Prism) {\n  Prism.register(refractorRuby)\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.erb = {\n      delimiter: {\n        pattern: /^(\\s*)<%=?|%>(?=\\s*$)/,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      ruby: {\n        pattern: /\\s*\\S[\\s\\S]*/,\n        alias: 'language-ruby',\n        inside: Prism.languages.ruby\n      }\n    }\n    Prism.hooks.add('before-tokenize', function (env) {\n      var erbPattern =\n        /<%=?(?:[^\\r\\n]|[\\r\\n](?!=begin)|[\\r\\n]=begin\\s(?:[^\\r\\n]|[\\r\\n](?!=end))*[\\r\\n]=end)+?%>/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'erb',\n        erbPattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'erb')\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AACA,QAAI,gBAAgB;AACpB,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,SAAS,aAAa;AAC5B,YAAM,SAAS,yBAAyB;AACvC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,MAAM;AAAA,UACpB,WAAW;AAAA,YACT,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,YACJ,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,QACF;AACA,QAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,cAAI,aACF;AACF,UAAAA,OAAM,UAAU,mBAAmB,EAAE;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,UAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,KAAK;AAAA,QACtE,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}