{"version": 3, "sources": ["../../refractor/lang/parigp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = parigp\nparigp.displayName = 'parigp'\nparigp.aliases = []\nfunction parigp(Prism) {\n  Prism.languages.parigp = {\n    comment: /\\/\\*[\\s\\S]*?\\*\\/|\\\\\\\\.*/,\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"/,\n      greedy: true\n    },\n    // PARI/GP does not care about white spaces at all\n    // so let's process the keywords to build an appropriate regexp\n    // (e.g. \"b *r *e *a *k\", etc.)\n    keyword: (function () {\n      var keywords = [\n        'breakpoint',\n        'break',\n        'dbg_down',\n        'dbg_err',\n        'dbg_up',\n        'dbg_x',\n        'forcomposite',\n        'fordiv',\n        'forell',\n        'forpart',\n        'forprime',\n        'forstep',\n        'forsubgroup',\n        'forvec',\n        'for',\n        'iferr',\n        'if',\n        'local',\n        'my',\n        'next',\n        'return',\n        'until',\n        'while'\n      ]\n      keywords = keywords\n        .map(function (keyword) {\n          return keyword.split('').join(' *')\n        })\n        .join('|')\n      return RegExp('\\\\b(?:' + keywords + ')\\\\b')\n    })(),\n    function: /\\b\\w(?:[\\w ]*\\w)?(?= *\\()/,\n    number: {\n      // The lookbehind and the negative lookahead prevent from breaking the .. operator\n      pattern:\n        /((?:\\. *\\. *)?)(?:\\b\\d(?: *\\d)*(?: *(?!\\. *\\.)\\.(?: *\\d)*)?|\\. *\\d(?: *\\d)*)(?: *e *(?:[+-] *)?\\d(?: *\\d)*)?/i,\n      lookbehind: true\n    },\n    operator:\n      /\\. *\\.|[*\\/!](?: *=)?|%(?: *=|(?: *#)?(?: *')*)?|\\+(?: *[+=])?|-(?: *[-=>])?|<(?: *>|(?: *<)?(?: *=)?)?|>(?: *>)?(?: *=)?|=(?: *=){0,2}|\\\\(?: *\\/)?(?: *=)?|&(?: *&)?|\\| *\\||['#~^]/,\n    punctuation: /[\\[\\]{}().,:;|]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS;AAAA,QACvB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA;AAAA;AAAA;AAAA,QAIA,SAAU,WAAY;AACpB,cAAI,WAAW;AAAA,YACb;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,qBAAW,SACR,IAAI,SAAU,SAAS;AACtB,mBAAO,QAAQ,MAAM,EAAE,EAAE,KAAK,IAAI;AAAA,UACpC,CAAC,EACA,KAAK,GAAG;AACX,iBAAO,OAAO,WAAW,WAAW,MAAM;AAAA,QAC5C,EAAG;AAAA,QACH,UAAU;AAAA,QACV,QAAQ;AAAA;AAAA,UAEN,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,UACE;AAAA,QACF,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}