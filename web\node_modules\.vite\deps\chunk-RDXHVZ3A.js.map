{"version": 3, "sources": ["../../highlight.js/lib/languages/crmsh.js"], "sourcesContent": ["/*\nLanguage: crmsh\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: http://crmsh.github.io\nDescription: Syntax Highlighting for the crmsh DSL\nCategory: config\n*/\n\n/** @type LanguageFn */\nfunction crmsh(hljs) {\n  const RESOURCES = 'primitive rsc_template';\n  const COMMANDS = 'group clone ms master location colocation order fencing_topology ' +\n      'rsc_ticket acl_target acl_group user role ' +\n      'tag xml';\n  const PROPERTY_SETS = 'property rsc_defaults op_defaults';\n  const KEYWORDS = 'params meta operations op rule attributes utilization';\n  const OPERATORS = 'read write deny defined not_defined in_range date spec in ' +\n      'ref reference attribute type xpath version and or lt gt tag ' +\n      'lte gte eq ne \\\\';\n  const TYPES = 'number string';\n  const LITERALS = 'Master Started Slave Stopped start promote demote stop monitor true false';\n\n  return {\n    name: 'crmsh',\n    aliases: [\n      'crm',\n      'pcmk'\n    ],\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS + ' ' + OPERATORS + ' ' + TYPES,\n      literal: LITERALS\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      {\n        beginKeywords: 'node',\n        starts: {\n          end: '\\\\s*([\\\\w_-]+:)?',\n          starts: {\n            className: 'title',\n            end: '\\\\s*[\\\\$\\\\w_][\\\\w_-]*'\n          }\n        }\n      },\n      {\n        beginKeywords: RESOURCES,\n        starts: {\n          className: 'title',\n          end: '\\\\s*[\\\\$\\\\w_][\\\\w_-]*',\n          starts: {\n            end: '\\\\s*@?[\\\\w_][\\\\w_\\\\.:-]*'\n          }\n        }\n      },\n      {\n        begin: '\\\\b(' + COMMANDS.split(' ').join('|') + ')\\\\s+',\n        keywords: COMMANDS,\n        starts: {\n          className: 'title',\n          end: '[\\\\$\\\\w_][\\\\w_-]*'\n        }\n      },\n      {\n        beginKeywords: PROPERTY_SETS,\n        starts: {\n          className: 'title',\n          end: '\\\\s*([\\\\w_-]+:)?'\n        }\n      },\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'meta',\n        begin: '(ocf|systemd|service|lsb):[\\\\w_:-]+',\n        relevance: 0\n      },\n      {\n        className: 'number',\n        begin: '\\\\b\\\\d+(\\\\.\\\\d+)?(ms|s|h|m)?',\n        relevance: 0\n      },\n      {\n        className: 'literal',\n        begin: '[-]?(infinity|inf)',\n        relevance: 0\n      },\n      {\n        className: 'attr',\n        begin: /([A-Za-z$_#][\\w_-]+)=/,\n        relevance: 0\n      },\n      {\n        className: 'tag',\n        begin: '</?',\n        end: '/?>',\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = crmsh;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,MAAM,MAAM;AACnB,YAAM,YAAY;AAClB,YAAM,WAAW;AAGjB,YAAM,gBAAgB;AACtB,YAAM,WAAW;AACjB,YAAM,YAAY;AAGlB,YAAM,QAAQ;AACd,YAAM,WAAW;AAEjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,SAAS,WAAW,MAAM,YAAY,MAAM;AAAA,UAC5C,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,YACE,eAAe;AAAA,YACf,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,QAAQ;AAAA,gBACN,WAAW;AAAA,gBACX,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,QAAQ;AAAA,cACN,WAAW;AAAA,cACX,KAAK;AAAA,cACL,QAAQ;AAAA,gBACN,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO,SAAS,SAAS,MAAM,GAAG,EAAE,KAAK,GAAG,IAAI;AAAA,YAChD,UAAU;AAAA,YACV,QAAQ;AAAA,cACN,WAAW;AAAA,cACX,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,QAAQ;AAAA,cACN,WAAW;AAAA,cACX,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}