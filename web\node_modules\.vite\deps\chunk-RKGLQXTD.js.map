{"version": 3, "sources": ["../../highlight.js/lib/languages/oxygene.js"], "sourcesContent": ["/*\nLanguage: Oxygene\nAuthor: <PERSON> <<EMAIL>>\nDescription: Oxygene is built on the foundation of Object Pascal, revamped and extended to be a modern language for the twenty-first century.\nWebsite: https://www.elementscompiler.com/elements/default.aspx\n*/\n\nfunction oxygene(hljs) {\n  const OXYGENE_KEYWORDS = {\n    $pattern: /\\.?\\w+/,\n    keyword:\n      'abstract add and array as asc aspect assembly async begin break block by case class concat const copy constructor continue ' +\n      'create default delegate desc distinct div do downto dynamic each else empty end ensure enum equals event except exit extension external false ' +\n      'final finalize finalizer finally flags for forward from function future global group has if implementation implements implies in index inherited ' +\n      'inline interface into invariants is iterator join locked locking loop matching method mod module namespace nested new nil not notify nullable of ' +\n      'old on operator or order out override parallel params partial pinned private procedure property protected public queryable raise read readonly ' +\n      'record reintroduce remove repeat require result reverse sealed select self sequence set shl shr skip static step soft take then to true try tuple ' +\n      'type union unit unsafe until uses using var virtual raises volatile where while with write xor yield await mapped deprecated stdcall cdecl pascal ' +\n      'register safecall overload library platform reference packed strict published autoreleasepool selector strong weak unretained'\n  };\n  const CURLY_COMMENT = hljs.COMMENT(\n    /\\{/,\n    /\\}/,\n    {\n      relevance: 0\n    }\n  );\n  const PAREN_COMMENT = hljs.COMMENT(\n    '\\\\(\\\\*',\n    '\\\\*\\\\)',\n    {\n      relevance: 10\n    }\n  );\n  const STRING = {\n    className: 'string',\n    begin: '\\'',\n    end: '\\'',\n    contains: [\n      {\n        begin: '\\'\\''\n      }\n    ]\n  };\n  const CHAR_STRING = {\n    className: 'string',\n    begin: '(#\\\\d+)+'\n  };\n  const FUNCTION = {\n    className: 'function',\n    beginKeywords: 'function constructor destructor procedure method',\n    end: '[:;]',\n    keywords: 'function constructor|10 destructor|10 procedure|10 method|10',\n    contains: [\n      hljs.TITLE_MODE,\n      {\n        className: 'params',\n        begin: '\\\\(',\n        end: '\\\\)',\n        keywords: OXYGENE_KEYWORDS,\n        contains: [\n          STRING,\n          CHAR_STRING\n        ]\n      },\n      CURLY_COMMENT,\n      PAREN_COMMENT\n    ]\n  };\n  return {\n    name: 'Oxygene',\n    case_insensitive: true,\n    keywords: OXYGENE_KEYWORDS,\n    illegal: '(\"|\\\\$[G-Zg-z]|\\\\/\\\\*|</|=>|->)',\n    contains: [\n      CURLY_COMMENT,\n      PAREN_COMMENT,\n      hljs.C_LINE_COMMENT_MODE,\n      STRING,\n      CHAR_STRING,\n      hljs.NUMBER_MODE,\n      FUNCTION,\n      {\n        className: 'class',\n        begin: '=\\\\bclass\\\\b',\n        end: 'end;',\n        keywords: OXYGENE_KEYWORDS,\n        contains: [\n          STRING,\n          CHAR_STRING,\n          CURLY_COMMENT,\n          PAREN_COMMENT,\n          hljs.C_LINE_COMMENT_MODE,\n          FUNCTION\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = oxygene;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,QAAQ,MAAM;AACrB,YAAM,mBAAmB;AAAA,QACvB,UAAU;AAAA,QACV,SACE;AAAA,MAQJ;AACA,YAAM,gBAAgB,KAAK;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW;AAAA,QACb;AAAA,MACF;AACA,YAAM,gBAAgB,KAAK;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW;AAAA,QACb;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,KAAK;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}