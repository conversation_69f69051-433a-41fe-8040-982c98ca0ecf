{"version": 3, "sources": ["../../highlight.js/lib/languages/rib.js"], "sourcesContent": ["/*\nLanguage: RenderMan RIB\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://renderman.pixar.com/resources/RenderMan_20/ribBinding.html\nCategory: graphics\n*/\n\nfunction rib(hljs) {\n  return {\n    name: 'RenderMan RIB',\n    keywords:\n      'ArchiveRecord AreaLightSource Atmosphere Attribute AttributeBegin AttributeEnd Basis ' +\n      'Begin Blobby Bound Clipping ClippingPlane Color ColorSamples ConcatTransform Cone ' +\n      'CoordinateSystem CoordSysTransform CropWindow Curves Cylinder DepthOfField Detail ' +\n      'DetailRange Disk Displacement Display End ErrorHandler Exposure Exterior Format ' +\n      'FrameAspectRatio FrameBegin FrameEnd GeneralPolygon GeometricApproximation Geometry ' +\n      'Hider Hyperboloid Identity Illuminate Imager Interior LightSource ' +\n      'MakeCubeFaceEnvironment MakeLatLongEnvironment MakeShadow MakeTexture <PERSON> ' +\n      'MotionBegin MotionEnd NuPatch ObjectBegin ObjectEnd ObjectInstance Opacity Option ' +\n      'Orientation Paraboloid Patch PatchMesh Perspective PixelFilter PixelSamples ' +\n      'PixelVariance Points PointsGeneralPolygons PointsPolygons Polygon Procedural Projection ' +\n      'Quantize ReadArchive RelativeDetail ReverseOrientation Rotate Scale ScreenWindow ' +\n      'ShadingInterpolation ShadingRate Shutter Sides Skew SolidBegin SolidEnd Sphere ' +\n      'SubdivisionMesh Surface TextureCoordinates Torus Transform TransformBegin TransformEnd ' +\n      'TransformPoints Translate TrimCurve WorldBegin WorldEnd',\n    illegal: '</',\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n}\n\nmodule.exports = rib;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UACE;AAAA,QAcF,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}