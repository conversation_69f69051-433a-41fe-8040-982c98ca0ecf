{"version": 3, "sources": ["../../refractor/lang/toml.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = toml\ntoml.displayName = 'toml'\ntoml.aliases = []\nfunction toml(Prism) {\n  ;(function (Prism) {\n    var key = /(?:[\\w-]+|'[^'\\n\\r]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")/.source\n    /**\n     * @param {string} pattern\n     */\n    function insertKey(pattern) {\n      return pattern.replace(/__/g, function () {\n        return key\n      })\n    }\n    Prism.languages.toml = {\n      comment: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      table: {\n        pattern: RegExp(\n          insertKey(\n            /(^[\\t ]*\\[\\s*(?:\\[\\s*)?)__(?:\\s*\\.\\s*__)*(?=\\s*\\])/.source\n          ),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'class-name'\n      },\n      key: {\n        pattern: RegExp(\n          insertKey(/(^[\\t ]*|[{,]\\s*)__(?:\\s*\\.\\s*__)*(?=\\s*=)/.source),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'property'\n      },\n      string: {\n        pattern:\n          /\"\"\"(?:\\\\[\\s\\S]|[^\\\\])*?\"\"\"|'''[\\s\\S]*?'''|'[^'\\n\\r]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n        greedy: true\n      },\n      date: [\n        {\n          // Offset Date-Time, Local Date-Time, Local Date\n          pattern:\n            /\\b\\d{4}-\\d{2}-\\d{2}(?:[T\\s]\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|[+-]\\d{2}:\\d{2})?)?\\b/i,\n          alias: 'number'\n        },\n        {\n          // Local Time\n          pattern: /\\b\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?\\b/,\n          alias: 'number'\n        }\n      ],\n      number:\n        /(?:\\b0(?:x[\\da-zA-Z]+(?:_[\\da-zA-Z]+)*|o[0-7]+(?:_[0-7]+)*|b[10]+(?:_[10]+)*))\\b|[-+]?\\b\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?(?:[eE][+-]?\\d+(?:_\\d+)*)?\\b|[-+]?\\b(?:inf|nan)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      punctuation: /[.,=[\\]{}]/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,MAAM,+CAA+C;AAIzD,iBAAS,UAAU,SAAS;AAC1B,iBAAO,QAAQ,QAAQ,OAAO,WAAY;AACxC,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,QAAAA,OAAM,UAAU,OAAO;AAAA,UACrB,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,OAAO;AAAA,YACL,SAAS;AAAA,cACP;AAAA,gBACE,qDAAqD;AAAA,cACvD;AAAA,cACA;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,KAAK;AAAA,YACH,SAAS;AAAA,cACP,UAAU,6CAA6C,MAAM;AAAA,cAC7D;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,YACN,SACE;AAAA,YACF,QAAQ;AAAA,UACV;AAAA,UACA,MAAM;AAAA,YACJ;AAAA;AAAA,cAEE,SACE;AAAA,cACF,OAAO;AAAA,YACT;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,QACE;AAAA,UACF,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}