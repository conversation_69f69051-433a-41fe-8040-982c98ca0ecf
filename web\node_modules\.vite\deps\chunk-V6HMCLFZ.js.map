{"version": 3, "sources": ["../../refractor/lang/markup.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = markup\nmarkup.displayName = 'markup'\nmarkup.aliases = ['html', 'mathml', 'svg', 'xml', 'ssml', 'atom', 'rss']\nfunction markup(Prism) {\n  Prism.languages.markup = {\n    comment: {\n      pattern: /<!--(?:(?!<!--)[\\s\\S])*?-->/,\n      greedy: true\n    },\n    prolog: {\n      pattern: /<\\?[\\s\\S]+?\\?>/,\n      greedy: true\n    },\n    doctype: {\n      // https://www.w3.org/TR/xml/#NT-doctypedecl\n      pattern:\n        /<!DOCTYPE(?:[^>\"'[\\]]|\"[^\"]*\"|'[^']*')+(?:\\[(?:[^<\"'\\]]|\"[^\"]*\"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\\]\\s*)?>/i,\n      greedy: true,\n      inside: {\n        'internal-subset': {\n          pattern: /(^[^\\[]*\\[)[\\s\\S]+(?=\\]>$)/,\n          lookbehind: true,\n          greedy: true,\n          inside: null // see below\n        },\n        string: {\n          pattern: /\"[^\"]*\"|'[^']*'/,\n          greedy: true\n        },\n        punctuation: /^<!|>$|[[\\]]/,\n        'doctype-tag': /^DOCTYPE/i,\n        name: /[^\\s<>'\"]+/\n      }\n    },\n    cdata: {\n      pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n      greedy: true\n    },\n    tag: {\n      pattern:\n        /<\\/?(?!\\d)[^\\s>\\/=$<%]+(?:\\s(?:\\s*[^\\s>\\/=]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))|(?=[\\s/>])))+)?\\s*\\/?>/,\n      greedy: true,\n      inside: {\n        tag: {\n          pattern: /^<\\/?[^\\s>\\/]+/,\n          inside: {\n            punctuation: /^<\\/?/,\n            namespace: /^[^\\s>\\/:]+:/\n          }\n        },\n        'special-attr': [],\n        'attr-value': {\n          pattern: /=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+)/,\n          inside: {\n            punctuation: [\n              {\n                pattern: /^=/,\n                alias: 'attr-equals'\n              },\n              /\"|'/\n            ]\n          }\n        },\n        punctuation: /\\/?>/,\n        'attr-name': {\n          pattern: /[^\\s>\\/]+/,\n          inside: {\n            namespace: /^[^\\s>\\/:]+:/\n          }\n        }\n      }\n    },\n    entity: [\n      {\n        pattern: /&[\\da-z]{1,8};/i,\n        alias: 'named-entity'\n      },\n      /&#x?[\\da-f]{1,8};/i\n    ]\n  }\n  Prism.languages.markup['tag'].inside['attr-value'].inside['entity'] =\n    Prism.languages.markup['entity']\n  Prism.languages.markup['doctype'].inside['internal-subset'].inside =\n    Prism.languages.markup // Plugin to make entity title show the real entity, idea by Roman Komarov\n  Prism.hooks.add('wrap', function (env) {\n    if (env.type === 'entity') {\n      env.attributes['title'] = env.content.value.replace(/&amp;/, '&')\n    }\n  })\n  Object.defineProperty(Prism.languages.markup.tag, 'addInlined', {\n    /**\n     * Adds an inlined language to markup.\n     *\n     * An example of an inlined language is CSS with `<style>` tags.\n     *\n     * @param {string} tagName The name of the tag that contains the inlined language. This name will be treated as\n     * case insensitive.\n     * @param {string} lang The language key.\n     * @example\n     * addInlined('style', 'css');\n     */\n    value: function addInlined(tagName, lang) {\n      var includedCdataInside = {}\n      includedCdataInside['language-' + lang] = {\n        pattern: /(^<!\\[CDATA\\[)[\\s\\S]+?(?=\\]\\]>$)/i,\n        lookbehind: true,\n        inside: Prism.languages[lang]\n      }\n      includedCdataInside['cdata'] = /^<!\\[CDATA\\[|\\]\\]>$/i\n      var inside = {\n        'included-cdata': {\n          pattern: /<!\\[CDATA\\[[\\s\\S]*?\\]\\]>/i,\n          inside: includedCdataInside\n        }\n      }\n      inside['language-' + lang] = {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages[lang]\n      }\n      var def = {}\n      def[tagName] = {\n        pattern: RegExp(\n          /(<__[^>]*>)(?:<!\\[CDATA\\[(?:[^\\]]|\\](?!\\]>))*\\]\\]>|(?!<!\\[CDATA\\[)[\\s\\S])*?(?=<\\/__>)/.source.replace(\n            /__/g,\n            function () {\n              return tagName\n            }\n          ),\n          'i'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: inside\n      }\n      Prism.languages.insertBefore('markup', 'cdata', def)\n    }\n  })\n  Object.defineProperty(Prism.languages.markup.tag, 'addAttribute', {\n    /**\n     * Adds an pattern to highlight languages embedded in HTML attributes.\n     *\n     * An example of an inlined language is CSS with `style` attributes.\n     *\n     * @param {string} attrName The name of the tag that contains the inlined language. This name will be treated as\n     * case insensitive.\n     * @param {string} lang The language key.\n     * @example\n     * addAttribute('style', 'css');\n     */\n    value: function (attrName, lang) {\n      Prism.languages.markup.tag.inside['special-attr'].push({\n        pattern: RegExp(\n          /(^|[\"'\\s])/.source +\n            '(?:' +\n            attrName +\n            ')' +\n            /\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+(?=[\\s>]))/.source,\n          'i'\n        ),\n        lookbehind: true,\n        inside: {\n          'attr-name': /^[^\\s=]+/,\n          'attr-value': {\n            pattern: /=[\\s\\S]+/,\n            inside: {\n              value: {\n                pattern: /(^=\\s*([\"']|(?![\"'])))\\S[\\s\\S]*(?=\\2$)/,\n                lookbehind: true,\n                alias: [lang, 'language-' + lang],\n                inside: Prism.languages[lang]\n              },\n              punctuation: [\n                {\n                  pattern: /^=/,\n                  alias: 'attr-equals'\n                },\n                /\"|'/\n              ]\n            }\n          }\n        }\n      })\n    }\n  })\n  Prism.languages.html = Prism.languages.markup\n  Prism.languages.mathml = Prism.languages.markup\n  Prism.languages.svg = Prism.languages.markup\n  Prism.languages.xml = Prism.languages.extend('markup', {})\n  Prism.languages.ssml = Prism.languages.xml\n  Prism.languages.atom = Prism.languages.xml\n  Prism.languages.rss = Prism.languages.xml\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAQ,KAAK;AACvE,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS;AAAA,QACvB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SAAS;AAAA;AAAA,UAEP,SACE;AAAA,UACF,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,mBAAmB;AAAA,cACjB,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,QAAQ;AAAA;AAAA,YACV;AAAA,YACA,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,YACA,aAAa;AAAA,YACb,eAAe;AAAA,YACf,MAAM;AAAA,UACR;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,KAAK;AAAA,UACH,SACE;AAAA,UACF,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,KAAK;AAAA,cACH,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,aAAa;AAAA,gBACb,WAAW;AAAA,cACb;AAAA,YACF;AAAA,YACA,gBAAgB,CAAC;AAAA,YACjB,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,aAAa;AAAA,kBACX;AAAA,oBACE,SAAS;AAAA,oBACT,OAAO;AAAA,kBACT;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,YACA,aAAa;AAAA,YACb,aAAa;AAAA,cACX,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU,OAAO,KAAK,EAAE,OAAO,YAAY,EAAE,OAAO,QAAQ,IAChE,MAAM,UAAU,OAAO,QAAQ;AACjC,YAAM,UAAU,OAAO,SAAS,EAAE,OAAO,iBAAiB,EAAE,SAC1D,MAAM,UAAU;AAClB,YAAM,MAAM,IAAI,QAAQ,SAAU,KAAK;AACrC,YAAI,IAAI,SAAS,UAAU;AACzB,cAAI,WAAW,OAAO,IAAI,IAAI,QAAQ,MAAM,QAAQ,SAAS,GAAG;AAAA,QAClE;AAAA,MACF,CAAC;AACD,aAAO,eAAe,MAAM,UAAU,OAAO,KAAK,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAY9D,OAAO,SAAS,WAAW,SAAS,MAAM;AACxC,cAAI,sBAAsB,CAAC;AAC3B,8BAAoB,cAAc,IAAI,IAAI;AAAA,YACxC,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ,MAAM,UAAU,IAAI;AAAA,UAC9B;AACA,8BAAoB,OAAO,IAAI;AAC/B,cAAI,SAAS;AAAA,YACX,kBAAkB;AAAA,cAChB,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,UACF;AACA,iBAAO,cAAc,IAAI,IAAI;AAAA,YAC3B,SAAS;AAAA,YACT,QAAQ,MAAM,UAAU,IAAI;AAAA,UAC9B;AACA,cAAI,MAAM,CAAC;AACX,cAAI,OAAO,IAAI;AAAA,YACb,SAAS;AAAA,cACP,wFAAwF,OAAO;AAAA,gBAC7F;AAAA,gBACA,WAAY;AACV,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,cACA;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR;AAAA,UACF;AACA,gBAAM,UAAU,aAAa,UAAU,SAAS,GAAG;AAAA,QACrD;AAAA,MACF,CAAC;AACD,aAAO,eAAe,MAAM,UAAU,OAAO,KAAK,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAYhE,OAAO,SAAU,UAAU,MAAM;AAC/B,gBAAM,UAAU,OAAO,IAAI,OAAO,cAAc,EAAE,KAAK;AAAA,YACrD,SAAS;AAAA,cACP,aAAa,SACX,QACA,WACA,MACA,iDAAiD;AAAA,cACnD;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,cACb,cAAc;AAAA,gBACZ,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,OAAO;AAAA,oBACL,SAAS;AAAA,oBACT,YAAY;AAAA,oBACZ,OAAO,CAAC,MAAM,cAAc,IAAI;AAAA,oBAChC,QAAQ,MAAM,UAAU,IAAI;AAAA,kBAC9B;AAAA,kBACA,aAAa;AAAA,oBACX;AAAA,sBACE,SAAS;AAAA,sBACT,OAAO;AAAA,oBACT;AAAA,oBACA;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,YAAM,UAAU,OAAO,MAAM,UAAU;AACvC,YAAM,UAAU,SAAS,MAAM,UAAU;AACzC,YAAM,UAAU,MAAM,MAAM,UAAU;AACtC,YAAM,UAAU,MAAM,MAAM,UAAU,OAAO,UAAU,CAAC,CAAC;AACzD,YAAM,UAAU,OAAO,MAAM,UAAU;AACvC,YAAM,UAAU,OAAO,MAAM,UAAU;AACvC,YAAM,UAAU,MAAM,MAAM,UAAU;AAAA,IACxC;AAAA;AAAA;", "names": []}