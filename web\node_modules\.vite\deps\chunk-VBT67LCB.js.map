{"version": 3, "sources": ["../../refractor/lang/css-extras.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = cssExtras\ncssExtras.displayName = 'cssExtras'\ncssExtras.aliases = []\nfunction cssExtras(Prism) {\n  ;(function (Prism) {\n    var string = /(\"|')(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/\n    var selectorInside\n    Prism.languages.css.selector = {\n      pattern: Prism.languages.css.selector.pattern,\n      lookbehind: true,\n      inside: (selectorInside = {\n        'pseudo-element':\n          /:(?:after|before|first-letter|first-line|selection)|::[-\\w]+/,\n        'pseudo-class': /:[-\\w]+/,\n        class: /\\.[-\\w]+/,\n        id: /#[-\\w]+/,\n        attribute: {\n          pattern: RegExp('\\\\[(?:[^[\\\\]\"\\']|' + string.source + ')*\\\\]'),\n          greedy: true,\n          inside: {\n            punctuation: /^\\[|\\]$/,\n            'case-sensitivity': {\n              pattern: /(\\s)[si]$/i,\n              lookbehind: true,\n              alias: 'keyword'\n            },\n            namespace: {\n              pattern: /^(\\s*)(?:(?!\\s)[-*\\w\\xA0-\\uFFFF])*\\|(?!=)/,\n              lookbehind: true,\n              inside: {\n                punctuation: /\\|$/\n              }\n            },\n            'attr-name': {\n              pattern: /^(\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+/,\n              lookbehind: true\n            },\n            'attr-value': [\n              string,\n              {\n                pattern: /(=\\s*)(?:(?!\\s)[-\\w\\xA0-\\uFFFF])+(?=\\s*$)/,\n                lookbehind: true\n              }\n            ],\n            operator: /[|~*^$]?=/\n          }\n        },\n        'n-th': [\n          {\n            pattern: /(\\(\\s*)[+-]?\\d*[\\dn](?:\\s*[+-]\\s*\\d+)?(?=\\s*\\))/,\n            lookbehind: true,\n            inside: {\n              number: /[\\dn]+/,\n              operator: /[+-]/\n            }\n          },\n          {\n            pattern: /(\\(\\s*)(?:even|odd)(?=\\s*\\))/i,\n            lookbehind: true\n          }\n        ],\n        combinator: />|\\+|~|\\|\\|/,\n        // the `tag` token has been existed and removed.\n        // because we can't find a perfect tokenize to match it.\n        // if you want to add it, please read https://github.com/PrismJS/prism/pull/2373 first.\n        punctuation: /[(),]/\n      })\n    }\n    Prism.languages.css['atrule'].inside['selector-function-argument'].inside =\n      selectorInside\n    Prism.languages.insertBefore('css', 'property', {\n      variable: {\n        pattern:\n          /(^|[^-\\w\\xA0-\\uFFFF])--(?!\\s)[-_a-z\\xA0-\\uFFFF](?:(?!\\s)[-\\w\\xA0-\\uFFFF])*/i,\n        lookbehind: true\n      }\n    })\n    var unit = {\n      pattern: /(\\b\\d+)(?:%|[a-z]+(?![\\w-]))/,\n      lookbehind: true\n    } // 123 -123 .123 -.123 12.3 -12.3\n    var number = {\n      pattern: /(^|[^\\w.-])-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)/,\n      lookbehind: true\n    }\n    Prism.languages.insertBefore('css', 'function', {\n      operator: {\n        pattern: /(\\s)[+\\-*\\/](?=\\s)/,\n        lookbehind: true\n      },\n      // CAREFUL!\n      // Previewers and Inline color use hexcode and color.\n      hexcode: {\n        pattern: /\\B#[\\da-f]{3,8}\\b/i,\n        alias: 'color'\n      },\n      color: [\n        {\n          pattern:\n            /(^|[^\\w-])(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)(?![\\w-])/i,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /\\b(?:hsl|rgb)\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*\\)\\B|\\b(?:hsl|rgb)a\\(\\s*\\d{1,3}\\s*,\\s*\\d{1,3}%?\\s*,\\s*\\d{1,3}%?\\s*,\\s*(?:0|0?\\.\\d+|1)\\s*\\)\\B/i,\n          inside: {\n            unit: unit,\n            number: number,\n            function: /[\\w-]+(?=\\()/,\n            punctuation: /[(),]/\n          }\n        }\n      ],\n      // it's important that there is no boundary assertion after the hex digits\n      entity: /\\\\[\\da-f]{1,8}/i,\n      unit: unit,\n      number: number\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,cAAU,cAAc;AACxB,cAAU,UAAU,CAAC;AACrB,aAAS,UAAU,OAAO;AACxB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,SAAS;AACb,YAAI;AACJ,QAAAA,OAAM,UAAU,IAAI,WAAW;AAAA,UAC7B,SAASA,OAAM,UAAU,IAAI,SAAS;AAAA,UACtC,YAAY;AAAA,UACZ,QAAS,iBAAiB;AAAA,YACxB,kBACE;AAAA,YACF,gBAAgB;AAAA,YAChB,OAAO;AAAA,YACP,IAAI;AAAA,YACJ,WAAW;AAAA,cACT,SAAS,OAAO,qBAAsB,OAAO,SAAS,OAAO;AAAA,cAC7D,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,aAAa;AAAA,gBACb,oBAAoB;AAAA,kBAClB,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,OAAO;AAAA,gBACT;AAAA,gBACA,WAAW;AAAA,kBACT,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQ;AAAA,oBACN,aAAa;AAAA,kBACf;AAAA,gBACF;AAAA,gBACA,aAAa;AAAA,kBACX,SAAS;AAAA,kBACT,YAAY;AAAA,gBACd;AAAA,gBACA,cAAc;AAAA,kBACZ;AAAA,kBACA;AAAA,oBACE,SAAS;AAAA,oBACT,YAAY;AAAA,kBACd;AAAA,gBACF;AAAA,gBACA,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,cACN;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,QAAQ;AAAA,kBACR,UAAU;AAAA,gBACZ;AAAA,cACF;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,YACF;AAAA,YACA,YAAY;AAAA;AAAA;AAAA;AAAA,YAIZ,aAAa;AAAA,UACf;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,IAAI,QAAQ,EAAE,OAAO,4BAA4B,EAAE,SACjE;AACF,QAAAA,OAAM,UAAU,aAAa,OAAO,YAAY;AAAA,UAC9C,UAAU;AAAA,YACR,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,QACF,CAAC;AACD,YAAI,OAAO;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AACA,YAAI,SAAS;AAAA,UACX,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AACA,QAAAA,OAAM,UAAU,aAAa,OAAO,YAAY;AAAA,UAC9C,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA;AAAA;AAAA,UAGA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,OAAO;AAAA,YACL;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,QAAQ;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA,UAAU;AAAA,gBACV,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}