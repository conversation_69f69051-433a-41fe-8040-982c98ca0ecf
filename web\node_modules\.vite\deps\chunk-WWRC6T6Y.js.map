{"version": 3, "sources": ["../../refractor/lang/groovy.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = groovy\ngroovy.displayName = 'groovy'\ngroovy.aliases = []\nfunction groovy(Prism) {\n  Prism.languages.groovy = Prism.languages.extend('clike', {\n    string: [\n      {\n        // https://groovy-lang.org/syntax.html#_dollar_slashy_string\n        pattern:\n          /(\"\"\"|''')(?:[^\\\\]|\\\\[\\s\\S])*?\\1|\\$\\/(?:[^/$]|\\$(?:[/$]|(?![/$]))|\\/(?!\\$))*\\/\\$/,\n        greedy: true\n      },\n      {\n        // TODO: Slash strings (e.g. /foo/) can contain line breaks but this will cause a lot of trouble with\n        // simple division (see JS regex), so find a fix maybe?\n        pattern: /([\"'/])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        greedy: true\n      }\n    ],\n    keyword:\n      /\\b(?:abstract|as|assert|boolean|break|byte|case|catch|char|class|const|continue|def|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|in|instanceof|int|interface|long|native|new|package|private|protected|public|return|short|static|strictfp|super|switch|synchronized|this|throw|throws|trait|transient|try|void|volatile|while)\\b/,\n    number:\n      /\\b(?:0b[01_]+|0x[\\da-f_]+(?:\\.[\\da-f_p\\-]+)?|[\\d_]+(?:\\.[\\d_]+)?(?:e[+-]?\\d+)?)[glidf]?\\b/i,\n    operator: {\n      pattern:\n        /(^|[^.])(?:~|==?~?|\\?[.:]?|\\*(?:[.=]|\\*=?)?|\\.[@&]|\\.\\.<|\\.\\.(?!\\.)|-[-=>]?|\\+[+=]?|!=?|<(?:<=?|=>?)?|>(?:>>?=?|=)?|&[&=]?|\\|[|=]?|\\/=?|\\^=?|%=?)/,\n      lookbehind: true\n    },\n    punctuation: /\\.+|[{}[\\];(),:$]/\n  })\n  Prism.languages.insertBefore('groovy', 'string', {\n    shebang: {\n      pattern: /#!.+/,\n      alias: 'comment'\n    }\n  })\n  Prism.languages.insertBefore('groovy', 'punctuation', {\n    'spock-block': /\\b(?:and|cleanup|expect|given|setup|then|when|where):/\n  })\n  Prism.languages.insertBefore('groovy', 'function', {\n    annotation: {\n      pattern: /(^|[^.])@\\w+/,\n      lookbehind: true,\n      alias: 'punctuation'\n    }\n  }) // Handle string interpolation\n  Prism.hooks.add('wrap', function (env) {\n    if (env.language === 'groovy' && env.type === 'string') {\n      var delimiter = env.content.value[0]\n      if (delimiter != \"'\") {\n        var pattern = /([^\\\\])(?:\\$(?:\\{.*?\\}|[\\w.]+))/\n        if (delimiter === '$') {\n          pattern = /([^\\$])(?:\\$(?:\\{.*?\\}|[\\w.]+))/\n        } // To prevent double HTML-encoding we have to decode env.content first\n        env.content.value = env.content.value\n          .replace(/&lt;/g, '<')\n          .replace(/&amp;/g, '&')\n        env.content = Prism.highlight(env.content.value, {\n          expression: {\n            pattern: pattern,\n            lookbehind: true,\n            inside: Prism.languages.groovy\n          }\n        })\n        env.classes.push(delimiter === '/' ? 'regex' : 'gstring')\n      }\n    }\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS,MAAM,UAAU,OAAO,SAAS;AAAA,QACvD,QAAQ;AAAA,UACN;AAAA;AAAA,YAEE,SACE;AAAA,YACF,QAAQ;AAAA,UACV;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,SACE;AAAA,QACF,QACE;AAAA,QACF,UAAU;AAAA,UACR,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AACD,YAAM,UAAU,aAAa,UAAU,UAAU;AAAA,QAC/C,SAAS;AAAA,UACP,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,UAAU,eAAe;AAAA,QACpD,eAAe;AAAA,MACjB,CAAC;AACD,YAAM,UAAU,aAAa,UAAU,YAAY;AAAA,QACjD,YAAY;AAAA,UACV,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,YAAM,MAAM,IAAI,QAAQ,SAAU,KAAK;AACrC,YAAI,IAAI,aAAa,YAAY,IAAI,SAAS,UAAU;AACtD,cAAI,YAAY,IAAI,QAAQ,MAAM,CAAC;AACnC,cAAI,aAAa,KAAK;AACpB,gBAAI,UAAU;AACd,gBAAI,cAAc,KAAK;AACrB,wBAAU;AAAA,YACZ;AACA,gBAAI,QAAQ,QAAQ,IAAI,QAAQ,MAC7B,QAAQ,SAAS,GAAG,EACpB,QAAQ,UAAU,GAAG;AACxB,gBAAI,UAAU,MAAM,UAAU,IAAI,QAAQ,OAAO;AAAA,cAC/C,YAAY;AAAA,gBACV;AAAA,gBACA,YAAY;AAAA,gBACZ,QAAQ,MAAM,UAAU;AAAA,cAC1B;AAAA,YACF,CAAC;AACD,gBAAI,QAAQ,KAAK,cAAc,MAAM,UAAU,SAAS;AAAA,UAC1D;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}