{"version": 3, "sources": ["../../refractor/lang/javadoc.js"], "sourcesContent": ["'use strict'\nvar refractorJava = require('./java.js')\nvar refractorJavadoclike = require('./javadoclike.js')\nmodule.exports = javadoc\njavadoc.displayName = 'javadoc'\njavadoc.aliases = []\nfunction javadoc(Prism) {\n  Prism.register(refractorJava)\n  Prism.register(refractorJavadoclike)\n  ;(function (Prism) {\n    var codeLinePattern = /(^(?:[\\t ]*(?:\\*\\s*)*))[^*\\s].*$/m\n    var memberReference = /#\\s*\\w+(?:\\s*\\([^()]*\\))?/.source\n    var reference =\n      /(?:\\b[a-zA-Z]\\w+\\s*\\.\\s*)*\\b[A-Z]\\w*(?:\\s*<mem>)?|<mem>/.source.replace(\n        /<mem>/g,\n        function () {\n          return memberReference\n        }\n      )\n    Prism.languages.javadoc = Prism.languages.extend('javadoclike', {})\n    Prism.languages.insertBefore('javadoc', 'keyword', {\n      reference: {\n        pattern: RegExp(\n          /(@(?:exception|link|linkplain|see|throws|value)\\s+(?:\\*\\s*)?)/\n            .source +\n            '(?:' +\n            reference +\n            ')'\n        ),\n        lookbehind: true,\n        inside: {\n          function: {\n            pattern: /(#\\s*)\\w+(?=\\s*\\()/,\n            lookbehind: true\n          },\n          field: {\n            pattern: /(#\\s*)\\w+/,\n            lookbehind: true\n          },\n          namespace: {\n            pattern: /\\b(?:[a-z]\\w*\\s*\\.\\s*)+/,\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          'class-name': /\\b[A-Z]\\w*/,\n          keyword: Prism.languages.java.keyword,\n          punctuation: /[#()[\\],.]/\n        }\n      },\n      'class-name': {\n        // @param <T> the first generic type parameter\n        pattern: /(@param\\s+)<[A-Z]\\w*>/,\n        lookbehind: true,\n        inside: {\n          punctuation: /[.<>]/\n        }\n      },\n      'code-section': [\n        {\n          pattern:\n            /(\\{@code\\s+(?!\\s))(?:[^\\s{}]|\\s+(?![\\s}])|\\{(?:[^{}]|\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\})*\\})+(?=\\s*\\})/,\n          lookbehind: true,\n          inside: {\n            code: {\n              // there can't be any HTML inside of {@code} tags\n              pattern: codeLinePattern,\n              lookbehind: true,\n              inside: Prism.languages.java,\n              alias: 'language-java'\n            }\n          }\n        },\n        {\n          pattern:\n            /(<(code|pre|tt)>(?!<code>)\\s*)\\S(?:\\S|\\s+\\S)*?(?=\\s*<\\/\\2>)/,\n          lookbehind: true,\n          inside: {\n            line: {\n              pattern: codeLinePattern,\n              lookbehind: true,\n              inside: {\n                // highlight HTML tags and entities\n                tag: Prism.languages.markup.tag,\n                entity: Prism.languages.markup.entity,\n                code: {\n                  // everything else is Java code\n                  pattern: /.+/,\n                  inside: Prism.languages.java,\n                  alias: 'language-java'\n                }\n              }\n            }\n          }\n        }\n      ],\n      tag: Prism.languages.markup.tag,\n      entity: Prism.languages.markup.entity\n    })\n    Prism.languages.javadoclike.addSupport('java', Prism.languages.javadoc)\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AACA,QAAI,gBAAgB;AACpB,QAAI,uBAAuB;AAC3B,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AACtB,YAAM,SAAS,aAAa;AAC5B,YAAM,SAAS,oBAAoB;AAClC,OAAC,SAAUA,QAAO;AACjB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB,4BAA4B;AAClD,YAAI,YACF,0DAA0D,OAAO;AAAA,UAC/D;AAAA,UACA,WAAY;AACV,mBAAO;AAAA,UACT;AAAA,QACF;AACF,QAAAA,OAAM,UAAU,UAAUA,OAAM,UAAU,OAAO,eAAe,CAAC,CAAC;AAClE,QAAAA,OAAM,UAAU,aAAa,WAAW,WAAW;AAAA,UACjD,WAAW;AAAA,YACT,SAAS;AAAA,cACP,gEACG,SACD,QACA,YACA;AAAA,YACJ;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,UAAU;AAAA,gBACR,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA,OAAO;AAAA,gBACL,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,cAAc;AAAA,cACd,SAASA,OAAM,UAAU,KAAK;AAAA,cAC9B,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,cAAc;AAAA;AAAA,YAEZ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,gBAAgB;AAAA,YACd;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,MAAM;AAAA;AAAA,kBAEJ,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQA,OAAM,UAAU;AAAA,kBACxB,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,MAAM;AAAA,kBACJ,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQ;AAAA;AAAA,oBAEN,KAAKA,OAAM,UAAU,OAAO;AAAA,oBAC5B,QAAQA,OAAM,UAAU,OAAO;AAAA,oBAC/B,MAAM;AAAA;AAAA,sBAEJ,SAAS;AAAA,sBACT,QAAQA,OAAM,UAAU;AAAA,sBACxB,OAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAKA,OAAM,UAAU,OAAO;AAAA,UAC5B,QAAQA,OAAM,UAAU,OAAO;AAAA,QACjC,CAAC;AACD,QAAAA,OAAM,UAAU,YAAY,WAAW,QAAQA,OAAM,UAAU,OAAO;AAAA,MACxE,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}