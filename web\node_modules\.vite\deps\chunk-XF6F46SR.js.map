{"version": 3, "sources": ["../../highlight.js/lib/languages/livescript.js"], "sourcesContent": ["const KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // JS handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\nconst TYPES = [\n  \"Intl\",\n  \"DataView\",\n  \"Number\",\n  \"Math\",\n  \"Date\",\n  \"String\",\n  \"RegExp\",\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Error\",\n  \"Symbol\",\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  \"Proxy\",\n  \"Reflect\",\n  \"JSON\",\n  \"Promise\",\n  \"Float64Array\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Int8Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"Float32Array\",\n  \"Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"ArrayBuffer\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  \"BigInt\"\n];\n\nconst ERROR_TYPES = [\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  BUILT_IN_VARIABLES,\n  TYPES,\n  ERROR_TYPES\n);\n\n/*\nLanguage: LiveScript\nAuthor: Taneli Vatanen <<EMAIL>>\nContributors: Jen Evers-Corvina <<EMAIL>>\nOrigin: coffeescript.js\nDescription: LiveScript is a programming language that transcompiles to JavaScript. For info about language see http://livescript.net/\nWebsite: https://livescript.net\nCategory: scripting\n*/\n\nfunction livescript(hljs) {\n  const LIVESCRIPT_BUILT_INS = [\n    'npm',\n    'print'\n  ];\n  const LIVESCRIPT_LITERALS = [\n    'yes',\n    'no',\n    'on',\n    'off',\n    'it',\n    'that',\n    'void'\n  ];\n  const LIVESCRIPT_KEYWORDS = [\n    'then',\n    'unless',\n    'until',\n    'loop',\n    'of',\n    'by',\n    'when',\n    'and',\n    'or',\n    'is',\n    'isnt',\n    'not',\n    'it',\n    'that',\n    'otherwise',\n    'from',\n    'to',\n    'til',\n    'fallthrough',\n    'case',\n    'enum',\n    'native',\n    'list',\n    'map',\n    '__hasProp',\n    '__extends',\n    '__slice',\n    '__bind',\n    '__indexOf'\n  ];\n  const KEYWORDS$1 = {\n    keyword: KEYWORDS.concat(LIVESCRIPT_KEYWORDS),\n    literal: LITERALS.concat(LIVESCRIPT_LITERALS),\n    built_in: BUILT_INS.concat(LIVESCRIPT_BUILT_INS)\n  };\n  const JS_IDENT_RE = '[A-Za-z$_](?:-[0-9A-Za-z$_]|[0-9A-Za-z$_])*';\n  const TITLE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: JS_IDENT_RE\n  });\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS$1\n  };\n  const SUBST_SIMPLE = {\n    className: 'subst',\n    begin: /#[A-Za-z$_]/,\n    end: /(?:-[0-9A-Za-z$_]|[0-9A-Za-z$_])*/,\n    keywords: KEYWORDS$1\n  };\n  const EXPRESSIONS = [\n    hljs.BINARY_NUMBER_MODE,\n    {\n      className: 'number',\n      begin: '(\\\\b0[xX][a-fA-F0-9_]+)|(\\\\b\\\\d(\\\\d|_\\\\d)*(\\\\.(\\\\d(\\\\d|_\\\\d)*)?)?(_*[eE]([-+]\\\\d(_\\\\d|\\\\d)*)?)?[_a-z]*)',\n      relevance: 0,\n      starts: {\n        end: '(\\\\s*/)?',\n        relevance: 0\n      } // a number tries to eat the following slash to prevent treating it as a regexp\n    },\n    {\n      className: 'string',\n      variants: [\n        {\n          begin: /'''/,\n          end: /'''/,\n          contains: [hljs.BACKSLASH_ESCAPE]\n        },\n        {\n          begin: /'/,\n          end: /'/,\n          contains: [hljs.BACKSLASH_ESCAPE]\n        },\n        {\n          begin: /\"\"\"/,\n          end: /\"\"\"/,\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST,\n            SUBST_SIMPLE\n          ]\n        },\n        {\n          begin: /\"/,\n          end: /\"/,\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST,\n            SUBST_SIMPLE\n          ]\n        },\n        {\n          begin: /\\\\/,\n          end: /(\\s|$)/,\n          excludeEnd: true\n        }\n      ]\n    },\n    {\n      className: 'regexp',\n      variants: [\n        {\n          begin: '//',\n          end: '//[gim]*',\n          contains: [\n            SUBST,\n            hljs.HASH_COMMENT_MODE\n          ]\n        },\n        {\n          // regex can't start with space to parse x / 2 / 3 as two divisions\n          // regex can't start with *, and it supports an \"illegal\" in the main mode\n          begin: /\\/(?![ *])(\\\\.|[^\\\\\\n])*?\\/[gim]*(?=\\W)/\n        }\n      ]\n    },\n    {\n      begin: '@' + JS_IDENT_RE\n    },\n    {\n      begin: '``',\n      end: '``',\n      excludeBegin: true,\n      excludeEnd: true,\n      subLanguage: 'javascript'\n    }\n  ];\n  SUBST.contains = EXPRESSIONS;\n\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    returnBegin: true,\n    /* We need another contained nameless mode to not have every nested\n    pair of parens to be called \"params\" */\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS$1,\n        contains: ['self'].concat(EXPRESSIONS)\n      }\n    ]\n  };\n\n  const SYMBOLS = {\n    begin: '(#=>|=>|\\\\|>>|-?->|!->)'\n  };\n\n  return {\n    name: 'LiveScript',\n    aliases: ['ls'],\n    keywords: KEYWORDS$1,\n    illegal: /\\/\\*/,\n    contains: EXPRESSIONS.concat([\n      hljs.COMMENT('\\\\/\\\\*', '\\\\*\\\\/'),\n      hljs.HASH_COMMENT_MODE,\n      SYMBOLS, // relevance booster\n      {\n        className: 'function',\n        contains: [\n          TITLE,\n          PARAMS\n        ],\n        returnBegin: true,\n        variants: [\n          {\n            begin: '(' + JS_IDENT_RE + '\\\\s*(?:=|:=)\\\\s*)?(\\\\(.*\\\\)\\\\s*)?\\\\B->\\\\*?',\n            end: '->\\\\*?'\n          },\n          {\n            begin: '(' + JS_IDENT_RE + '\\\\s*(?:=|:=)\\\\s*)?!?(\\\\(.*\\\\)\\\\s*)?\\\\B[-~]{1,2}>\\\\*?',\n            end: '[-~]{1,2}>\\\\*?'\n          },\n          {\n            begin: '(' + JS_IDENT_RE + '\\\\s*(?:=|:=)\\\\s*)?(\\\\(.*\\\\)\\\\s*)?\\\\B!?[-~]{1,2}>\\\\*?',\n            end: '!?[-~]{1,2}>\\\\*?'\n          }\n        ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class',\n        end: '$',\n        illegal: /[:=\"\\[\\]]/,\n        contains: [\n          {\n            beginKeywords: 'extends',\n            endsWithParent: true,\n            illegal: /[:=\"\\[\\]]/,\n            contains: [TITLE]\n          },\n          TITLE\n        ]\n      },\n      {\n        begin: JS_IDENT_RE + ':',\n        end: ':',\n        returnBegin: true,\n        returnEnd: true,\n        relevance: 0\n      }\n    ])\n  };\n}\n\nmodule.exports = livescript;\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,QAAM,WAAW;AAAA,MACf;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA,MAIA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,QAAQ;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,cAAc;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAM,qBAAqB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,IACF;AAEA,QAAM,YAAY,CAAC,EAAE;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAYA,aAAS,WAAW,MAAM;AACxB,YAAM,uBAAuB;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AACA,YAAM,sBAAsB;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,sBAAsB;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,aAAa;AAAA,QACjB,SAAS,SAAS,OAAO,mBAAmB;AAAA,QAC5C,SAAS,SAAS,OAAO,mBAAmB;AAAA,QAC5C,UAAU,UAAU,OAAO,oBAAoB;AAAA,MACjD;AACA,YAAM,cAAc;AACpB,YAAM,QAAQ,KAAK,QAAQ,KAAK,YAAY;AAAA,QAC1C,OAAO;AAAA,MACT,CAAC;AACD,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,cAAc;AAAA,QAClB,KAAK;AAAA,QACL;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,WAAW;AAAA,UACX,QAAQ;AAAA,YACN,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU,CAAC,KAAK,gBAAgB;AAAA,YAClC;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU,CAAC,KAAK,gBAAgB;AAAA,YAClC;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU;AAAA,gBACR,KAAK;AAAA,gBACL;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU;AAAA,gBACR,KAAK;AAAA,gBACL;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU;AAAA,gBACR;AAAA,gBACA,KAAK;AAAA,cACP;AAAA,YACF;AAAA,YACA;AAAA;AAAA;AAAA,cAGE,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,OAAO,MAAM;AAAA,QACf;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,aAAa;AAAA,QACf;AAAA,MACF;AACA,YAAM,WAAW;AAEjB,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,aAAa;AAAA;AAAA;AAAA,QAGb,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU,CAAC,MAAM,EAAE,OAAO,WAAW;AAAA,UACvC;AAAA,QACF;AAAA,MACF;AAEA,YAAM,UAAU;AAAA,QACd,OAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,IAAI;AAAA,QACd,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU,YAAY,OAAO;AAAA,UAC3B,KAAK,QAAQ,UAAU,QAAQ;AAAA,UAC/B,KAAK;AAAA,UACL;AAAA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,YACA,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,MAAM,cAAc;AAAA,gBAC3B,KAAK;AAAA,cACP;AAAA,cACA;AAAA,gBACE,OAAO,MAAM,cAAc;AAAA,gBAC3B,KAAK;AAAA,cACP;AAAA,cACA;AAAA,gBACE,OAAO,MAAM,cAAc;AAAA,gBAC3B,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,gBACf,gBAAgB;AAAA,gBAChB,SAAS;AAAA,gBACT,UAAU,CAAC,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO,cAAc;AAAA,YACrB,KAAK;AAAA,YACL,aAAa;AAAA,YACb,WAAW;AAAA,YACX,WAAW;AAAA,UACb;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}