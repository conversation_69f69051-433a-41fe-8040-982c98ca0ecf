{"version": 3, "sources": ["../../refractor/lang/puppet.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = puppet\npuppet.displayName = 'puppet'\npuppet.aliases = []\nfunction puppet(Prism) {\n  ;(function (Prism) {\n    Prism.languages.puppet = {\n      heredoc: [\n        // Matches the content of a quoted heredoc string (subject to interpolation)\n        {\n          pattern:\n            /(@\\(\"([^\"\\r\\n\\/):]+)\"(?:\\/[nrts$uL]*)?\\).*(?:\\r?\\n|\\r))(?:.*(?:\\r?\\n|\\r(?!\\n)))*?[ \\t]*(?:\\|[ \\t]*)?(?:-[ \\t]*)?\\2/,\n          lookbehind: true,\n          alias: 'string',\n          inside: {\n            // Matches the end tag\n            punctuation: /(?=\\S).*\\S(?= *$)/ // See interpolation below\n          }\n        }, // Matches the content of an unquoted heredoc string (no interpolation)\n        {\n          pattern:\n            /(@\\(([^\"\\r\\n\\/):]+)(?:\\/[nrts$uL]*)?\\).*(?:\\r?\\n|\\r))(?:.*(?:\\r?\\n|\\r(?!\\n)))*?[ \\t]*(?:\\|[ \\t]*)?(?:-[ \\t]*)?\\2/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'string',\n          inside: {\n            // Matches the end tag\n            punctuation: /(?=\\S).*\\S(?= *$)/\n          }\n        }, // Matches the start tag of heredoc strings\n        {\n          pattern: /@\\(\"?(?:[^\"\\r\\n\\/):]+)\"?(?:\\/[nrts$uL]*)?\\)/,\n          alias: 'string',\n          inside: {\n            punctuation: {\n              pattern: /(\\().+?(?=\\))/,\n              lookbehind: true\n            }\n          }\n        }\n      ],\n      'multiline-comment': {\n        pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n        lookbehind: true,\n        greedy: true,\n        alias: 'comment'\n      },\n      regex: {\n        // Must be prefixed with the keyword \"node\" or a non-word char\n        pattern:\n          /((?:\\bnode\\s+|[~=\\(\\[\\{,]\\s*|[=+]>\\s*|^\\s*))\\/(?:[^\\/\\\\]|\\\\[\\s\\S])+\\/(?:[imx]+\\b|\\B)/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          // Extended regexes must have the x flag. They can contain single-line comments.\n          'extended-regex': {\n            pattern: /^\\/(?:[^\\/\\\\]|\\\\[\\s\\S])+\\/[im]*x[im]*$/,\n            inside: {\n              comment: /#.*/\n            }\n          }\n        }\n      },\n      comment: {\n        pattern: /(^|[^\\\\])#.*/,\n        lookbehind: true,\n        greedy: true\n      },\n      string: {\n        // Allow for one nested level of double quotes inside interpolation\n        pattern:\n          /([\"'])(?:\\$\\{(?:[^'\"}]|([\"'])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2)+\\}|\\$(?!\\{)|(?!\\1)[^\\\\$]|\\\\[\\s\\S])*\\1/,\n        greedy: true,\n        inside: {\n          'double-quoted': {\n            pattern: /^\"[\\s\\S]*\"$/,\n            inside: {\n              // See interpolation below\n            }\n          }\n        }\n      },\n      variable: {\n        pattern: /\\$(?:::)?\\w+(?:::\\w+)*/,\n        inside: {\n          punctuation: /::/\n        }\n      },\n      'attr-name': /(?:\\b\\w+|\\*)(?=\\s*=>)/,\n      function: [\n        {\n          pattern: /(\\.)(?!\\d)\\w+/,\n          lookbehind: true\n        },\n        /\\b(?:contain|debug|err|fail|include|info|notice|realize|require|tag|warning)\\b|\\b(?!\\d)\\w+(?=\\()/\n      ],\n      number: /\\b(?:0x[a-f\\d]+|\\d+(?:\\.\\d+)?(?:e-?\\d+)?)\\b/i,\n      boolean: /\\b(?:false|true)\\b/,\n      // Includes words reserved for future use\n      keyword:\n        /\\b(?:application|attr|case|class|consumes|default|define|else|elsif|function|if|import|inherits|node|private|produces|type|undef|unless)\\b/,\n      datatype: {\n        pattern:\n          /\\b(?:Any|Array|Boolean|Callable|Catalogentry|Class|Collection|Data|Default|Enum|Float|Hash|Integer|NotUndef|Numeric|Optional|Pattern|Regexp|Resource|Runtime|Scalar|String|Struct|Tuple|Type|Undef|Variant)\\b/,\n        alias: 'symbol'\n      },\n      operator:\n        /=[=~>]?|![=~]?|<(?:<\\|?|[=~|-])?|>[>=]?|->?|~>|\\|>?>?|[*\\/%+?]|\\b(?:and|in|or)\\b/,\n      punctuation: /[\\[\\]{}().,;]|:+/\n    }\n    var interpolation = [\n      {\n        // Allow for one nested level of braces inside interpolation\n        pattern:\n          /(^|[^\\\\])\\$\\{(?:[^'\"{}]|\\{[^}]*\\}|([\"'])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2)+\\}/,\n        lookbehind: true,\n        inside: {\n          'short-variable': {\n            // Negative look-ahead prevent wrong highlighting of functions\n            pattern: /(^\\$\\{)(?!\\w+\\()(?:::)?\\w+(?:::\\w+)*/,\n            lookbehind: true,\n            alias: 'variable',\n            inside: {\n              punctuation: /::/\n            }\n          },\n          delimiter: {\n            pattern: /^\\$/,\n            alias: 'variable'\n          },\n          rest: Prism.languages.puppet\n        }\n      },\n      {\n        pattern: /(^|[^\\\\])\\$(?:::)?\\w+(?:::\\w+)*/,\n        lookbehind: true,\n        alias: 'variable',\n        inside: {\n          punctuation: /::/\n        }\n      }\n    ]\n    Prism.languages.puppet['heredoc'][0].inside.interpolation = interpolation\n    Prism.languages.puppet['string'].inside[\n      'double-quoted'\n    ].inside.interpolation = interpolation\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,SAAS;AAAA,UACvB,SAAS;AAAA;AAAA,YAEP;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ;AAAA;AAAA,gBAEN,aAAa;AAAA;AAAA,cACf;AAAA,YACF;AAAA;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,OAAO;AAAA,cACP,QAAQ;AAAA;AAAA,gBAEN,aAAa;AAAA,cACf;AAAA,YACF;AAAA;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,aAAa;AAAA,kBACX,SAAS;AAAA,kBACT,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,qBAAqB;AAAA,YACnB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,OAAO;AAAA;AAAA,YAEL,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA;AAAA,cAEN,kBAAkB;AAAA,gBAChB,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,SAAS;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA;AAAA,YAEN,SACE;AAAA,YACF,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,iBAAiB;AAAA,gBACf,SAAS;AAAA,gBACT,QAAQ;AAAA;AAAA,gBAER;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,UACR,SAAS;AAAA;AAAA,UAET,SACE;AAAA,UACF,UAAU;AAAA,YACR,SACE;AAAA,YACF,OAAO;AAAA,UACT;AAAA,UACA,UACE;AAAA,UACF,aAAa;AAAA,QACf;AACA,YAAI,gBAAgB;AAAA,UAClB;AAAA;AAAA,YAEE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,kBAAkB;AAAA;AAAA,gBAEhB,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,MAAMA,OAAM,UAAU;AAAA,YACxB;AAAA,UACF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,OAAO,SAAS,EAAE,CAAC,EAAE,OAAO,gBAAgB;AAC5D,QAAAA,OAAM,UAAU,OAAO,QAAQ,EAAE,OAC/B,eACF,EAAE,OAAO,gBAAgB;AAAA,MAC3B,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}