{"version": 3, "sources": ["../../highlight.js/lib/languages/stan.js"], "sourcesContent": ["/*\nLanguage: Stan\nDescription: The Stan probabilistic programming language\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://mc-stan.org/\nCategory: scientific\n*/\n\nfunction stan(hljs) {\n  // variable names cannot conflict with block identifiers\n  const BLOCKS = [\n    'functions',\n    'model',\n    'data',\n    'parameters',\n    'quantities',\n    'transformed',\n    'generated'\n  ];\n  const STATEMENTS = [\n    'for',\n    'in',\n    'if',\n    'else',\n    'while',\n    'break',\n    'continue',\n    'return'\n  ];\n  const SPECIAL_FUNCTIONS = [\n    'print',\n    'reject',\n    'increment_log_prob|10',\n    'integrate_ode|10',\n    'integrate_ode_rk45|10',\n    'integrate_ode_bdf|10',\n    'algebra_solver'\n  ];\n  const VAR_TYPES = [\n    'int',\n    'real',\n    'vector',\n    'ordered',\n    'positive_ordered',\n    'simplex',\n    'unit_vector',\n    'row_vector',\n    'matrix',\n    'cholesky_factor_corr|10',\n    'cholesky_factor_cov|10',\n    'corr_matrix|10',\n    'cov_matrix|10',\n    'void'\n  ];\n  const FUNCTIONS = [\n    'Phi',\n    'Phi_approx',\n    'abs',\n    'acos',\n    'acosh',\n    'algebra_solver',\n    'append_array',\n    'append_col',\n    'append_row',\n    'asin',\n    'asinh',\n    'atan',\n    'atan2',\n    'atanh',\n    'bernoulli_cdf',\n    'bernoulli_lccdf',\n    'bernoulli_lcdf',\n    'bernoulli_logit_lpmf',\n    'bernoulli_logit_rng',\n    'bernoulli_lpmf',\n    'bernoulli_rng',\n    'bessel_first_kind',\n    'bessel_second_kind',\n    'beta_binomial_cdf',\n    'beta_binomial_lccdf',\n    'beta_binomial_lcdf',\n    'beta_binomial_lpmf',\n    'beta_binomial_rng',\n    'beta_cdf',\n    'beta_lccdf',\n    'beta_lcdf',\n    'beta_lpdf',\n    'beta_rng',\n    'binary_log_loss',\n    'binomial_cdf',\n    'binomial_coefficient_log',\n    'binomial_lccdf',\n    'binomial_lcdf',\n    'binomial_logit_lpmf',\n    'binomial_lpmf',\n    'binomial_rng',\n    'block',\n    'categorical_logit_lpmf',\n    'categorical_logit_rng',\n    'categorical_lpmf',\n    'categorical_rng',\n    'cauchy_cdf',\n    'cauchy_lccdf',\n    'cauchy_lcdf',\n    'cauchy_lpdf',\n    'cauchy_rng',\n    'cbrt',\n    'ceil',\n    'chi_square_cdf',\n    'chi_square_lccdf',\n    'chi_square_lcdf',\n    'chi_square_lpdf',\n    'chi_square_rng',\n    'cholesky_decompose',\n    'choose',\n    'col',\n    'cols',\n    'columns_dot_product',\n    'columns_dot_self',\n    'cos',\n    'cosh',\n    'cov_exp_quad',\n    'crossprod',\n    'csr_extract_u',\n    'csr_extract_v',\n    'csr_extract_w',\n    'csr_matrix_times_vector',\n    'csr_to_dense_matrix',\n    'cumulative_sum',\n    'determinant',\n    'diag_matrix',\n    'diag_post_multiply',\n    'diag_pre_multiply',\n    'diagonal',\n    'digamma',\n    'dims',\n    'dirichlet_lpdf',\n    'dirichlet_rng',\n    'distance',\n    'dot_product',\n    'dot_self',\n    'double_exponential_cdf',\n    'double_exponential_lccdf',\n    'double_exponential_lcdf',\n    'double_exponential_lpdf',\n    'double_exponential_rng',\n    'e',\n    'eigenvalues_sym',\n    'eigenvectors_sym',\n    'erf',\n    'erfc',\n    'exp',\n    'exp2',\n    'exp_mod_normal_cdf',\n    'exp_mod_normal_lccdf',\n    'exp_mod_normal_lcdf',\n    'exp_mod_normal_lpdf',\n    'exp_mod_normal_rng',\n    'expm1',\n    'exponential_cdf',\n    'exponential_lccdf',\n    'exponential_lcdf',\n    'exponential_lpdf',\n    'exponential_rng',\n    'fabs',\n    'falling_factorial',\n    'fdim',\n    'floor',\n    'fma',\n    'fmax',\n    'fmin',\n    'fmod',\n    'frechet_cdf',\n    'frechet_lccdf',\n    'frechet_lcdf',\n    'frechet_lpdf',\n    'frechet_rng',\n    'gamma_cdf',\n    'gamma_lccdf',\n    'gamma_lcdf',\n    'gamma_lpdf',\n    'gamma_p',\n    'gamma_q',\n    'gamma_rng',\n    'gaussian_dlm_obs_lpdf',\n    'get_lp',\n    'gumbel_cdf',\n    'gumbel_lccdf',\n    'gumbel_lcdf',\n    'gumbel_lpdf',\n    'gumbel_rng',\n    'head',\n    'hypergeometric_lpmf',\n    'hypergeometric_rng',\n    'hypot',\n    'inc_beta',\n    'int_step',\n    'integrate_ode',\n    'integrate_ode_bdf',\n    'integrate_ode_rk45',\n    'inv',\n    'inv_Phi',\n    'inv_chi_square_cdf',\n    'inv_chi_square_lccdf',\n    'inv_chi_square_lcdf',\n    'inv_chi_square_lpdf',\n    'inv_chi_square_rng',\n    'inv_cloglog',\n    'inv_gamma_cdf',\n    'inv_gamma_lccdf',\n    'inv_gamma_lcdf',\n    'inv_gamma_lpdf',\n    'inv_gamma_rng',\n    'inv_logit',\n    'inv_sqrt',\n    'inv_square',\n    'inv_wishart_lpdf',\n    'inv_wishart_rng',\n    'inverse',\n    'inverse_spd',\n    'is_inf',\n    'is_nan',\n    'lbeta',\n    'lchoose',\n    'lgamma',\n    'lkj_corr_cholesky_lpdf',\n    'lkj_corr_cholesky_rng',\n    'lkj_corr_lpdf',\n    'lkj_corr_rng',\n    'lmgamma',\n    'lmultiply',\n    'log',\n    'log10',\n    'log1m',\n    'log1m_exp',\n    'log1m_inv_logit',\n    'log1p',\n    'log1p_exp',\n    'log2',\n    'log_determinant',\n    'log_diff_exp',\n    'log_falling_factorial',\n    'log_inv_logit',\n    'log_mix',\n    'log_rising_factorial',\n    'log_softmax',\n    'log_sum_exp',\n    'logistic_cdf',\n    'logistic_lccdf',\n    'logistic_lcdf',\n    'logistic_lpdf',\n    'logistic_rng',\n    'logit',\n    'lognormal_cdf',\n    'lognormal_lccdf',\n    'lognormal_lcdf',\n    'lognormal_lpdf',\n    'lognormal_rng',\n    'machine_precision',\n    'matrix_exp',\n    'max',\n    'mdivide_left_spd',\n    'mdivide_left_tri_low',\n    'mdivide_right_spd',\n    'mdivide_right_tri_low',\n    'mean',\n    'min',\n    'modified_bessel_first_kind',\n    'modified_bessel_second_kind',\n    'multi_gp_cholesky_lpdf',\n    'multi_gp_lpdf',\n    'multi_normal_cholesky_lpdf',\n    'multi_normal_cholesky_rng',\n    'multi_normal_lpdf',\n    'multi_normal_prec_lpdf',\n    'multi_normal_rng',\n    'multi_student_t_lpdf',\n    'multi_student_t_rng',\n    'multinomial_lpmf',\n    'multinomial_rng',\n    'multiply_log',\n    'multiply_lower_tri_self_transpose',\n    'neg_binomial_2_cdf',\n    'neg_binomial_2_lccdf',\n    'neg_binomial_2_lcdf',\n    'neg_binomial_2_log_lpmf',\n    'neg_binomial_2_log_rng',\n    'neg_binomial_2_lpmf',\n    'neg_binomial_2_rng',\n    'neg_binomial_cdf',\n    'neg_binomial_lccdf',\n    'neg_binomial_lcdf',\n    'neg_binomial_lpmf',\n    'neg_binomial_rng',\n    'negative_infinity',\n    'normal_cdf',\n    'normal_lccdf',\n    'normal_lcdf',\n    'normal_lpdf',\n    'normal_rng',\n    'not_a_number',\n    'num_elements',\n    'ordered_logistic_lpmf',\n    'ordered_logistic_rng',\n    'owens_t',\n    'pareto_cdf',\n    'pareto_lccdf',\n    'pareto_lcdf',\n    'pareto_lpdf',\n    'pareto_rng',\n    'pareto_type_2_cdf',\n    'pareto_type_2_lccdf',\n    'pareto_type_2_lcdf',\n    'pareto_type_2_lpdf',\n    'pareto_type_2_rng',\n    'pi',\n    'poisson_cdf',\n    'poisson_lccdf',\n    'poisson_lcdf',\n    'poisson_log_lpmf',\n    'poisson_log_rng',\n    'poisson_lpmf',\n    'poisson_rng',\n    'positive_infinity',\n    'pow',\n    'print',\n    'prod',\n    'qr_Q',\n    'qr_R',\n    'quad_form',\n    'quad_form_diag',\n    'quad_form_sym',\n    'rank',\n    'rayleigh_cdf',\n    'rayleigh_lccdf',\n    'rayleigh_lcdf',\n    'rayleigh_lpdf',\n    'rayleigh_rng',\n    'reject',\n    'rep_array',\n    'rep_matrix',\n    'rep_row_vector',\n    'rep_vector',\n    'rising_factorial',\n    'round',\n    'row',\n    'rows',\n    'rows_dot_product',\n    'rows_dot_self',\n    'scaled_inv_chi_square_cdf',\n    'scaled_inv_chi_square_lccdf',\n    'scaled_inv_chi_square_lcdf',\n    'scaled_inv_chi_square_lpdf',\n    'scaled_inv_chi_square_rng',\n    'sd',\n    'segment',\n    'sin',\n    'singular_values',\n    'sinh',\n    'size',\n    'skew_normal_cdf',\n    'skew_normal_lccdf',\n    'skew_normal_lcdf',\n    'skew_normal_lpdf',\n    'skew_normal_rng',\n    'softmax',\n    'sort_asc',\n    'sort_desc',\n    'sort_indices_asc',\n    'sort_indices_desc',\n    'sqrt',\n    'sqrt2',\n    'square',\n    'squared_distance',\n    'step',\n    'student_t_cdf',\n    'student_t_lccdf',\n    'student_t_lcdf',\n    'student_t_lpdf',\n    'student_t_rng',\n    'sub_col',\n    'sub_row',\n    'sum',\n    'tail',\n    'tan',\n    'tanh',\n    'target',\n    'tcrossprod',\n    'tgamma',\n    'to_array_1d',\n    'to_array_2d',\n    'to_matrix',\n    'to_row_vector',\n    'to_vector',\n    'trace',\n    'trace_gen_quad_form',\n    'trace_quad_form',\n    'trigamma',\n    'trunc',\n    'uniform_cdf',\n    'uniform_lccdf',\n    'uniform_lcdf',\n    'uniform_lpdf',\n    'uniform_rng',\n    'variance',\n    'von_mises_lpdf',\n    'von_mises_rng',\n    'weibull_cdf',\n    'weibull_lccdf',\n    'weibull_lcdf',\n    'weibull_lpdf',\n    'weibull_rng',\n    'wiener_lpdf',\n    'wishart_lpdf',\n    'wishart_rng'\n  ];\n  const DISTRIBUTIONS = [\n    'bernoulli',\n    'bernoulli_logit',\n    'beta',\n    'beta_binomial',\n    'binomial',\n    'binomial_logit',\n    'categorical',\n    'categorical_logit',\n    'cauchy',\n    'chi_square',\n    'dirichlet',\n    'double_exponential',\n    'exp_mod_normal',\n    'exponential',\n    'frechet',\n    'gamma',\n    'gaussian_dlm_obs',\n    'gumbel',\n    'hypergeometric',\n    'inv_chi_square',\n    'inv_gamma',\n    'inv_wishart',\n    'lkj_corr',\n    'lkj_corr_cholesky',\n    'logistic',\n    'lognormal',\n    'multi_gp',\n    'multi_gp_cholesky',\n    'multi_normal',\n    'multi_normal_cholesky',\n    'multi_normal_prec',\n    'multi_student_t',\n    'multinomial',\n    'neg_binomial',\n    'neg_binomial_2',\n    'neg_binomial_2_log',\n    'normal',\n    'ordered_logistic',\n    'pareto',\n    'pareto_type_2',\n    'poisson',\n    'poisson_log',\n    'rayleigh',\n    'scaled_inv_chi_square',\n    'skew_normal',\n    'student_t',\n    'uniform',\n    'von_mises',\n    'weibull',\n    'wiener',\n    'wishart'\n  ];\n\n  return {\n    name: 'Stan',\n    aliases: [ 'stanfuncs' ],\n    keywords: {\n      $pattern: hljs.IDENT_RE,\n      title: BLOCKS,\n      keyword: STATEMENTS.concat(VAR_TYPES).concat(SPECIAL_FUNCTIONS),\n      built_in: FUNCTIONS\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.COMMENT(\n        /#/,\n        /$/,\n        {\n          relevance: 0,\n          keywords: {\n            'meta-keyword': 'include'\n          }\n        }\n      ),\n      hljs.COMMENT(\n        /\\/\\*/,\n        /\\*\\//,\n        {\n          relevance: 0,\n          // highlight doc strings mentioned in Stan reference\n          contains: [\n            {\n              className: 'doctag',\n              begin: /@(return|param)/\n            }\n          ]\n        }\n      ),\n      {\n        // hack: in range constraints, lower must follow \"<\"\n        begin: /<\\s*lower\\s*=/,\n        keywords: 'lower'\n      },\n      {\n        // hack: in range constraints, upper must follow either , or <\n        // <lower = ..., upper = ...> or <upper = ...>\n        begin: /[<,]\\s*upper\\s*=/,\n        keywords: 'upper'\n      },\n      {\n        className: 'keyword',\n        begin: /\\btarget\\s*\\+=/,\n        relevance: 10\n      },\n      {\n        begin: '~\\\\s*(' + hljs.IDENT_RE + ')\\\\s*\\\\(',\n        keywords: DISTRIBUTIONS\n      },\n      {\n        className: 'number',\n        variants: [\n          {\n            begin: /\\b\\d+(?:\\.\\d*)?(?:[eE][+-]?\\d+)?/\n          },\n          {\n            begin: /\\.\\d+(?:[eE][+-]?\\d+)?\\b/\n          }\n        ],\n        relevance: 0\n      },\n      {\n        className: 'string',\n        begin: '\"',\n        end: '\"',\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = stan;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,KAAK,MAAM;AAElB,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,oBAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,gBAAgB;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,WAAY;AAAA,QACvB,UAAU;AAAA,UACR,UAAU,KAAK;AAAA,UACf,OAAO;AAAA,UACP,SAAS,WAAW,OAAO,SAAS,EAAE,OAAO,iBAAiB;AAAA,UAC9D,UAAU;AAAA,QACZ;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA,gBACR,gBAAgB;AAAA,cAClB;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA;AAAA,cAEX,UAAU;AAAA,gBACR;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,YACP,UAAU;AAAA,UACZ;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,OAAO;AAAA,YACP,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO,WAAW,KAAK,WAAW;AAAA,YAClC,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}