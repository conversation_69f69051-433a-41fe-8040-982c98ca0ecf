{"version": 3, "sources": ["../../highlight.js/lib/languages/rust.js"], "sourcesContent": ["/*\nLanguage: Rust\nAuthor: <PERSON><PERSON> <andrey.v<PERSON><PERSON>@gmail.com>\nContributors: Roman Shmatov <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.rust-lang.org\nCategory: common, system\n*/\n\nfunction rust(hljs) {\n  const NUM_SUFFIX = '([ui](8|16|32|64|128|size)|f(32|64))\\?';\n  const KEYWORDS =\n    'abstract as async await become box break const continue crate do dyn ' +\n    'else enum extern false final fn for if impl in let loop macro match mod ' +\n    'move mut override priv pub ref return self Self static struct super ' +\n    'trait true try type typeof unsafe unsized use virtual where while yield';\n  const BUILTINS =\n    // functions\n    'drop ' +\n    // types\n    'i8 i16 i32 i64 i128 isize ' +\n    'u8 u16 u32 u64 u128 usize ' +\n    'f32 f64 ' +\n    'str char bool ' +\n    'Box Option Result String Vec ' +\n    // traits\n    'Copy Send Sized Sync Drop Fn FnMut FnOnce ToOwned Clone Debug ' +\n    'PartialEq PartialOrd Eq Ord AsRef AsMut Into From Default Iterator ' +\n    'Extend IntoIterator DoubleEndedIterator ExactSizeIterator ' +\n    'SliceConcatExt ToString ' +\n    // macros\n    'assert! assert_eq! bitflags! bytes! cfg! col! concat! concat_idents! ' +\n    'debug_assert! debug_assert_eq! env! panic! file! format! format_args! ' +\n    'include_bin! include_str! line! local_data_key! module_path! ' +\n    'option_env! print! println! select! stringify! try! unimplemented! ' +\n    'unreachable! vec! write! writeln! macro_rules! assert_ne! debug_assert_ne!';\n  return {\n    name: 'Rust',\n    aliases: [ 'rs' ],\n    keywords: {\n      $pattern: hljs.IDENT_RE + '!?',\n      keyword:\n        KEYWORDS,\n      literal:\n        'true false Some None Ok Err',\n      built_in:\n        BUILTINS\n    },\n    illegal: '</',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.COMMENT('/\\\\*', '\\\\*/', {\n        contains: [ 'self' ]\n      }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        begin: /b?\"/,\n        illegal: null\n      }),\n      {\n        className: 'string',\n        variants: [\n          {\n            begin: /r(#*)\"(.|\\n)*?\"\\1(?!#)/\n          },\n          {\n            begin: /b?'\\\\?(x\\w{2}|u\\w{4}|U\\w{8}|.)'/\n          }\n        ]\n      },\n      {\n        className: 'symbol',\n        begin: /'[a-zA-Z_][a-zA-Z0-9_]*/\n      },\n      {\n        className: 'number',\n        variants: [\n          {\n            begin: '\\\\b0b([01_]+)' + NUM_SUFFIX\n          },\n          {\n            begin: '\\\\b0o([0-7_]+)' + NUM_SUFFIX\n          },\n          {\n            begin: '\\\\b0x([A-Fa-f0-9_]+)' + NUM_SUFFIX\n          },\n          {\n            begin: '\\\\b(\\\\d[\\\\d_]*(\\\\.[0-9_]+)?([eE][+-]?[0-9_]+)?)' +\n                   NUM_SUFFIX\n          }\n        ],\n        relevance: 0\n      },\n      {\n        className: 'function',\n        beginKeywords: 'fn',\n        end: '(\\\\(|<)',\n        excludeEnd: true,\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      {\n        className: 'meta',\n        begin: '#!?\\\\[',\n        end: '\\\\]',\n        contains: [\n          {\n            className: 'meta-string',\n            begin: /\"/,\n            end: /\"/\n          }\n        ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'type',\n        end: ';',\n        contains: [\n          hljs.inherit(hljs.UNDERSCORE_TITLE_MODE, {\n            endsParent: true\n          })\n        ],\n        illegal: '\\\\S'\n      },\n      {\n        className: 'class',\n        beginKeywords: 'trait enum struct union',\n        end: /\\{/,\n        contains: [\n          hljs.inherit(hljs.UNDERSCORE_TITLE_MODE, {\n            endsParent: true\n          })\n        ],\n        illegal: '[\\\\w\\\\d]'\n      },\n      {\n        begin: hljs.IDENT_RE + '::',\n        keywords: {\n          built_in: BUILTINS\n        }\n      },\n      {\n        begin: '->'\n      }\n    ]\n  };\n}\n\nmodule.exports = rust;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,KAAK,MAAM;AAClB,YAAM,aAAa;AACnB,YAAM,WACJ;AAIF,YAAM;AAAA;AAAA,QAEJ;AAAA;AAkBF,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,UAAU;AAAA,UACR,UAAU,KAAK,WAAW;AAAA,UAC1B,SACE;AAAA,UACF,SACE;AAAA,UACF,UACE;AAAA,QACJ;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK,QAAQ,QAAQ,QAAQ;AAAA,YAC3B,UAAU,CAAE,MAAO;AAAA,UACrB,CAAC;AAAA,UACD,KAAK,QAAQ,KAAK,mBAAmB;AAAA,YACnC,OAAO;AAAA,YACP,SAAS;AAAA,UACX,CAAC;AAAA,UACD;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,kBAAkB;AAAA,cAC3B;AAAA,cACA;AAAA,gBACE,OAAO,mBAAmB;AAAA,cAC5B;AAAA,cACA;AAAA,gBACE,OAAO,yBAAyB;AAAA,cAClC;AAAA,cACA;AAAA,gBACE,OAAO,oDACA;AAAA,cACT;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU,CAAE,KAAK,qBAAsB;AAAA,UACzC;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK,QAAQ,KAAK,uBAAuB;AAAA,gBACvC,YAAY;AAAA,cACd,CAAC;AAAA,YACH;AAAA,YACA,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK,QAAQ,KAAK,uBAAuB;AAAA,gBACvC,YAAY;AAAA,cACd,CAAC;AAAA,YACH;AAAA,YACA,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,OAAO,KAAK,WAAW;AAAA,YACvB,UAAU;AAAA,cACR,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}