{"version": 3, "sources": ["../../highlight.js/lib/languages/applescript.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: AppleScript\nAuthors: <AUTHORS>\nCategory: scripting\nWebsite: https://developer.apple.com/library/archive/documentation/AppleScript/Conceptual/AppleScriptLangGuide/introduction/ASLR_intro.html\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction applescript(hljs) {\n  const STRING = hljs.inherit(\n    hljs.QUOTE_STRING_MODE, {\n      illegal: null\n    });\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    contains: [\n      'self',\n      hljs.C_NUMBER_MODE,\n      STRING\n    ]\n  };\n  const COMMENT_MODE_1 = hljs.COMMENT(/--/, /$/);\n  const COMMENT_MODE_2 = hljs.COMMENT(\n    /\\(\\*/,\n    /\\*\\)/,\n    {\n      contains: [\n        'self', // allow nesting\n        COMMENT_MODE_1\n      ]\n    }\n  );\n  const COMMENTS = [\n    COMMENT_MODE_1,\n    COMMENT_MODE_2,\n    hljs.HASH_COMMENT_MODE\n  ];\n\n  const KEYWORD_PATTERNS = [\n    /apart from/,\n    /aside from/,\n    /instead of/,\n    /out of/,\n    /greater than/,\n    /isn't|(doesn't|does not) (equal|come before|come after|contain)/,\n    /(greater|less) than( or equal)?/,\n    /(starts?|ends|begins?) with/,\n    /contained by/,\n    /comes (before|after)/,\n    /a (ref|reference)/,\n    /POSIX (file|path)/,\n    /(date|time) string/,\n    /quoted form/\n  ];\n\n  const BUILT_IN_PATTERNS = [\n    /clipboard info/,\n    /the clipboard/,\n    /info for/,\n    /list (disks|folder)/,\n    /mount volume/,\n    /path to/,\n    /(close|open for) access/,\n    /(get|set) eof/,\n    /current date/,\n    /do shell script/,\n    /get volume settings/,\n    /random number/,\n    /set volume/,\n    /system attribute/,\n    /system info/,\n    /time to GMT/,\n    /(load|run|store) script/,\n    /scripting components/,\n    /ASCII (character|number)/,\n    /localized string/,\n    /choose (application|color|file|file name|folder|from list|remote application|URL)/,\n    /display (alert|dialog)/\n  ];\n\n  return {\n    name: 'AppleScript',\n    aliases: [ 'osascript' ],\n    keywords: {\n      keyword:\n        'about above after against and around as at back before beginning ' +\n        'behind below beneath beside between but by considering ' +\n        'contain contains continue copy div does eighth else end equal ' +\n        'equals error every exit fifth first for fourth from front ' +\n        'get given global if ignoring in into is it its last local me ' +\n        'middle mod my ninth not of on onto or over prop property put ref ' +\n        'reference repeat returning script second set seventh since ' +\n        'sixth some tell tenth that the|0 then third through thru ' +\n        'timeout times to transaction try until where while whose with ' +\n        'without',\n      literal:\n        'AppleScript false linefeed return pi quote result space tab true',\n      built_in:\n        'alias application boolean class constant date file integer list ' +\n        'number real record string text ' +\n        'activate beep count delay launch log offset read round ' +\n        'run say summarize write ' +\n        'character characters contents day frontmost id item length ' +\n        'month name paragraph paragraphs rest reverse running time version ' +\n        'weekday word words year'\n    },\n    contains: [\n      STRING,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'built_in',\n        begin: concat(\n          /\\b/,\n          either(...BUILT_IN_PATTERNS),\n          /\\b/\n        )\n      },\n      {\n        className: 'built_in',\n        begin: /^\\s*return\\b/\n      },\n      {\n        className: 'literal',\n        begin:\n          /\\b(text item delimiters|current application|missing value)\\b/\n      },\n      {\n        className: 'keyword',\n        begin: concat(\n          /\\b/,\n          either(...KEYWORD_PATTERNS),\n          /\\b/\n        )\n      },\n      {\n        beginKeywords: 'on',\n        illegal: /[${=;\\n]/,\n        contains: [\n          hljs.UNDERSCORE_TITLE_MODE,\n          PARAMS\n        ]\n      },\n      ...COMMENTS\n    ],\n    illegal: /\\/\\/|->|=>|\\[\\[/\n  };\n}\n\nmodule.exports = applescript;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AASA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC5D,aAAO;AAAA,IACT;AAWA,aAAS,YAAY,MAAM;AACzB,YAAM,SAAS,KAAK;AAAA,QAClB,KAAK;AAAA,QAAmB;AAAA,UACtB,SAAS;AAAA,QACX;AAAA,MAAC;AACH,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,YAAM,iBAAiB,KAAK,QAAQ,MAAM,GAAG;AAC7C,YAAM,iBAAiB,KAAK;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,UACE,UAAU;AAAA,YACR;AAAA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA,KAAK;AAAA,MACP;AAEA,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,oBAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,WAAY;AAAA,QACvB,UAAU;AAAA,UACR,SACE;AAAA,UAUF,SACE;AAAA,UACF,UACE;AAAA,QAOJ;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,cACL;AAAA,cACA,OAAO,GAAG,iBAAiB;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OACE;AAAA,UACJ;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,cACL;AAAA,cACA,OAAO,GAAG,gBAAgB;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,SAAS;AAAA,YACT,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,UACA,GAAG;AAAA,QACL;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}