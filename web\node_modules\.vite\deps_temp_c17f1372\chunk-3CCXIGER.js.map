{"version": 3, "sources": ["../../refractor/lang/cpp.js"], "sourcesContent": ["'use strict'\nvar refractorC = require('./c.js')\nmodule.exports = cpp\ncpp.displayName = 'cpp'\ncpp.aliases = []\nfunction cpp(Prism) {\n  Prism.register(refractorC)\n  ;(function (Prism) {\n    var keyword =\n      /\\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\\b/\n    var modName = /\\b(?!<keyword>)\\w+(?:\\s*\\.\\s*\\w+)*\\b/.source.replace(\n      /<keyword>/g,\n      function () {\n        return keyword.source\n      }\n    )\n    Prism.languages.cpp = Prism.languages.extend('c', {\n      'class-name': [\n        {\n          pattern: RegExp(\n            /(\\b(?:class|concept|enum|struct|typename)\\s+)(?!<keyword>)\\w+/.source.replace(\n              /<keyword>/g,\n              function () {\n                return keyword.source\n              }\n            )\n          ),\n          lookbehind: true\n        }, // This is intended to capture the class name of method implementations like:\n        //   void foo::bar() const {}\n        // However! The `foo` in the above example could also be a namespace, so we only capture the class name if\n        // it starts with an uppercase letter. This approximation should give decent results.\n        /\\b[A-Z]\\w*(?=\\s*::\\s*\\w+\\s*\\()/, // This will capture the class name before destructors like:\n        //   Foo::~Foo() {}\n        /\\b[A-Z_]\\w*(?=\\s*::\\s*~\\w+\\s*\\()/i, // This also intends to capture the class name of method implementations but here the class has template\n        // parameters, so it can't be a namespace (until C++ adds generic namespaces).\n        /\\b\\w+(?=\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\\s*::\\s*\\w+\\s*\\()/\n      ],\n      keyword: keyword,\n      number: {\n        pattern:\n          /(?:\\b0b[01']+|\\b0x(?:[\\da-f']+(?:\\.[\\da-f']*)?|\\.[\\da-f']+)(?:p[+-]?[\\d']+)?|(?:\\b[\\d']+(?:\\.[\\d']*)?|\\B\\.[\\d']+)(?:e[+-]?[\\d']+)?)[ful]{0,4}/i,\n        greedy: true\n      },\n      operator:\n        />>=?|<<=?|->|--|\\+\\+|&&|\\|\\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\\b/,\n      boolean: /\\b(?:false|true)\\b/\n    })\n    Prism.languages.insertBefore('cpp', 'string', {\n      module: {\n        // https://en.cppreference.com/w/cpp/language/modules\n        pattern: RegExp(\n          /(\\b(?:import|module)\\s+)/.source +\n            '(?:' + // header-name\n            /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|<[^<>\\r\\n]*>/.source +\n            '|' + // module name or partition or both\n            /<mod-name>(?:\\s*:\\s*<mod-name>)?|:\\s*<mod-name>/.source.replace(\n              /<mod-name>/g,\n              function () {\n                return modName\n              }\n            ) +\n            ')'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          string: /^[<\"][\\s\\S]+/,\n          operator: /:/,\n          punctuation: /\\./\n        }\n      },\n      'raw-string': {\n        pattern: /R\"([^()\\\\ ]{0,16})\\([\\s\\S]*?\\)\\1\"/,\n        alias: 'string',\n        greedy: true\n      }\n    })\n    Prism.languages.insertBefore('cpp', 'keyword', {\n      'generic-function': {\n        pattern: /\\b(?!operator\\b)[a-z_]\\w*\\s*<(?:[^<>]|<[^<>]*>)*>(?=\\s*\\()/i,\n        inside: {\n          function: /^\\w+/,\n          generic: {\n            pattern: /<[\\s\\S]+/,\n            alias: 'class-name',\n            inside: Prism.languages.cpp\n          }\n        }\n      }\n    })\n    Prism.languages.insertBefore('cpp', 'operator', {\n      'double-colon': {\n        pattern: /::/,\n        alias: 'punctuation'\n      }\n    })\n    Prism.languages.insertBefore('cpp', 'class-name', {\n      // the base clause is an optional list of parent classes\n      // https://en.cppreference.com/w/cpp/language/class\n      'base-clause': {\n        pattern:\n          /(\\b(?:class|struct)\\s+\\w+\\s*:\\s*)[^;{}\"'\\s]+(?:\\s+[^;{}\"'\\s]+)*(?=\\s*[;{])/,\n        lookbehind: true,\n        greedy: true,\n        inside: Prism.languages.extend('cpp', {})\n      }\n    })\n    Prism.languages.insertBefore(\n      'inside',\n      'double-colon',\n      {\n        // All untokenized words that are not namespaces should be class names\n        'class-name': /\\b[a-z_]\\w*\\b(?!\\s*::)/i\n      },\n      Prism.languages.cpp['base-clause']\n    )\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,aAAa;AACjB,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,SAAS,UAAU;AACxB,OAAC,SAAUA,QAAO;AACjB,YAAI,UACF;AACF,YAAI,UAAU,uCAAuC,OAAO;AAAA,UAC1D;AAAA,UACA,WAAY;AACV,mBAAO,QAAQ;AAAA,UACjB;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,KAAK;AAAA,UAChD,cAAc;AAAA,YACZ;AAAA,cACE,SAAS;AAAA,gBACP,gEAAgE,OAAO;AAAA,kBACrE;AAAA,kBACA,WAAY;AACV,2BAAO,QAAQ;AAAA,kBACjB;AAAA,gBACF;AAAA,cACF;AAAA,cACA,YAAY;AAAA,YACd;AAAA;AAAA;AAAA;AAAA;AAAA,YAIA;AAAA;AAAA;AAAA,YAEA;AAAA;AAAA;AAAA,YAEA;AAAA,UACF;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,YACN,SACE;AAAA,YACF,QAAQ;AAAA,UACV;AAAA,UACA,UACE;AAAA,UACF,SAAS;AAAA,QACX,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,OAAO,UAAU;AAAA,UAC5C,QAAQ;AAAA;AAAA,YAEN,SAAS;AAAA,cACP,2BAA2B,SACzB;AAAA,cACA,mDAAmD,SACnD;AAAA,cACA,kDAAkD,OAAO;AAAA,gBACvD;AAAA,gBACA,WAAY;AACV,yBAAO;AAAA,gBACT;AAAA,cACF,IACA;AAAA,YACJ;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,QAAQ;AAAA,cACR,UAAU;AAAA,cACV,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,OAAO,WAAW;AAAA,UAC7C,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,UAAU;AAAA,cACV,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQA,OAAM,UAAU;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,OAAO,YAAY;AAAA,UAC9C,gBAAgB;AAAA,YACd,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,OAAO,cAAc;AAAA;AAAA;AAAA,UAGhD,eAAe;AAAA,YACb,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQA,OAAM,UAAU,OAAO,OAAO,CAAC,CAAC;AAAA,UAC1C;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA;AAAA,YAEE,cAAc;AAAA,UAChB;AAAA,UACAA,OAAM,UAAU,IAAI,aAAa;AAAA,QACnC;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}