{"version": 3, "sources": ["../../refractor/lang/pug.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = pug\npug.displayName = 'pug'\npug.aliases = []\nfunction pug(Prism) {\n  ;(function (Prism) {\n    // TODO:\n    // - Add CSS highlighting inside <style> tags\n    // - Add support for multi-line code blocks\n    // - Add support for interpolation #{} and !{}\n    // - Add support for tag interpolation #[]\n    // - Add explicit support for plain text using |\n    // - Add support for markup embedded in plain text\n    Prism.languages.pug = {\n      // Multiline stuff should appear before the rest\n      // This handles both single-line and multi-line comments\n      comment: {\n        pattern: /(^([\\t ]*))\\/\\/.*(?:(?:\\r?\\n|\\r)\\2[\\t ].+)*/m,\n        lookbehind: true\n      },\n      // All the tag-related part is in lookbehind\n      // so that it can be highlighted by the \"tag\" pattern\n      'multiline-script': {\n        pattern:\n          /(^([\\t ]*)script\\b.*\\.[\\t ]*)(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/m,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      // See at the end of the file for known filters\n      filter: {\n        pattern:\n          /(^([\\t ]*)):.+(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/m,\n        lookbehind: true,\n        inside: {\n          'filter-name': {\n            pattern: /^:[\\w-]+/,\n            alias: 'variable'\n          },\n          text: /\\S[\\s\\S]*/\n        }\n      },\n      'multiline-plain-text': {\n        pattern:\n          /(^([\\t ]*)[\\w\\-#.]+\\.[\\t ]*)(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/m,\n        lookbehind: true\n      },\n      markup: {\n        pattern: /(^[\\t ]*)<.+/m,\n        lookbehind: true,\n        inside: Prism.languages.markup\n      },\n      doctype: {\n        pattern: /((?:^|\\n)[\\t ]*)doctype(?: .+)?/,\n        lookbehind: true\n      },\n      // This handle all conditional and loop keywords\n      'flow-control': {\n        pattern:\n          /(^[\\t ]*)(?:case|default|each|else|if|unless|when|while)\\b(?: .+)?/m,\n        lookbehind: true,\n        inside: {\n          each: {\n            pattern: /^each .+? in\\b/,\n            inside: {\n              keyword: /\\b(?:each|in)\\b/,\n              punctuation: /,/\n            }\n          },\n          branch: {\n            pattern: /^(?:case|default|else|if|unless|when|while)\\b/,\n            alias: 'keyword'\n          },\n          rest: Prism.languages.javascript\n        }\n      },\n      keyword: {\n        pattern: /(^[\\t ]*)(?:append|block|extends|include|prepend)\\b.+/m,\n        lookbehind: true\n      },\n      mixin: [\n        // Declaration\n        {\n          pattern: /(^[\\t ]*)mixin .+/m,\n          lookbehind: true,\n          inside: {\n            keyword: /^mixin/,\n            function: /\\w+(?=\\s*\\(|\\s*$)/,\n            punctuation: /[(),.]/\n          }\n        }, // Usage\n        {\n          pattern: /(^[\\t ]*)\\+.+/m,\n          lookbehind: true,\n          inside: {\n            name: {\n              pattern: /^\\+\\w+/,\n              alias: 'function'\n            },\n            rest: Prism.languages.javascript\n          }\n        }\n      ],\n      script: {\n        pattern: /(^[\\t ]*script(?:(?:&[^(]+)?\\([^)]+\\))*[\\t ]).+/m,\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      'plain-text': {\n        pattern:\n          /(^[\\t ]*(?!-)[\\w\\-#.]*[\\w\\-](?:(?:&[^(]+)?\\([^)]+\\))*\\/?[\\t ]).+/m,\n        lookbehind: true\n      },\n      tag: {\n        pattern: /(^[\\t ]*)(?!-)[\\w\\-#.]*[\\w\\-](?:(?:&[^(]+)?\\([^)]+\\))*\\/?:?/m,\n        lookbehind: true,\n        inside: {\n          attributes: [\n            {\n              pattern: /&[^(]+\\([^)]+\\)/,\n              inside: Prism.languages.javascript\n            },\n            {\n              pattern: /\\([^)]+\\)/,\n              inside: {\n                'attr-value': {\n                  pattern: /(=\\s*(?!\\s))(?:\\{[^}]*\\}|[^,)\\r\\n]+)/,\n                  lookbehind: true,\n                  inside: Prism.languages.javascript\n                },\n                'attr-name': /[\\w-]+(?=\\s*!?=|\\s*[,)])/,\n                punctuation: /[!=(),]+/\n              }\n            }\n          ],\n          punctuation: /:/,\n          'attr-id': /#[\\w\\-]+/,\n          'attr-class': /\\.[\\w\\-]+/\n        }\n      },\n      code: [\n        {\n          pattern: /(^[\\t ]*(?:-|!?=)).+/m,\n          lookbehind: true,\n          inside: Prism.languages.javascript\n        }\n      ],\n      punctuation: /[.\\-!=|]+/\n    }\n    var filter_pattern =\n      /(^([\\t ]*)):<filter_name>(?:(?:\\r?\\n|\\r(?!\\n))(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+/\n        .source // Non exhaustive list of available filters and associated languages\n    var filters = [\n      {\n        filter: 'atpl',\n        language: 'twig'\n      },\n      {\n        filter: 'coffee',\n        language: 'coffeescript'\n      },\n      'ejs',\n      'handlebars',\n      'less',\n      'livescript',\n      'markdown',\n      {\n        filter: 'sass',\n        language: 'scss'\n      },\n      'stylus'\n    ]\n    var all_filters = {}\n    for (var i = 0, l = filters.length; i < l; i++) {\n      var filter = filters[i]\n      filter =\n        typeof filter === 'string'\n          ? {\n              filter: filter,\n              language: filter\n            }\n          : filter\n      if (Prism.languages[filter.language]) {\n        all_filters['filter-' + filter.filter] = {\n          pattern: RegExp(\n            filter_pattern.replace('<filter_name>', function () {\n              return filter.filter\n            }),\n            'm'\n          ),\n          lookbehind: true,\n          inside: {\n            'filter-name': {\n              pattern: /^:[\\w-]+/,\n              alias: 'variable'\n            },\n            text: {\n              pattern: /\\S[\\s\\S]*/,\n              alias: [filter.language, 'language-' + filter.language],\n              inside: Prism.languages[filter.language]\n            }\n          }\n        }\n      }\n    }\n    Prism.languages.insertBefore('pug', 'filter', all_filters)\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB;AAAC,OAAC,SAAUA,QAAO;AAQjB,QAAAA,OAAM,UAAU,MAAM;AAAA;AAAA;AAAA,UAGpB,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA;AAAA;AAAA,UAGA,oBAAoB;AAAA,YAClB,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA;AAAA,UAEA,QAAQ;AAAA,YACN,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,eAAe;AAAA,gBACb,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,MAAM;AAAA,YACR;AAAA,UACF;AAAA,UACA,wBAAwB;AAAA,YACtB,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA;AAAA,UAEA,gBAAgB;AAAA,YACd,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,SAAS;AAAA,kBACT,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,MAAMA,OAAM,UAAU;AAAA,YACxB;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,OAAO;AAAA;AAAA,YAEL;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,UAAU;AAAA,gBACV,aAAa;AAAA,cACf;AAAA,YACF;AAAA;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,MAAM;AAAA,kBACJ,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,MAAMA,OAAM,UAAU;AAAA,cACxB;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,UACA,cAAc;AAAA,YACZ,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,KAAK;AAAA,YACH,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,YAAY;AAAA,gBACV;AAAA,kBACE,SAAS;AAAA,kBACT,QAAQA,OAAM,UAAU;AAAA,gBAC1B;AAAA,gBACA;AAAA,kBACE,SAAS;AAAA,kBACT,QAAQ;AAAA,oBACN,cAAc;AAAA,sBACZ,SAAS;AAAA,sBACT,YAAY;AAAA,sBACZ,QAAQA,OAAM,UAAU;AAAA,oBAC1B;AAAA,oBACA,aAAa;AAAA,oBACb,aAAa;AAAA,kBACf;AAAA,gBACF;AAAA,cACF;AAAA,cACA,aAAa;AAAA,cACb,WAAW;AAAA,cACX,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,UACA,MAAM;AAAA,YACJ;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQA,OAAM,UAAU;AAAA,YAC1B;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf;AACA,YAAI,iBACF,iFACG;AACL,YAAI,UAAU;AAAA,UACZ;AAAA,YACE,QAAQ;AAAA,YACR,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,YACR,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,QAAQ;AAAA,YACR,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,QACF;AACA,YAAI,cAAc,CAAC;AACnB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC9C,cAAI,SAAS,QAAQ,CAAC;AACtB,mBACE,OAAO,WAAW,WACd;AAAA,YACE;AAAA,YACA,UAAU;AAAA,UACZ,IACA;AACN,cAAIA,OAAM,UAAU,OAAO,QAAQ,GAAG;AACpC,wBAAY,YAAY,OAAO,MAAM,IAAI;AAAA,cACvC,SAAS;AAAA,gBACP,eAAe,QAAQ,iBAAiB,WAAY;AAClD,yBAAO,OAAO;AAAA,gBAChB,CAAC;AAAA,gBACD;AAAA,cACF;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,eAAe;AAAA,kBACb,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,MAAM;AAAA,kBACJ,SAAS;AAAA,kBACT,OAAO,CAAC,OAAO,UAAU,cAAc,OAAO,QAAQ;AAAA,kBACtD,QAAQA,OAAM,UAAU,OAAO,QAAQ;AAAA,gBACzC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,aAAa,OAAO,UAAU,WAAW;AAAA,MAC3D,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}