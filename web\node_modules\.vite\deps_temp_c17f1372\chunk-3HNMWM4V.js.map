{"version": 3, "sources": ["../../refractor/lang/gap.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = gap\ngap.displayName = 'gap'\ngap.aliases = []\nfunction gap(Prism) {\n  // https://www.gap-system.org/Manuals/doc/ref/chap4.html\n  // https://www.gap-system.org/Manuals/doc/ref/chap27.html\n  Prism.languages.gap = {\n    shell: {\n      pattern: /^gap>[\\s\\S]*?(?=^gap>|$(?![\\s\\S]))/m,\n      greedy: true,\n      inside: {\n        gap: {\n          pattern: /^(gap>).+(?:(?:\\r(?:\\n|(?!\\n))|\\n)>.*)*/,\n          lookbehind: true,\n          inside: null // see below\n        },\n        punctuation: /^gap>/\n      }\n    },\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    string: {\n      pattern:\n        /(^|[^\\\\'\"])(?:'(?:[^\\r\\n\\\\']|\\\\.){1,10}'|\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"(?!\")|\"\"\"[\\s\\S]*?\"\"\")/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        continuation: {\n          pattern: /([\\r\\n])>/,\n          lookbehind: true,\n          alias: 'punctuation'\n        }\n      }\n    },\n    keyword:\n      /\\b(?:Assert|Info|IsBound|QUIT|TryNextMethod|Unbind|and|atomic|break|continue|do|elif|else|end|fi|for|function|if|in|local|mod|not|od|or|quit|readonly|readwrite|rec|repeat|return|then|until|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number: {\n      pattern:\n        /(^|[^\\w.]|\\.\\.)(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eE][+-]?\\d+)?(?:_[a-z]?)?(?=$|[^\\w.]|\\.\\.)/,\n      lookbehind: true\n    },\n    continuation: {\n      pattern: /([\\r\\n])>/,\n      lookbehind: true,\n      alias: 'punctuation'\n    },\n    operator: /->|[-+*/^~=!]|<>|[<>]=?|:=|\\.\\./,\n    punctuation: /[()[\\]{},;.:]/\n  }\n  Prism.languages.gap.shell.inside.gap.inside = Prism.languages.gap\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAGlB,YAAM,UAAU,MAAM;AAAA,QACpB,OAAO;AAAA,UACL,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,KAAK;AAAA,cACH,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA;AAAA,YACV;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA,SACE;AAAA,QACF,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AACA,YAAM,UAAU,IAAI,MAAM,OAAO,IAAI,SAAS,MAAM,UAAU;AAAA,IAChE;AAAA;AAAA;", "names": []}