{"version": 3, "sources": ["../../highlight.js/lib/languages/handlebars.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: Handlebars\nRequires: xml.js\nAuthor: <PERSON> <PERSON> <<EMAIL>>\nDescription: Matcher for Handlebars as well as EmberJS additions.\nWebsite: https://handlebarsjs.com\nCategory: template\n*/\n\nfunction handlebars(hljs) {\n  const BUILT_INS = {\n    'builtin-name': [\n      'action',\n      'bindattr',\n      'collection',\n      'component',\n      'concat',\n      'debugger',\n      'each',\n      'each-in',\n      'get',\n      'hash',\n      'if',\n      'in',\n      'input',\n      'link-to',\n      'loc',\n      'log',\n      'lookup',\n      'mut',\n      'outlet',\n      'partial',\n      'query-params',\n      'render',\n      'template',\n      'textarea',\n      'unbound',\n      'unless',\n      'view',\n      'with',\n      'yield'\n    ]\n  };\n\n  const LITERALS = {\n    literal: [\n      'true',\n      'false',\n      'undefined',\n      'null'\n    ]\n  };\n\n  // as defined in https://handlebarsjs.com/guide/expressions.html#literal-segments\n  // this regex matches literal segments like ' abc ' or [ abc ] as well as helpers and paths\n  // like a/b, ./abc/cde, and abc.bcd\n\n  const DOUBLE_QUOTED_ID_REGEX = /\"\"|\"[^\"]+\"/;\n  const SINGLE_QUOTED_ID_REGEX = /''|'[^']+'/;\n  const BRACKET_QUOTED_ID_REGEX = /\\[\\]|\\[[^\\]]+\\]/;\n  const PLAIN_ID_REGEX = /[^\\s!\"#%&'()*+,.\\/;<=>@\\[\\\\\\]^`{|}~]+/;\n  const PATH_DELIMITER_REGEX = /(\\.|\\/)/;\n  const ANY_ID = either(\n    DOUBLE_QUOTED_ID_REGEX,\n    SINGLE_QUOTED_ID_REGEX,\n    BRACKET_QUOTED_ID_REGEX,\n    PLAIN_ID_REGEX\n    );\n\n  const IDENTIFIER_REGEX = concat(\n    optional(/\\.|\\.\\/|\\//), // relative or absolute path\n    ANY_ID,\n    anyNumberOfTimes(concat(\n      PATH_DELIMITER_REGEX,\n      ANY_ID\n    ))\n  );\n\n  // identifier followed by a equal-sign (without the equal sign)\n  const HASH_PARAM_REGEX = concat(\n    '(',\n    BRACKET_QUOTED_ID_REGEX, '|',\n    PLAIN_ID_REGEX,\n    ')(?==)'\n  );\n\n  const HELPER_NAME_OR_PATH_EXPRESSION = {\n    begin: IDENTIFIER_REGEX,\n    lexemes: /[\\w.\\/]+/\n  };\n\n  const HELPER_PARAMETER = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    keywords: LITERALS\n  });\n\n  const SUB_EXPRESSION = {\n    begin: /\\(/,\n    end: /\\)/\n    // the \"contains\" is added below when all necessary sub-modes are defined\n  };\n\n  const HASH = {\n    // fka \"attribute-assignment\", parameters of the form 'key=value'\n    className: 'attr',\n    begin: HASH_PARAM_REGEX,\n    relevance: 0,\n    starts: {\n      begin: /=/,\n      end: /=/,\n      starts: {\n        contains: [\n          hljs.NUMBER_MODE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          HELPER_PARAMETER,\n          SUB_EXPRESSION\n        ]\n      }\n    }\n  };\n\n  const BLOCK_PARAMS = {\n    // parameters of the form '{{#with x as | y |}}...{{/with}}'\n    begin: /as\\s+\\|/,\n    keywords: {\n      keyword: 'as'\n    },\n    end: /\\|/,\n    contains: [\n      {\n        // define sub-mode in order to prevent highlighting of block-parameter named \"as\"\n        begin: /\\w+/\n      }\n    ]\n  };\n\n  const HELPER_PARAMETERS = {\n    contains: [\n      hljs.NUMBER_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      BLOCK_PARAMS,\n      HASH,\n      HELPER_PARAMETER,\n      SUB_EXPRESSION\n    ],\n    returnEnd: true\n    // the property \"end\" is defined through inheritance when the mode is used. If depends\n    // on the surrounding mode, but \"endsWithParent\" does not work here (i.e. it includes the\n    // end-token of the surrounding mode)\n  };\n\n  const SUB_EXPRESSION_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    className: 'name',\n    keywords: BUILT_INS,\n    starts: hljs.inherit(HELPER_PARAMETERS, {\n      end: /\\)/\n    })\n  });\n\n  SUB_EXPRESSION.contains = [SUB_EXPRESSION_CONTENTS];\n\n  const OPENING_BLOCK_MUSTACHE_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    keywords: BUILT_INS,\n    className: 'name',\n    starts: hljs.inherit(HELPER_PARAMETERS, {\n      end: /\\}\\}/\n    })\n  });\n\n  const CLOSING_BLOCK_MUSTACHE_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    keywords: BUILT_INS,\n    className: 'name'\n  });\n\n  const BASIC_MUSTACHE_CONTENTS = hljs.inherit(HELPER_NAME_OR_PATH_EXPRESSION, {\n    className: 'name',\n    keywords: BUILT_INS,\n    starts: hljs.inherit(HELPER_PARAMETERS, {\n      end: /\\}\\}/\n    })\n  });\n\n  const ESCAPE_MUSTACHE_WITH_PRECEEDING_BACKSLASH = {\n    begin: /\\\\\\{\\{/,\n    skip: true\n  };\n  const PREVENT_ESCAPE_WITH_ANOTHER_PRECEEDING_BACKSLASH = {\n    begin: /\\\\\\\\(?=\\{\\{)/,\n    skip: true\n  };\n\n  return {\n    name: 'Handlebars',\n    aliases: [\n      'hbs',\n      'html.hbs',\n      'html.handlebars',\n      'htmlbars'\n    ],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [\n      ESCAPE_MUSTACHE_WITH_PRECEEDING_BACKSLASH,\n      PREVENT_ESCAPE_WITH_ANOTHER_PRECEEDING_BACKSLASH,\n      hljs.COMMENT(/\\{\\{!--/, /--\\}\\}/),\n      hljs.COMMENT(/\\{\\{!/, /\\}\\}/),\n      {\n        // open raw block \"{{{{raw}}}} content not evaluated {{{{/raw}}}}\"\n        className: 'template-tag',\n        begin: /\\{\\{\\{\\{(?!\\/)/,\n        end: /\\}\\}\\}\\}/,\n        contains: [OPENING_BLOCK_MUSTACHE_CONTENTS],\n        starts: {\n          end: /\\{\\{\\{\\{\\//,\n          returnEnd: true,\n          subLanguage: 'xml'\n        }\n      },\n      {\n        // close raw block\n        className: 'template-tag',\n        begin: /\\{\\{\\{\\{\\//,\n        end: /\\}\\}\\}\\}/,\n        contains: [CLOSING_BLOCK_MUSTACHE_CONTENTS]\n      },\n      {\n        // open block statement\n        className: 'template-tag',\n        begin: /\\{\\{#/,\n        end: /\\}\\}/,\n        contains: [OPENING_BLOCK_MUSTACHE_CONTENTS]\n      },\n      {\n        className: 'template-tag',\n        begin: /\\{\\{(?=else\\}\\})/,\n        end: /\\}\\}/,\n        keywords: 'else'\n      },\n      {\n        className: 'template-tag',\n        begin: /\\{\\{(?=else if)/,\n        end: /\\}\\}/,\n        keywords: 'else if'\n      },\n      {\n        // closing block statement\n        className: 'template-tag',\n        begin: /\\{\\{\\//,\n        end: /\\}\\}/,\n        contains: [CLOSING_BLOCK_MUSTACHE_CONTENTS]\n      },\n      {\n        // template variable or helper-call that is NOT html-escaped\n        className: 'template-variable',\n        begin: /\\{\\{\\{/,\n        end: /\\}\\}\\}/,\n        contains: [BASIC_MUSTACHE_CONTENTS]\n      },\n      {\n        // template variable or helper-call that is html-escaped\n        className: 'template-variable',\n        begin: /\\{\\{/,\n        end: /\\}\\}/,\n        contains: [BASIC_MUSTACHE_CONTENTS]\n      }\n    ]\n  };\n}\n\nmodule.exports = handlebars;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,iBAAiB,IAAI;AAC5B,aAAO,OAAO,KAAK,IAAI,IAAI;AAAA,IAC7B;AAMA,aAAS,SAAS,IAAI;AACpB,aAAO,OAAO,KAAK,IAAI,IAAI;AAAA,IAC7B;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AASA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC5D,aAAO;AAAA,IACT;AAWA,aAAS,WAAW,MAAM;AACxB,YAAM,YAAY;AAAA,QAChB,gBAAgB;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAMA,YAAM,yBAAyB;AAC/B,YAAM,yBAAyB;AAC/B,YAAM,0BAA0B;AAChC,YAAM,iBAAiB;AACvB,YAAM,uBAAuB;AAC7B,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACA;AAEF,YAAM,mBAAmB;AAAA,QACvB,SAAS,YAAY;AAAA;AAAA,QACrB;AAAA,QACA,iBAAiB;AAAA,UACf;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAGA,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QAAyB;AAAA,QACzB;AAAA,QACA;AAAA,MACF;AAEA,YAAM,iCAAiC;AAAA,QACrC,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAEA,YAAM,mBAAmB,KAAK,QAAQ,gCAAgC;AAAA,QACpE,UAAU;AAAA,MACZ,CAAC;AAED,YAAM,iBAAiB;AAAA,QACrB,OAAO;AAAA,QACP,KAAK;AAAA;AAAA,MAEP;AAEA,YAAM,OAAO;AAAA;AAAA,QAEX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,UACL,QAAQ;AAAA,YACN,UAAU;AAAA,cACR,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,eAAe;AAAA;AAAA,QAEnB,OAAO;AAAA,QACP,UAAU;AAAA,UACR,SAAS;AAAA,QACX;AAAA,QACA,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,oBAAoB;AAAA,QACxB,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA;AAAA;AAAA;AAAA,MAIb;AAEA,YAAM,0BAA0B,KAAK,QAAQ,gCAAgC;AAAA,QAC3E,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ,KAAK,QAAQ,mBAAmB;AAAA,UACtC,KAAK;AAAA,QACP,CAAC;AAAA,MACH,CAAC;AAED,qBAAe,WAAW,CAAC,uBAAuB;AAElD,YAAM,kCAAkC,KAAK,QAAQ,gCAAgC;AAAA,QACnF,UAAU;AAAA,QACV,WAAW;AAAA,QACX,QAAQ,KAAK,QAAQ,mBAAmB;AAAA,UACtC,KAAK;AAAA,QACP,CAAC;AAAA,MACH,CAAC;AAED,YAAM,kCAAkC,KAAK,QAAQ,gCAAgC;AAAA,QACnF,UAAU;AAAA,QACV,WAAW;AAAA,MACb,CAAC;AAED,YAAM,0BAA0B,KAAK,QAAQ,gCAAgC;AAAA,QAC3E,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ,KAAK,QAAQ,mBAAmB;AAAA,UACtC,KAAK;AAAA,QACP,CAAC;AAAA,MACH,CAAC;AAED,YAAM,4CAA4C;AAAA,QAChD,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AACA,YAAM,mDAAmD;AAAA,QACvD,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA,KAAK,QAAQ,WAAW,QAAQ;AAAA,UAChC,KAAK,QAAQ,SAAS,MAAM;AAAA,UAC5B;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC,+BAA+B;AAAA,YAC1C,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,WAAW;AAAA,cACX,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC,+BAA+B;AAAA,UAC5C;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC,+BAA+B;AAAA,UAC5C;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,UACZ;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC,+BAA+B;AAAA,UAC5C;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC,uBAAuB;AAAA,UACpC;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC,uBAAuB;AAAA,UACpC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}