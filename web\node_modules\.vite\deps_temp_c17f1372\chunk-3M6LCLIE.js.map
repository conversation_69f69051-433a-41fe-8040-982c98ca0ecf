{"version": 3, "sources": ["../../highlight.js/lib/languages/sqf.js"], "sourcesContent": ["/*\nLanguage: SQF\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Scripting language for the Arma game series\nWebsite: https://community.bistudio.com/wiki/SQF_syntax\nCategory: scripting\n*/\n\nfunction sqf(hljs) {\n  // In SQF, a variable start with _\n  const VARIABLE = {\n    className: 'variable',\n    begin: /\\b_+[a-zA-Z]\\w*/\n  };\n\n  // In SQF, a function should fit myTag_fnc_myFunction pattern\n  // https://community.bistudio.com/wiki/Functions_Library_(Arma_3)#Adding_a_Function\n  const FUNCTION = {\n    className: 'title',\n    begin: /[a-zA-Z][a-zA-Z0-9]+_fnc_\\w*/\n  };\n\n  // In SQF strings, quotes matching the start are escaped by adding a consecutive.\n  // Example of single escaped quotes: \" \"\" \" and  ' '' '.\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      {\n        begin: '\"',\n        end: '\"',\n        contains: [ {\n          begin: '\"\"',\n          relevance: 0\n        } ]\n      },\n      {\n        begin: '\\'',\n        end: '\\'',\n        contains: [ {\n          begin: '\\'\\'',\n          relevance: 0\n        } ]\n      }\n    ]\n  };\n\n  // list of keywords from:\n  // https://community.bistudio.com/wiki/PreProcessor_Commands\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: {\n      'meta-keyword':\n        'define undef ifdef ifndef else endif include'\n    },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      hljs.inherit(STRINGS, {\n        className: 'meta-string'\n      }),\n      {\n        className: 'meta-string',\n        begin: /<[^\\n>]*>/,\n        end: /$/,\n        illegal: '\\\\n'\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  return {\n    name: 'SQF',\n    case_insensitive: true,\n    keywords: {\n      keyword:\n        'case catch default do else exit exitWith for forEach from if ' +\n        'private switch then throw to try waitUntil while with',\n      built_in:\n        'abs accTime acos action actionIDs actionKeys actionKeysImages actionKeysNames ' +\n        'actionKeysNamesArray actionName actionParams activateAddons activatedAddons activateKey ' +\n        'add3DENConnection add3DENEventHandler add3DENLayer addAction addBackpack addBackpackCargo ' +\n        'addBackpackCargoGlobal addBackpackGlobal addCamShake addCuratorAddons addCuratorCameraArea ' +\n        'addCuratorEditableObjects addCuratorEditingArea addCuratorPoints addEditorObject addEventHandler ' +\n        'addForce addGoggles addGroupIcon addHandgunItem addHeadgear addItem addItemCargo ' +\n        'addItemCargoGlobal addItemPool addItemToBackpack addItemToUniform addItemToVest addLiveStats ' +\n        'addMagazine addMagazineAmmoCargo addMagazineCargo addMagazineCargoGlobal addMagazineGlobal ' +\n        'addMagazinePool addMagazines addMagazineTurret addMenu addMenuItem addMissionEventHandler ' +\n        'addMPEventHandler addMusicEventHandler addOwnedMine addPlayerScores addPrimaryWeaponItem ' +\n        'addPublicVariableEventHandler addRating addResources addScore addScoreSide addSecondaryWeaponItem ' +\n        'addSwitchableUnit addTeamMember addToRemainsCollector addTorque addUniform addVehicle addVest ' +\n        'addWaypoint addWeapon addWeaponCargo addWeaponCargoGlobal addWeaponGlobal addWeaponItem ' +\n        'addWeaponPool addWeaponTurret admin agent agents AGLToASL aimedAtTarget aimPos airDensityRTD ' +\n        'airplaneThrottle airportSide AISFinishHeal alive all3DENEntities allAirports allControls ' +\n        'allCurators allCutLayers allDead allDeadMen allDisplays allGroups allMapMarkers allMines ' +\n        'allMissionObjects allow3DMode allowCrewInImmobile allowCuratorLogicIgnoreAreas allowDamage ' +\n        'allowDammage allowFileOperations allowFleeing allowGetIn allowSprint allPlayers allSimpleObjects ' +\n        'allSites allTurrets allUnits allUnitsUAV allVariables ammo ammoOnPylon and animate animateBay ' +\n        'animateDoor animatePylon animateSource animationNames animationPhase animationSourcePhase ' +\n        'animationState append apply armoryPoints arrayIntersect asin ASLToAGL ASLToATL assert ' +\n        'assignAsCargo assignAsCargoIndex assignAsCommander assignAsDriver assignAsGunner assignAsTurret ' +\n        'assignCurator assignedCargo assignedCommander assignedDriver assignedGunner assignedItems ' +\n        'assignedTarget assignedTeam assignedVehicle assignedVehicleRole assignItem assignTeam ' +\n        'assignToAirport atan atan2 atg ATLToASL attachedObject attachedObjects attachedTo attachObject ' +\n        'attachTo attackEnabled backpack backpackCargo backpackContainer backpackItems backpackMagazines ' +\n        'backpackSpaceFor behaviour benchmark binocular boundingBox boundingBoxReal boundingCenter ' +\n        'breakOut breakTo briefingName buildingExit buildingPos buttonAction buttonSetAction cadetMode ' +\n        'call callExtension camCommand camCommit camCommitPrepared camCommitted camConstuctionSetParams ' +\n        'camCreate camDestroy cameraEffect cameraEffectEnableHUD cameraInterest cameraOn cameraView ' +\n        'campaignConfigFile camPreload camPreloaded camPrepareBank camPrepareDir camPrepareDive ' +\n        'camPrepareFocus camPrepareFov camPrepareFovRange camPreparePos camPrepareRelPos camPrepareTarget ' +\n        'camSetBank camSetDir camSetDive camSetFocus camSetFov camSetFovRange camSetPos camSetRelPos ' +\n        'camSetTarget camTarget camUseNVG canAdd canAddItemToBackpack canAddItemToUniform canAddItemToVest ' +\n        'cancelSimpleTaskDestination canFire canMove canSlingLoad canStand canSuspend ' +\n        'canTriggerDynamicSimulation canUnloadInCombat canVehicleCargo captive captiveNum cbChecked ' +\n        'cbSetChecked ceil channelEnabled cheatsEnabled checkAIFeature checkVisibility className ' +\n        'clearAllItemsFromBackpack clearBackpackCargo clearBackpackCargoGlobal clearGroupIcons ' +\n        'clearItemCargo clearItemCargoGlobal clearItemPool clearMagazineCargo clearMagazineCargoGlobal ' +\n        'clearMagazinePool clearOverlay clearRadio clearWeaponCargo clearWeaponCargoGlobal clearWeaponPool ' +\n        'clientOwner closeDialog closeDisplay closeOverlay collapseObjectTree collect3DENHistory ' +\n        'collectiveRTD combatMode commandArtilleryFire commandChat commander commandFire commandFollow ' +\n        'commandFSM commandGetOut commandingMenu commandMove commandRadio commandStop ' +\n        'commandSuppressiveFire commandTarget commandWatch comment commitOverlay compile compileFinal ' +\n        'completedFSM composeText configClasses configFile configHierarchy configName configProperties ' +\n        'configSourceAddonList configSourceMod configSourceModList confirmSensorTarget ' +\n        'connectTerminalToUAV controlsGroupCtrl copyFromClipboard copyToClipboard copyWaypoints cos count ' +\n        'countEnemy countFriendly countSide countType countUnknown create3DENComposition create3DENEntity ' +\n        'createAgent createCenter createDialog createDiaryLink createDiaryRecord createDiarySubject ' +\n        'createDisplay createGearDialog createGroup createGuardedPoint createLocation createMarker ' +\n        'createMarkerLocal createMenu createMine createMissionDisplay createMPCampaignDisplay ' +\n        'createSimpleObject createSimpleTask createSite createSoundSource createTask createTeam ' +\n        'createTrigger createUnit createVehicle createVehicleCrew createVehicleLocal crew ctAddHeader ' +\n        'ctAddRow ctClear ctCurSel ctData ctFindHeaderRows ctFindRowHeader ctHeaderControls ctHeaderCount ' +\n        'ctRemoveHeaders ctRemoveRows ctrlActivate ctrlAddEventHandler ctrlAngle ctrlAutoScrollDelay ' +\n        'ctrlAutoScrollRewind ctrlAutoScrollSpeed ctrlChecked ctrlClassName ctrlCommit ctrlCommitted ' +\n        'ctrlCreate ctrlDelete ctrlEnable ctrlEnabled ctrlFade ctrlHTMLLoaded ctrlIDC ctrlIDD ' +\n        'ctrlMapAnimAdd ctrlMapAnimClear ctrlMapAnimCommit ctrlMapAnimDone ctrlMapCursor ctrlMapMouseOver ' +\n        'ctrlMapScale ctrlMapScreenToWorld ctrlMapWorldToScreen ctrlModel ctrlModelDirAndUp ctrlModelScale ' +\n        'ctrlParent ctrlParentControlsGroup ctrlPosition ctrlRemoveAllEventHandlers ctrlRemoveEventHandler ' +\n        'ctrlScale ctrlSetActiveColor ctrlSetAngle ctrlSetAutoScrollDelay ctrlSetAutoScrollRewind ' +\n        'ctrlSetAutoScrollSpeed ctrlSetBackgroundColor ctrlSetChecked ctrlSetEventHandler ctrlSetFade ' +\n        'ctrlSetFocus ctrlSetFont ctrlSetFontH1 ctrlSetFontH1B ctrlSetFontH2 ctrlSetFontH2B ctrlSetFontH3 ' +\n        'ctrlSetFontH3B ctrlSetFontH4 ctrlSetFontH4B ctrlSetFontH5 ctrlSetFontH5B ctrlSetFontH6 ' +\n        'ctrlSetFontH6B ctrlSetFontHeight ctrlSetFontHeightH1 ctrlSetFontHeightH2 ctrlSetFontHeightH3 ' +\n        'ctrlSetFontHeightH4 ctrlSetFontHeightH5 ctrlSetFontHeightH6 ctrlSetFontHeightSecondary ' +\n        'ctrlSetFontP ctrlSetFontPB ctrlSetFontSecondary ctrlSetForegroundColor ctrlSetModel ' +\n        'ctrlSetModelDirAndUp ctrlSetModelScale ctrlSetPixelPrecision ctrlSetPosition ctrlSetScale ' +\n        'ctrlSetStructuredText ctrlSetText ctrlSetTextColor ctrlSetTooltip ctrlSetTooltipColorBox ' +\n        'ctrlSetTooltipColorShade ctrlSetTooltipColorText ctrlShow ctrlShown ctrlText ctrlTextHeight ' +\n        'ctrlTextWidth ctrlType ctrlVisible ctRowControls ctRowCount ctSetCurSel ctSetData ' +\n        'ctSetHeaderTemplate ctSetRowTemplate ctSetValue ctValue curatorAddons curatorCamera ' +\n        'curatorCameraArea curatorCameraAreaCeiling curatorCoef curatorEditableObjects curatorEditingArea ' +\n        'curatorEditingAreaType curatorMouseOver curatorPoints curatorRegisteredObjects curatorSelected ' +\n        'curatorWaypointCost current3DENOperation currentChannel currentCommand currentMagazine ' +\n        'currentMagazineDetail currentMagazineDetailTurret currentMagazineTurret currentMuzzle ' +\n        'currentNamespace currentTask currentTasks currentThrowable currentVisionMode currentWaypoint ' +\n        'currentWeapon currentWeaponMode currentWeaponTurret currentZeroing cursorObject cursorTarget ' +\n        'customChat customRadio cutFadeOut cutObj cutRsc cutText damage date dateToNumber daytime ' +\n        'deActivateKey debriefingText debugFSM debugLog deg delete3DENEntities deleteAt deleteCenter ' +\n        'deleteCollection deleteEditorObject deleteGroup deleteGroupWhenEmpty deleteIdentity ' +\n        'deleteLocation deleteMarker deleteMarkerLocal deleteRange deleteResources deleteSite deleteStatus ' +\n        'deleteTeam deleteVehicle deleteVehicleCrew deleteWaypoint detach detectedMines ' +\n        'diag_activeMissionFSMs diag_activeScripts diag_activeSQFScripts diag_activeSQSScripts ' +\n        'diag_captureFrame diag_captureFrameToFile diag_captureSlowFrame diag_codePerformance ' +\n        'diag_drawMode diag_enable diag_enabled diag_fps diag_fpsMin diag_frameNo diag_lightNewLoad ' +\n        'diag_list diag_log diag_logSlowFrame diag_mergeConfigFile diag_recordTurretLimits ' +\n        'diag_setLightNew diag_tickTime diag_toggle dialog diarySubjectExists didJIP didJIPOwner ' +\n        'difficulty difficultyEnabled difficultyEnabledRTD difficultyOption direction directSay disableAI ' +\n        'disableCollisionWith disableConversation disableDebriefingStats disableMapIndicators ' +\n        'disableNVGEquipment disableRemoteSensors disableSerialization disableTIEquipment ' +\n        'disableUAVConnectability disableUserInput displayAddEventHandler displayCtrl displayParent ' +\n        'displayRemoveAllEventHandlers displayRemoveEventHandler displaySetEventHandler dissolveTeam ' +\n        'distance distance2D distanceSqr distributionRegion do3DENAction doArtilleryFire doFire doFollow ' +\n        'doFSM doGetOut doMove doorPhase doStop doSuppressiveFire doTarget doWatch drawArrow drawEllipse ' +\n        'drawIcon drawIcon3D drawLine drawLine3D drawLink drawLocation drawPolygon drawRectangle ' +\n        'drawTriangle driver drop dynamicSimulationDistance dynamicSimulationDistanceCoef ' +\n        'dynamicSimulationEnabled dynamicSimulationSystemEnabled echo edit3DENMissionAttributes editObject ' +\n        'editorSetEventHandler effectiveCommander emptyPositions enableAI enableAIFeature ' +\n        'enableAimPrecision enableAttack enableAudioFeature enableAutoStartUpRTD enableAutoTrimRTD ' +\n        'enableCamShake enableCaustics enableChannel enableCollisionWith enableCopilot ' +\n        'enableDebriefingStats enableDiagLegend enableDynamicSimulation enableDynamicSimulationSystem ' +\n        'enableEndDialog enableEngineArtillery enableEnvironment enableFatigue enableGunLights ' +\n        'enableInfoPanelComponent enableIRLasers enableMimics enablePersonTurret enableRadio enableReload ' +\n        'enableRopeAttach enableSatNormalOnDetail enableSaving enableSentences enableSimulation ' +\n        'enableSimulationGlobal enableStamina enableTeamSwitch enableTraffic enableUAVConnectability ' +\n        'enableUAVWaypoints enableVehicleCargo enableVehicleSensor enableWeaponDisassembly ' +\n        'endLoadingScreen endMission engineOn enginesIsOnRTD enginesRpmRTD enginesTorqueRTD entities ' +\n        'environmentEnabled estimatedEndServerTime estimatedTimeLeft evalObjectArgument everyBackpack ' +\n        'everyContainer exec execEditorScript execFSM execVM exp expectedDestination exportJIPMessages ' +\n        'eyeDirection eyePos face faction fadeMusic fadeRadio fadeSound fadeSpeech failMission ' +\n        'fillWeaponsFromPool find findCover findDisplay findEditorObject findEmptyPosition ' +\n        'findEmptyPositionReady findIf findNearestEnemy finishMissionInit finite fire fireAtTarget ' +\n        'firstBackpack flag flagAnimationPhase flagOwner flagSide flagTexture fleeing floor flyInHeight ' +\n        'flyInHeightASL fog fogForecast fogParams forceAddUniform forcedMap forceEnd forceFlagTexture ' +\n        'forceFollowRoad forceMap forceRespawn forceSpeed forceWalk forceWeaponFire forceWeatherChange ' +\n        'forEachMember forEachMemberAgent forEachMemberTeam forgetTarget format formation ' +\n        'formationDirection formationLeader formationMembers formationPosition formationTask formatText ' +\n        'formLeader freeLook fromEditor fuel fullCrew gearIDCAmmoCount gearSlotAmmoCount gearSlotData ' +\n        'get3DENActionState get3DENAttribute get3DENCamera get3DENConnections get3DENEntity ' +\n        'get3DENEntityID get3DENGrid get3DENIconsVisible get3DENLayerEntities get3DENLinesVisible ' +\n        'get3DENMissionAttribute get3DENMouseOver get3DENSelected getAimingCoef getAllEnvSoundControllers ' +\n        'getAllHitPointsDamage getAllOwnedMines getAllSoundControllers getAmmoCargo getAnimAimPrecision ' +\n        'getAnimSpeedCoef getArray getArtilleryAmmo getArtilleryComputerSettings getArtilleryETA ' +\n        'getAssignedCuratorLogic getAssignedCuratorUnit getBackpackCargo getBleedingRemaining ' +\n        'getBurningValue getCameraViewDirection getCargoIndex getCenterOfMass getClientState ' +\n        'getClientStateNumber getCompatiblePylonMagazines getConnectedUAV getContainerMaxLoad ' +\n        'getCursorObjectParams getCustomAimCoef getDammage getDescription getDir getDirVisual ' +\n        'getDLCAssetsUsage getDLCAssetsUsageByName getDLCs getEditorCamera getEditorMode ' +\n        'getEditorObjectScope getElevationOffset getEnvSoundController getFatigue getForcedFlagTexture ' +\n        'getFriend getFSMVariable getFuelCargo getGroupIcon getGroupIconParams getGroupIcons getHideFrom ' +\n        'getHit getHitIndex getHitPointDamage getItemCargo getMagazineCargo getMarkerColor getMarkerPos ' +\n        'getMarkerSize getMarkerType getMass getMissionConfig getMissionConfigValue getMissionDLCs ' +\n        'getMissionLayerEntities getModelInfo getMousePosition getMusicPlayedTime getNumber ' +\n        'getObjectArgument getObjectChildren getObjectDLC getObjectMaterials getObjectProxy ' +\n        'getObjectTextures getObjectType getObjectViewDistance getOxygenRemaining getPersonUsedDLCs ' +\n        'getPilotCameraDirection getPilotCameraPosition getPilotCameraRotation getPilotCameraTarget ' +\n        'getPlateNumber getPlayerChannel getPlayerScores getPlayerUID getPos getPosASL getPosASLVisual ' +\n        'getPosASLW getPosATL getPosATLVisual getPosVisual getPosWorld getPylonMagazines getRelDir ' +\n        'getRelPos getRemoteSensorsDisabled getRepairCargo getResolution getShadowDistance getShotParents ' +\n        'getSlingLoad getSoundController getSoundControllerResult getSpeed getStamina getStatValue ' +\n        'getSuppression getTerrainGrid getTerrainHeightASL getText getTotalDLCUsageTime getUnitLoadout ' +\n        'getUnitTrait getUserMFDText getUserMFDvalue getVariable getVehicleCargo getWeaponCargo ' +\n        'getWeaponSway getWingsOrientationRTD getWingsPositionRTD getWPPos glanceAt globalChat globalRadio ' +\n        'goggles goto group groupChat groupFromNetId groupIconSelectable groupIconsVisible groupId ' +\n        'groupOwner groupRadio groupSelectedUnits groupSelectUnit gunner gusts halt handgunItems ' +\n        'handgunMagazine handgunWeapon handsHit hasInterface hasPilotCamera hasWeapon hcAllGroups ' +\n        'hcGroupParams hcLeader hcRemoveAllGroups hcRemoveGroup hcSelected hcSelectGroup hcSetGroup ' +\n        'hcShowBar hcShownBar headgear hideBody hideObject hideObjectGlobal hideSelection hint hintC ' +\n        'hintCadet hintSilent hmd hostMission htmlLoad HUDMovementLevels humidity image importAllGroups ' +\n        'importance in inArea inAreaArray incapacitatedState inflame inflamed infoPanel ' +\n        'infoPanelComponentEnabled infoPanelComponents infoPanels inGameUISetEventHandler inheritsFrom ' +\n        'initAmbientLife inPolygon inputAction inRangeOfArtillery insertEditorObject intersect is3DEN ' +\n        'is3DENMultiplayer isAbleToBreathe isAgent isArray isAutoHoverOn isAutonomous isAutotest ' +\n        'isBleeding isBurning isClass isCollisionLightOn isCopilotEnabled isDamageAllowed isDedicated ' +\n        'isDLCAvailable isEngineOn isEqualTo isEqualType isEqualTypeAll isEqualTypeAny isEqualTypeArray ' +\n        'isEqualTypeParams isFilePatchingEnabled isFlashlightOn isFlatEmpty isForcedWalk isFormationLeader ' +\n        'isGroupDeletedWhenEmpty isHidden isInRemainsCollector isInstructorFigureEnabled isIRLaserOn ' +\n        'isKeyActive isKindOf isLaserOn isLightOn isLocalized isManualFire isMarkedForCollection ' +\n        'isMultiplayer isMultiplayerSolo isNil isNull isNumber isObjectHidden isObjectRTD isOnRoad ' +\n        'isPipEnabled isPlayer isRealTime isRemoteExecuted isRemoteExecutedJIP isServer isShowing3DIcons ' +\n        'isSimpleObject isSprintAllowed isStaminaEnabled isSteamMission isStreamFriendlyUIEnabled isText ' +\n        'isTouchingGround isTurnedOut isTutHintsEnabled isUAVConnectable isUAVConnected isUIContext ' +\n        'isUniformAllowed isVehicleCargo isVehicleRadarOn isVehicleSensorEnabled isWalking ' +\n        'isWeaponDeployed isWeaponRested itemCargo items itemsWithMagazines join joinAs joinAsSilent ' +\n        'joinSilent joinString kbAddDatabase kbAddDatabaseTargets kbAddTopic kbHasTopic kbReact ' +\n        'kbRemoveTopic kbTell kbWasSaid keyImage keyName knowsAbout land landAt landResult language ' +\n        'laserTarget lbAdd lbClear lbColor lbColorRight lbCurSel lbData lbDelete lbIsSelected lbPicture ' +\n        'lbPictureRight lbSelection lbSetColor lbSetColorRight lbSetCurSel lbSetData lbSetPicture ' +\n        'lbSetPictureColor lbSetPictureColorDisabled lbSetPictureColorSelected lbSetPictureRight ' +\n        'lbSetPictureRightColor lbSetPictureRightColorDisabled lbSetPictureRightColorSelected ' +\n        'lbSetSelectColor lbSetSelectColorRight lbSetSelected lbSetText lbSetTextRight lbSetTooltip ' +\n        'lbSetValue lbSize lbSort lbSortByValue lbText lbTextRight lbValue leader leaderboardDeInit ' +\n        'leaderboardGetRows leaderboardInit leaderboardRequestRowsFriends leaderboardsRequestUploadScore ' +\n        'leaderboardsRequestUploadScoreKeepBest leaderboardState leaveVehicle libraryCredits ' +\n        'libraryDisclaimers lifeState lightAttachObject lightDetachObject lightIsOn lightnings limitSpeed ' +\n        'linearConversion lineIntersects lineIntersectsObjs lineIntersectsSurfaces lineIntersectsWith ' +\n        'linkItem list listObjects listRemoteTargets listVehicleSensors ln lnbAddArray lnbAddColumn ' +\n        'lnbAddRow lnbClear lnbColor lnbCurSelRow lnbData lnbDeleteColumn lnbDeleteRow ' +\n        'lnbGetColumnsPosition lnbPicture lnbSetColor lnbSetColumnsPos lnbSetCurSelRow lnbSetData ' +\n        'lnbSetPicture lnbSetText lnbSetValue lnbSize lnbSort lnbSortByValue lnbText lnbValue load loadAbs ' +\n        'loadBackpack loadFile loadGame loadIdentity loadMagazine loadOverlay loadStatus loadUniform ' +\n        'loadVest local localize locationPosition lock lockCameraTo lockCargo lockDriver locked ' +\n        'lockedCargo lockedDriver lockedTurret lockIdentity lockTurret lockWP log logEntities logNetwork ' +\n        'logNetworkTerminate lookAt lookAtPos magazineCargo magazines magazinesAllTurrets magazinesAmmo ' +\n        'magazinesAmmoCargo magazinesAmmoFull magazinesDetail magazinesDetailBackpack ' +\n        'magazinesDetailUniform magazinesDetailVest magazinesTurret magazineTurretAmmo mapAnimAdd ' +\n        'mapAnimClear mapAnimCommit mapAnimDone mapCenterOnCamera mapGridPosition markAsFinishedOnSteam ' +\n        'markerAlpha markerBrush markerColor markerDir markerPos markerShape markerSize markerText ' +\n        'markerType max members menuAction menuAdd menuChecked menuClear menuCollapse menuData menuDelete ' +\n        'menuEnable menuEnabled menuExpand menuHover menuPicture menuSetAction menuSetCheck menuSetData ' +\n        'menuSetPicture menuSetValue menuShortcut menuShortcutText menuSize menuSort menuText menuURL ' +\n        'menuValue min mineActive mineDetectedBy missionConfigFile missionDifficulty missionName ' +\n        'missionNamespace missionStart missionVersion mod modelToWorld modelToWorldVisual ' +\n        'modelToWorldVisualWorld modelToWorldWorld modParams moonIntensity moonPhase morale move ' +\n        'move3DENCamera moveInAny moveInCargo moveInCommander moveInDriver moveInGunner moveInTurret ' +\n        'moveObjectToEnd moveOut moveTime moveTo moveToCompleted moveToFailed musicVolume name nameSound ' +\n        'nearEntities nearestBuilding nearestLocation nearestLocations nearestLocationWithDubbing ' +\n        'nearestObject nearestObjects nearestTerrainObjects nearObjects nearObjectsReady nearRoads ' +\n        'nearSupplies nearTargets needReload netId netObjNull newOverlay nextMenuItemIndex ' +\n        'nextWeatherChange nMenuItems not numberOfEnginesRTD numberToDate objectCurators objectFromNetId ' +\n        'objectParent objStatus onBriefingGroup onBriefingNotes onBriefingPlan onBriefingTeamSwitch ' +\n        'onCommandModeChanged onDoubleClick onEachFrame onGroupIconClick onGroupIconOverEnter ' +\n        'onGroupIconOverLeave onHCGroupSelectionChanged onMapSingleClick onPlayerConnected ' +\n        'onPlayerDisconnected onPreloadFinished onPreloadStarted onShowNewObject onTeamSwitch ' +\n        'openCuratorInterface openDLCPage openMap openSteamApp openYoutubeVideo or orderGetIn overcast ' +\n        'overcastForecast owner param params parseNumber parseSimpleArray parseText parsingNamespace ' +\n        'particlesQuality pickWeaponPool pitch pixelGrid pixelGridBase pixelGridNoUIScale pixelH pixelW ' +\n        'playableSlotsNumber playableUnits playAction playActionNow player playerRespawnTime playerSide ' +\n        'playersNumber playGesture playMission playMove playMoveNow playMusic playScriptedMission ' +\n        'playSound playSound3D position positionCameraToWorld posScreenToWorld posWorldToScreen ' +\n        'ppEffectAdjust ppEffectCommit ppEffectCommitted ppEffectCreate ppEffectDestroy ppEffectEnable ' +\n        'ppEffectEnabled ppEffectForceInNVG precision preloadCamera preloadObject preloadSound ' +\n        'preloadTitleObj preloadTitleRsc preprocessFile preprocessFileLineNumbers primaryWeapon ' +\n        'primaryWeaponItems primaryWeaponMagazine priority processDiaryLink productVersion profileName ' +\n        'profileNamespace profileNameSteam progressLoadingScreen progressPosition progressSetPosition ' +\n        'publicVariable publicVariableClient publicVariableServer pushBack pushBackUnique putWeaponPool ' +\n        'queryItemsPool queryMagazinePool queryWeaponPool rad radioChannelAdd radioChannelCreate ' +\n        'radioChannelRemove radioChannelSetCallSign radioChannelSetLabel radioVolume rain rainbow random ' +\n        'rank rankId rating rectangular registeredTasks registerTask reload reloadEnabled remoteControl ' +\n        'remoteExec remoteExecCall remoteExecutedOwner remove3DENConnection remove3DENEventHandler ' +\n        'remove3DENLayer removeAction removeAll3DENEventHandlers removeAllActions removeAllAssignedItems ' +\n        'removeAllContainers removeAllCuratorAddons removeAllCuratorCameraAreas ' +\n        'removeAllCuratorEditingAreas removeAllEventHandlers removeAllHandgunItems removeAllItems ' +\n        'removeAllItemsWithMagazines removeAllMissionEventHandlers removeAllMPEventHandlers ' +\n        'removeAllMusicEventHandlers removeAllOwnedMines removeAllPrimaryWeaponItems removeAllWeapons ' +\n        'removeBackpack removeBackpackGlobal removeCuratorAddons removeCuratorCameraArea ' +\n        'removeCuratorEditableObjects removeCuratorEditingArea removeDrawIcon removeDrawLinks ' +\n        'removeEventHandler removeFromRemainsCollector removeGoggles removeGroupIcon removeHandgunItem ' +\n        'removeHeadgear removeItem removeItemFromBackpack removeItemFromUniform removeItemFromVest ' +\n        'removeItems removeMagazine removeMagazineGlobal removeMagazines removeMagazinesTurret ' +\n        'removeMagazineTurret removeMenuItem removeMissionEventHandler removeMPEventHandler ' +\n        'removeMusicEventHandler removeOwnedMine removePrimaryWeaponItem removeSecondaryWeaponItem ' +\n        'removeSimpleTask removeSwitchableUnit removeTeamMember removeUniform removeVest removeWeapon ' +\n        'removeWeaponAttachmentCargo removeWeaponCargo removeWeaponGlobal removeWeaponTurret ' +\n        'reportRemoteTarget requiredVersion resetCamShake resetSubgroupDirection resize resources ' +\n        'respawnVehicle restartEditorCamera reveal revealMine reverse reversedMouseY roadAt ' +\n        'roadsConnectedTo roleDescription ropeAttachedObjects ropeAttachedTo ropeAttachEnabled ' +\n        'ropeAttachTo ropeCreate ropeCut ropeDestroy ropeDetach ropeEndPosition ropeLength ropes ' +\n        'ropeUnwind ropeUnwound rotorsForcesRTD rotorsRpmRTD round runInitScript safeZoneH safeZoneW ' +\n        'safeZoneWAbs safeZoneX safeZoneXAbs safeZoneY save3DENInventory saveGame saveIdentity ' +\n        'saveJoysticks saveOverlay saveProfileNamespace saveStatus saveVar savingEnabled say say2D say3D ' +\n        'scopeName score scoreSide screenshot screenToWorld scriptDone scriptName scudState ' +\n        'secondaryWeapon secondaryWeaponItems secondaryWeaponMagazine select selectBestPlaces ' +\n        'selectDiarySubject selectedEditorObjects selectEditorObject selectionNames selectionPosition ' +\n        'selectLeader selectMax selectMin selectNoPlayer selectPlayer selectRandom selectRandomWeighted ' +\n        'selectWeapon selectWeaponTurret sendAUMessage sendSimpleCommand sendTask sendTaskResult ' +\n        'sendUDPMessage serverCommand serverCommandAvailable serverCommandExecutable serverName serverTime ' +\n        'set set3DENAttribute set3DENAttributes set3DENGrid set3DENIconsVisible set3DENLayer ' +\n        'set3DENLinesVisible set3DENLogicType set3DENMissionAttribute set3DENMissionAttributes ' +\n        'set3DENModelsVisible set3DENObjectType set3DENSelected setAccTime setActualCollectiveRTD ' +\n        'setAirplaneThrottle setAirportSide setAmmo setAmmoCargo setAmmoOnPylon setAnimSpeedCoef ' +\n        'setAperture setApertureNew setArmoryPoints setAttributes setAutonomous setBehaviour ' +\n        'setBleedingRemaining setBrakesRTD setCameraInterest setCamShakeDefParams setCamShakeParams ' +\n        'setCamUseTI setCaptive setCenterOfMass setCollisionLight setCombatMode setCompassOscillation ' +\n        'setConvoySeparation setCuratorCameraAreaCeiling setCuratorCoef setCuratorEditingAreaType ' +\n        'setCuratorWaypointCost setCurrentChannel setCurrentTask setCurrentWaypoint setCustomAimCoef ' +\n        'setCustomWeightRTD setDamage setDammage setDate setDebriefingText setDefaultCamera setDestination ' +\n        'setDetailMapBlendPars setDir setDirection setDrawIcon setDriveOnPath setDropInterval ' +\n        'setDynamicSimulationDistance setDynamicSimulationDistanceCoef setEditorMode setEditorObjectScope ' +\n        'setEffectCondition setEngineRPMRTD setFace setFaceAnimation setFatigue setFeatureType ' +\n        'setFlagAnimationPhase setFlagOwner setFlagSide setFlagTexture setFog setFormation ' +\n        'setFormationTask setFormDir setFriend setFromEditor setFSMVariable setFuel setFuelCargo ' +\n        'setGroupIcon setGroupIconParams setGroupIconsSelectable setGroupIconsVisible setGroupId ' +\n        'setGroupIdGlobal setGroupOwner setGusts setHideBehind setHit setHitIndex setHitPointDamage ' +\n        'setHorizonParallaxCoef setHUDMovementLevels setIdentity setImportance setInfoPanel setLeader ' +\n        'setLightAmbient setLightAttenuation setLightBrightness setLightColor setLightDayLight ' +\n        'setLightFlareMaxDistance setLightFlareSize setLightIntensity setLightnings setLightUseFlare ' +\n        'setLocalWindParams setMagazineTurretAmmo setMarkerAlpha setMarkerAlphaLocal setMarkerBrush ' +\n        'setMarkerBrushLocal setMarkerColor setMarkerColorLocal setMarkerDir setMarkerDirLocal ' +\n        'setMarkerPos setMarkerPosLocal setMarkerShape setMarkerShapeLocal setMarkerSize ' +\n        'setMarkerSizeLocal setMarkerText setMarkerTextLocal setMarkerType setMarkerTypeLocal setMass ' +\n        'setMimic setMousePosition setMusicEffect setMusicEventHandler setName setNameSound ' +\n        'setObjectArguments setObjectMaterial setObjectMaterialGlobal setObjectProxy setObjectTexture ' +\n        'setObjectTextureGlobal setObjectViewDistance setOvercast setOwner setOxygenRemaining ' +\n        'setParticleCircle setParticleClass setParticleFire setParticleParams setParticleRandom ' +\n        'setPilotCameraDirection setPilotCameraRotation setPilotCameraTarget setPilotLight setPiPEffect ' +\n        'setPitch setPlateNumber setPlayable setPlayerRespawnTime setPos setPosASL setPosASL2 setPosASLW ' +\n        'setPosATL setPosition setPosWorld setPylonLoadOut setPylonsPriority setRadioMsg setRain ' +\n        'setRainbow setRandomLip setRank setRectangular setRepairCargo setRotorBrakeRTD setShadowDistance ' +\n        'setShotParents setSide setSimpleTaskAlwaysVisible setSimpleTaskCustomData ' +\n        'setSimpleTaskDescription setSimpleTaskDestination setSimpleTaskTarget setSimpleTaskType ' +\n        'setSimulWeatherLayers setSize setSkill setSlingLoad setSoundEffect setSpeaker setSpeech ' +\n        'setSpeedMode setStamina setStaminaScheme setStatValue setSuppression setSystemOfUnits ' +\n        'setTargetAge setTaskMarkerOffset setTaskResult setTaskState setTerrainGrid setText ' +\n        'setTimeMultiplier setTitleEffect setTrafficDensity setTrafficDistance setTrafficGap ' +\n        'setTrafficSpeed setTriggerActivation setTriggerArea setTriggerStatements setTriggerText ' +\n        'setTriggerTimeout setTriggerType setType setUnconscious setUnitAbility setUnitLoadout setUnitPos ' +\n        'setUnitPosWeak setUnitRank setUnitRecoilCoefficient setUnitTrait setUnloadInCombat ' +\n        'setUserActionText setUserMFDText setUserMFDvalue setVariable setVectorDir setVectorDirAndUp ' +\n        'setVectorUp setVehicleAmmo setVehicleAmmoDef setVehicleArmor setVehicleCargo setVehicleId ' +\n        'setVehicleLock setVehiclePosition setVehicleRadar setVehicleReceiveRemoteTargets ' +\n        'setVehicleReportOwnPosition setVehicleReportRemoteTargets setVehicleTIPars setVehicleVarName ' +\n        'setVelocity setVelocityModelSpace setVelocityTransformation setViewDistance ' +\n        'setVisibleIfTreeCollapsed setWantedRPMRTD setWaves setWaypointBehaviour setWaypointCombatMode ' +\n        'setWaypointCompletionRadius setWaypointDescription setWaypointForceBehaviour setWaypointFormation ' +\n        'setWaypointHousePosition setWaypointLoiterRadius setWaypointLoiterType setWaypointName ' +\n        'setWaypointPosition setWaypointScript setWaypointSpeed setWaypointStatements setWaypointTimeout ' +\n        'setWaypointType setWaypointVisible setWeaponReloadingTime setWind setWindDir setWindForce ' +\n        'setWindStr setWingForceScaleRTD setWPPos show3DIcons showChat showCinemaBorder showCommandingMenu ' +\n        'showCompass showCuratorCompass showGPS showHUD showLegend showMap shownArtilleryComputer ' +\n        'shownChat shownCompass shownCuratorCompass showNewEditorObject shownGPS shownHUD shownMap ' +\n        'shownPad shownRadio shownScoretable shownUAVFeed shownWarrant shownWatch showPad showRadio ' +\n        'showScoretable showSubtitles showUAVFeed showWarrant showWatch showWaypoint showWaypoints side ' +\n        'sideChat sideEnemy sideFriendly sideRadio simpleTasks simulationEnabled simulCloudDensity ' +\n        'simulCloudOcclusion simulInClouds simulWeatherSync sin size sizeOf skill skillFinal skipTime ' +\n        'sleep sliderPosition sliderRange sliderSetPosition sliderSetRange sliderSetSpeed sliderSpeed ' +\n        'slingLoadAssistantShown soldierMagazines someAmmo sort soundVolume spawn speaker speed speedMode ' +\n        'splitString sqrt squadParams stance startLoadingScreen step stop stopEngineRTD stopped str ' +\n        'sunOrMoon supportInfo suppressFor surfaceIsWater surfaceNormal surfaceType swimInDepth ' +\n        'switchableUnits switchAction switchCamera switchGesture switchLight switchMove ' +\n        'synchronizedObjects synchronizedTriggers synchronizedWaypoints synchronizeObjectsAdd ' +\n        'synchronizeObjectsRemove synchronizeTrigger synchronizeWaypoint systemChat systemOfUnits tan ' +\n        'targetKnowledge targets targetsAggregate targetsQuery taskAlwaysVisible taskChildren ' +\n        'taskCompleted taskCustomData taskDescription taskDestination taskHint taskMarkerOffset taskParent ' +\n        'taskResult taskState taskType teamMember teamName teams teamSwitch teamSwitchEnabled teamType ' +\n        'terminate terrainIntersect terrainIntersectASL terrainIntersectAtASL text textLog textLogFormat ' +\n        'tg time timeMultiplier titleCut titleFadeOut titleObj titleRsc titleText toArray toFixed toLower ' +\n        'toString toUpper triggerActivated triggerActivation triggerArea triggerAttachedVehicle ' +\n        'triggerAttachObject triggerAttachVehicle triggerDynamicSimulation triggerStatements triggerText ' +\n        'triggerTimeout triggerTimeoutCurrent triggerType turretLocal turretOwner turretUnit tvAdd tvClear ' +\n        'tvCollapse tvCollapseAll tvCount tvCurSel tvData tvDelete tvExpand tvExpandAll tvPicture ' +\n        'tvSetColor tvSetCurSel tvSetData tvSetPicture tvSetPictureColor tvSetPictureColorDisabled ' +\n        'tvSetPictureColorSelected tvSetPictureRight tvSetPictureRightColor tvSetPictureRightColorDisabled ' +\n        'tvSetPictureRightColorSelected tvSetText tvSetTooltip tvSetValue tvSort tvSortByValue tvText ' +\n        'tvTooltip tvValue type typeName typeOf UAVControl uiNamespace uiSleep unassignCurator ' +\n        'unassignItem unassignTeam unassignVehicle underwater uniform uniformContainer uniformItems ' +\n        'uniformMagazines unitAddons unitAimPosition unitAimPositionVisual unitBackpack unitIsUAV unitPos ' +\n        'unitReady unitRecoilCoefficient units unitsBelowHeight unlinkItem unlockAchievement ' +\n        'unregisterTask updateDrawIcon updateMenuItem updateObjectTree useAISteeringComponent ' +\n        'useAudioTimeForMoves userInputDisabled vectorAdd vectorCos vectorCrossProduct vectorDiff ' +\n        'vectorDir vectorDirVisual vectorDistance vectorDistanceSqr vectorDotProduct vectorFromTo ' +\n        'vectorMagnitude vectorMagnitudeSqr vectorModelToWorld vectorModelToWorldVisual vectorMultiply ' +\n        'vectorNormalized vectorUp vectorUpVisual vectorWorldToModel vectorWorldToModelVisual vehicle ' +\n        'vehicleCargoEnabled vehicleChat vehicleRadio vehicleReceiveRemoteTargets vehicleReportOwnPosition ' +\n        'vehicleReportRemoteTargets vehicles vehicleVarName velocity velocityModelSpace verifySignature ' +\n        'vest vestContainer vestItems vestMagazines viewDistance visibleCompass visibleGPS visibleMap ' +\n        'visiblePosition visiblePositionASL visibleScoretable visibleWatch waves waypointAttachedObject ' +\n        'waypointAttachedVehicle waypointAttachObject waypointAttachVehicle waypointBehaviour ' +\n        'waypointCombatMode waypointCompletionRadius waypointDescription waypointForceBehaviour ' +\n        'waypointFormation waypointHousePosition waypointLoiterRadius waypointLoiterType waypointName ' +\n        'waypointPosition waypoints waypointScript waypointsEnabledUAV waypointShow waypointSpeed ' +\n        'waypointStatements waypointTimeout waypointTimeoutCurrent waypointType waypointVisible ' +\n        'weaponAccessories weaponAccessoriesCargo weaponCargo weaponDirection weaponInertia weaponLowered ' +\n        'weapons weaponsItems weaponsItemsCargo weaponState weaponsTurret weightRTD WFSideText wind ',\n      literal:\n        'blufor civilian configNull controlNull displayNull east endl false grpNull independent lineBreak ' +\n        'locationNull nil objNull opfor pi resistance scriptNull sideAmbientLife sideEmpty sideLogic ' +\n        'sideUnknown taskNull teamMemberNull true west'\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.NUMBER_MODE,\n      VARIABLE,\n      FUNCTION,\n      STRINGS,\n      PREPROCESSOR\n    ],\n    illegal: /#|^\\$ /\n  };\n}\n\nmodule.exports = sqf;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,IAAI,MAAM;AAEjB,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAIA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAIA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAE;AAAA,cACV,OAAO;AAAA,cACP,WAAW;AAAA,YACb,CAAE;AAAA,UACJ;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAE;AAAA,cACV,OAAO;AAAA,cACP,WAAW;AAAA,YACb,CAAE;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAIA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,gBACE;AAAA,QACJ;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA,KAAK,QAAQ,SAAS;AAAA,YACpB,WAAW;AAAA,UACb,CAAC;AAAA,UACD;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,UACX;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,SACE;AAAA,UAEF,UACE;AAAA,UAyVF,SACE;AAAA,QAGJ;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}