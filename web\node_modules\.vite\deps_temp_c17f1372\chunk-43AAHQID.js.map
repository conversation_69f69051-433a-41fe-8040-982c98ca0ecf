{"version": 3, "sources": ["../../highlight.js/lib/languages/groovy.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\n Language: Groovy\n Author: <PERSON> <<EMAIL>>\n Description: Groovy programming language implementation inspired from Vsevolod's Java mode\n Website: https://groovy-lang.org\n */\n\nfunction variants(variants, obj = {}) {\n  obj.variants = variants;\n  return obj;\n}\n\nfunction groovy(hljs) {\n  const IDENT_RE = '[A-Za-z0-9_$]+';\n  const COMMENT = variants([\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    hljs.COMMENT(\n      '/\\\\*\\\\*',\n      '\\\\*/',\n      {\n        relevance: 0,\n        contains: [\n          {\n            // eat up @'s in emails to prevent them to be recognized as doctags\n            begin: /\\w+@/,\n            relevance: 0\n          },\n          {\n            className: 'doctag',\n            begin: '@[A-Za-z]+'\n          }\n        ]\n      }\n    )\n  ]);\n  const REGEXP = {\n    className: 'regexp',\n    begin: /~?\\/[^\\/\\n]+\\//,\n    contains: [ hljs.BACKSLASH_ESCAPE ]\n  };\n  const NUMBER = variants([\n    hljs.BINARY_NUMBER_MODE,\n    hljs.C_NUMBER_MODE\n  ]);\n  const STRING = variants([\n    {\n      begin: /\"\"\"/,\n      end: /\"\"\"/\n    },\n    {\n      begin: /'''/,\n      end: /'''/\n    },\n    {\n      begin: \"\\\\$/\",\n      end: \"/\\\\$\",\n      relevance: 10\n    },\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ],\n  {\n    className: \"string\"\n  }\n  );\n\n  return {\n    name: 'Groovy',\n    keywords: {\n      built_in: 'this super',\n      literal: 'true false null',\n      keyword:\n            'byte short char int long boolean float double void ' +\n            // groovy specific keywords\n            'def as in assert trait ' +\n            // common keywords with Java\n            'abstract static volatile transient public private protected synchronized final ' +\n            'class interface enum if else for while switch case break default continue ' +\n            'throw throws try catch finally implements extends new import package return instanceof'\n    },\n    contains: [\n      hljs.SHEBANG({\n        binary: \"groovy\",\n        relevance: 10\n      }),\n      COMMENT,\n      STRING,\n      REGEXP,\n      NUMBER,\n      {\n        className: 'class',\n        beginKeywords: 'class interface trait enum',\n        end: /\\{/,\n        illegal: ':',\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        className: 'meta',\n        begin: '@[A-Za-z]+',\n        relevance: 0\n      },\n      {\n        // highlight map keys and named parameters as attrs\n        className: 'attr',\n        begin: IDENT_RE + '[ \\t]*:',\n        relevance: 0\n      },\n      {\n        // catch middle element of the ternary operator\n        // to avoid highlight it as a label, named parameter, or map key\n        begin: /\\?/,\n        end: /:/,\n        relevance: 0,\n        contains: [\n          COMMENT,\n          STRING,\n          REGEXP,\n          NUMBER,\n          'self'\n        ]\n      },\n      {\n        // highlight labeled statements\n        className: 'symbol',\n        begin: '^[ \\t]*' + lookahead(IDENT_RE + ':'),\n        excludeBegin: true,\n        end: IDENT_RE + ':',\n        relevance: 0\n      }\n    ],\n    illegal: /#|<\\//\n  };\n}\n\nmodule.exports = groovy;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,IAAI;AACrB,aAAO,OAAO,OAAO,IAAI,GAAG;AAAA,IAC9B;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AASA,aAAS,SAASA,WAAU,MAAM,CAAC,GAAG;AACpC,UAAI,WAAWA;AACf,aAAO;AAAA,IACT;AAEA,aAAS,OAAO,MAAM;AACpB,YAAM,WAAW;AACjB,YAAM,UAAU,SAAS;AAAA,QACvB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA;AAAA,gBAEE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,UAAU,CAAE,KAAK,gBAAiB;AAAA,MACpC;AACA,YAAM,SAAS,SAAS;AAAA,QACtB,KAAK;AAAA,QACL,KAAK;AAAA,MACP,CAAC;AACD,YAAM,SAAS;AAAA,QAAS;AAAA,UACtB;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,QACA;AAAA,UACE,WAAW;AAAA,QACb;AAAA,MACA;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SAAS;AAAA,UACT,SACM;AAAA,QAOR;AAAA,QACA,UAAU;AAAA,UACR,KAAK,QAAQ;AAAA,YACX,QAAQ;AAAA,YACR,WAAW;AAAA,UACb,CAAC;AAAA,UACD;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,cACjB;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO,WAAW;AAAA,YAClB,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO,WAAY,UAAU,WAAW,GAAG;AAAA,YAC3C,cAAc;AAAA,YACd,KAAK,WAAW;AAAA,YAChB,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["variants"]}