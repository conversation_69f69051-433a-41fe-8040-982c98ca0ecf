{"version": 3, "sources": ["../../highlight.js/lib/languages/inform7.js"], "sourcesContent": ["/*\nLanguage: Inform 7\nAuthor: <PERSON> <<EMAIL>>\nDescription: Language definition for Inform 7, a DSL for writing parser interactive fiction.\nWebsite: http://inform7.com\n*/\n\nfunction inform7(hljs) {\n  const START_BRACKET = '\\\\[';\n  const END_BRACKET = '\\\\]';\n  return {\n    name: 'Inform 7',\n    aliases: ['i7'],\n    case_insensitive: true,\n    keywords: {\n      // Some keywords more or less unique to I7, for relevance.\n      keyword:\n        // kind:\n        'thing room person man woman animal container ' +\n        'supporter backdrop door ' +\n        // characteristic:\n        'scenery open closed locked inside gender ' +\n        // verb:\n        'is are say understand ' +\n        // misc keyword:\n        'kind of rule'\n    },\n    contains: [\n      {\n        className: 'string',\n        begin: '\"',\n        end: '\"',\n        relevance: 0,\n        contains: [\n          {\n            className: 'subst',\n            begin: START_BRACKET,\n            end: END_BRACKET\n          }\n        ]\n      },\n      {\n        className: 'section',\n        begin: /^(Volume|Book|Part|Chapter|Section|Table)\\b/,\n        end: '$'\n      },\n      {\n        // Rule definition\n        // This is here for relevance.\n        begin: /^(Check|Carry out|Report|Instead of|To|Rule|When|Before|After)\\b/,\n        end: ':',\n        contains: [\n          {\n            // Rule name\n            begin: '\\\\(This',\n            end: '\\\\)'\n          }\n        ]\n      },\n      {\n        className: 'comment',\n        begin: START_BRACKET,\n        end: END_BRACKET,\n        contains: ['self']\n      }\n    ]\n  };\n}\n\nmodule.exports = inform7;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,QAAQ,MAAM;AACrB,YAAM,gBAAgB;AACtB,YAAM,cAAc;AACpB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,IAAI;AAAA,QACd,kBAAkB;AAAA,QAClB,UAAU;AAAA;AAAA,UAER;AAAA;AAAA,YAEE;AAAA;AAAA,QAQJ;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA;AAAA,gBAEE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC,MAAM;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}