{"version": 3, "sources": ["../../refractor/lang/lisp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = lisp\nlisp.displayName = 'lisp'\nlisp.aliases = []\nfunction lisp(Prism) {\n  ;(function (Prism) {\n    /**\n     * Functions to construct regular expressions\n     * e.g. (interactive ... or (interactive)\n     *\n     * @param {string} name\n     * @returns {RegExp}\n     */\n    function simple_form(name) {\n      return RegExp(/(\\()/.source + '(?:' + name + ')' + /(?=[\\s\\)])/.source)\n    }\n    /**\n     * booleans and numbers\n     *\n     * @param {string} pattern\n     * @returns {RegExp}\n     */\n    function primitive(pattern) {\n      return RegExp(\n        /([\\s([])/.source + '(?:' + pattern + ')' + /(?=[\\s)])/.source\n      )\n    } // Patterns in regular expressions\n    // Symbol name. See https://www.gnu.org/software/emacs/manual/html_node/elisp/Symbol-Type.html\n    // & and : are excluded as they are usually used for special purposes\n    var symbol = /(?!\\d)[-+*/~!@$%^=<>{}\\w]+/.source // symbol starting with & used in function arguments\n    var marker = '&' + symbol // Open parenthesis for look-behind\n    var par = '(\\\\()'\n    var endpar = '(?=\\\\))' // End the pattern with look-ahead space\n    var space = '(?=\\\\s)'\n    var nestedPar =\n      /(?:[^()]|\\((?:[^()]|\\((?:[^()]|\\((?:[^()]|\\((?:[^()]|\\([^()]*\\))*\\))*\\))*\\))*\\))*/\n        .source\n    var language = {\n      // Three or four semicolons are considered a heading.\n      // See https://www.gnu.org/software/emacs/manual/html_node/elisp/Comment-Tips.html\n      heading: {\n        pattern: /;;;.*/,\n        alias: ['comment', 'title']\n      },\n      comment: /;.*/,\n      string: {\n        pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        greedy: true,\n        inside: {\n          argument: /[-A-Z]+(?=[.,\\s])/,\n          symbol: RegExp('`' + symbol + \"'\")\n        }\n      },\n      'quoted-symbol': {\n        pattern: RegExp(\"#?'\" + symbol),\n        alias: ['variable', 'symbol']\n      },\n      'lisp-property': {\n        pattern: RegExp(':' + symbol),\n        alias: 'property'\n      },\n      splice: {\n        pattern: RegExp(',@?' + symbol),\n        alias: ['symbol', 'variable']\n      },\n      keyword: [\n        {\n          pattern: RegExp(\n            par +\n              '(?:and|(?:cl-)?letf|cl-loop|cond|cons|error|if|(?:lexical-)?let\\\\*?|message|not|null|or|provide|require|setq|unless|use-package|when|while)' +\n              space\n          ),\n          lookbehind: true\n        },\n        {\n          pattern: RegExp(\n            par +\n              '(?:append|by|collect|concat|do|finally|for|in|return)' +\n              space\n          ),\n          lookbehind: true\n        }\n      ],\n      declare: {\n        pattern: simple_form(/declare/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      interactive: {\n        pattern: simple_form(/interactive/.source),\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      boolean: {\n        pattern: primitive(/nil|t/.source),\n        lookbehind: true\n      },\n      number: {\n        pattern: primitive(/[-+]?\\d+(?:\\.\\d*)?/.source),\n        lookbehind: true\n      },\n      defvar: {\n        pattern: RegExp(par + 'def(?:const|custom|group|var)\\\\s+' + symbol),\n        lookbehind: true,\n        inside: {\n          keyword: /^def[a-z]+/,\n          variable: RegExp(symbol)\n        }\n      },\n      defun: {\n        pattern: RegExp(\n          par +\n            /(?:cl-)?(?:defmacro|defun\\*?)\\s+/.source +\n            symbol +\n            /\\s+\\(/.source +\n            nestedPar +\n            /\\)/.source\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^(?:cl-)?def\\S+/,\n          // See below, this property needs to be defined later so that it can\n          // reference the language object.\n          arguments: null,\n          function: {\n            pattern: RegExp('(^\\\\s)' + symbol),\n            lookbehind: true\n          },\n          punctuation: /[()]/\n        }\n      },\n      lambda: {\n        pattern: RegExp(\n          par +\n            'lambda\\\\s+\\\\(\\\\s*(?:&?' +\n            symbol +\n            '(?:\\\\s+&?' +\n            symbol +\n            ')*\\\\s*)?\\\\)'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          keyword: /^lambda/,\n          // See below, this property needs to be defined later so that it can\n          // reference the language object.\n          arguments: null,\n          punctuation: /[()]/\n        }\n      },\n      car: {\n        pattern: RegExp(par + symbol),\n        lookbehind: true\n      },\n      punctuation: [\n        // open paren, brackets, and close paren\n        /(?:['`,]?\\(|[)\\[\\]])/, // cons\n        {\n          pattern: /(\\s)\\.(?=\\s)/,\n          lookbehind: true\n        }\n      ]\n    }\n    var arg = {\n      'lisp-marker': RegExp(marker),\n      varform: {\n        pattern: RegExp(\n          /\\(/.source + symbol + /\\s+(?=\\S)/.source + nestedPar + /\\)/.source\n        ),\n        inside: language\n      },\n      argument: {\n        pattern: RegExp(/(^|[\\s(])/.source + symbol),\n        lookbehind: true,\n        alias: 'variable'\n      },\n      rest: language\n    }\n    var forms = '\\\\S+(?:\\\\s+\\\\S+)*'\n    var arglist = {\n      pattern: RegExp(par + nestedPar + endpar),\n      lookbehind: true,\n      inside: {\n        'rest-vars': {\n          pattern: RegExp('&(?:body|rest)\\\\s+' + forms),\n          inside: arg\n        },\n        'other-marker-vars': {\n          pattern: RegExp('&(?:aux|optional)\\\\s+' + forms),\n          inside: arg\n        },\n        keys: {\n          pattern: RegExp('&key\\\\s+' + forms + '(?:\\\\s+&allow-other-keys)?'),\n          inside: arg\n        },\n        argument: {\n          pattern: RegExp(symbol),\n          alias: 'variable'\n        },\n        punctuation: /[()]/\n      }\n    }\n    language['lambda'].inside.arguments = arglist\n    language['defun'].inside.arguments = Prism.util.clone(arglist)\n    language['defun'].inside.arguments.inside.sublist = arglist\n    Prism.languages.lisp = language\n    Prism.languages.elisp = language\n    Prism.languages.emacs = language\n    Prism.languages['emacs-lisp'] = language\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AAQjB,iBAAS,YAAY,MAAM;AACzB,iBAAO,OAAO,OAAO,SAAS,QAAQ,OAAO,MAAM,aAAa,MAAM;AAAA,QACxE;AAOA,iBAAS,UAAU,SAAS;AAC1B,iBAAO;AAAA,YACL,WAAW,SAAS,QAAQ,UAAU,MAAM,YAAY;AAAA,UAC1D;AAAA,QACF;AAGA,YAAI,SAAS,6BAA6B;AAC1C,YAAI,SAAS,MAAM;AACnB,YAAI,MAAM;AACV,YAAI,SAAS;AACb,YAAI,QAAQ;AACZ,YAAI,YACF,oFACG;AACL,YAAI,WAAW;AAAA;AAAA;AAAA,UAGb,SAAS;AAAA,YACP,SAAS;AAAA,YACT,OAAO,CAAC,WAAW,OAAO;AAAA,UAC5B;AAAA,UACA,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,UAAU;AAAA,cACV,QAAQ,OAAO,MAAM,SAAS,GAAG;AAAA,YACnC;AAAA,UACF;AAAA,UACA,iBAAiB;AAAA,YACf,SAAS,OAAO,QAAQ,MAAM;AAAA,YAC9B,OAAO,CAAC,YAAY,QAAQ;AAAA,UAC9B;AAAA,UACA,iBAAiB;AAAA,YACf,SAAS,OAAO,MAAM,MAAM;AAAA,YAC5B,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,YACN,SAAS,OAAO,QAAQ,MAAM;AAAA,YAC9B,OAAO,CAAC,UAAU,UAAU;AAAA,UAC9B;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,SAAS;AAAA,gBACP,MACE,gJACA;AAAA,cACJ;AAAA,cACA,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP,MACE,0DACA;AAAA,cACJ;AAAA,cACA,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP,SAAS,YAAY,UAAU,MAAM;AAAA,YACrC,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,aAAa;AAAA,YACX,SAAS,YAAY,cAAc,MAAM;AAAA,YACzC,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACP,SAAS,UAAU,QAAQ,MAAM;AAAA,YACjC,YAAY;AAAA,UACd;AAAA,UACA,QAAQ;AAAA,YACN,SAAS,UAAU,qBAAqB,MAAM;AAAA,YAC9C,YAAY;AAAA,UACd;AAAA,UACA,QAAQ;AAAA,YACN,SAAS,OAAO,MAAM,sCAAsC,MAAM;AAAA,YAClE,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,UAAU,OAAO,MAAM;AAAA,YACzB;AAAA,UACF;AAAA,UACA,OAAO;AAAA,YACL,SAAS;AAAA,cACP,MACE,mCAAmC,SACnC,SACA,QAAQ,SACR,YACA,KAAK;AAAA,YACT;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,SAAS;AAAA;AAAA;AAAA,cAGT,WAAW;AAAA,cACX,UAAU;AAAA,gBACR,SAAS,OAAO,WAAW,MAAM;AAAA,gBACjC,YAAY;AAAA,cACd;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,MACE,2BACA,SACA,cACA,SACA;AAAA,YACJ;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,SAAS;AAAA;AAAA;AAAA,cAGT,WAAW;AAAA,cACX,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,SAAS,OAAO,MAAM,MAAM;AAAA,YAC5B,YAAY;AAAA,UACd;AAAA,UACA,aAAa;AAAA;AAAA,YAEX;AAAA;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM;AAAA,UACR,eAAe,OAAO,MAAM;AAAA,UAC5B,SAAS;AAAA,YACP,SAAS;AAAA,cACP,KAAK,SAAS,SAAS,YAAY,SAAS,YAAY,KAAK;AAAA,YAC/D;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,UACA,UAAU;AAAA,YACR,SAAS,OAAO,YAAY,SAAS,MAAM;AAAA,YAC3C,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,QACR;AACA,YAAI,QAAQ;AACZ,YAAI,UAAU;AAAA,UACZ,SAAS,OAAO,MAAM,YAAY,MAAM;AAAA,UACxC,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,aAAa;AAAA,cACX,SAAS,OAAO,uBAAuB,KAAK;AAAA,cAC5C,QAAQ;AAAA,YACV;AAAA,YACA,qBAAqB;AAAA,cACnB,SAAS,OAAO,0BAA0B,KAAK;AAAA,cAC/C,QAAQ;AAAA,YACV;AAAA,YACA,MAAM;AAAA,cACJ,SAAS,OAAO,aAAa,QAAQ,4BAA4B;AAAA,cACjE,QAAQ;AAAA,YACV;AAAA,YACA,UAAU;AAAA,cACR,SAAS,OAAO,MAAM;AAAA,cACtB,OAAO;AAAA,YACT;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AACA,iBAAS,QAAQ,EAAE,OAAO,YAAY;AACtC,iBAAS,OAAO,EAAE,OAAO,YAAYA,OAAM,KAAK,MAAM,OAAO;AAC7D,iBAAS,OAAO,EAAE,OAAO,UAAU,OAAO,UAAU;AACpD,QAAAA,OAAM,UAAU,OAAO;AACvB,QAAAA,OAAM,UAAU,QAAQ;AACxB,QAAAA,OAAM,UAAU,QAAQ;AACxB,QAAAA,OAAM,UAAU,YAAY,IAAI;AAAA,MAClC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}