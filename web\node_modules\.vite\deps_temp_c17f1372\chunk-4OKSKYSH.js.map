{"version": 3, "sources": ["../../refractor/lang/maxscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = maxscript\nmaxscript.displayName = 'maxscript'\nmaxscript.aliases = []\nfunction maxscript(Prism) {\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:about|and|animate|as|at|attributes|by|case|catch|collect|continue|coordsys|do|else|exit|fn|for|from|function|global|if|in|local|macroscript|mapped|max|not|of|off|on|or|parameters|persistent|plugin|rcmenu|return|rollout|set|struct|then|throw|to|tool|try|undo|utility|when|where|while|with)\\b/i\n    Prism.languages.maxscript = {\n      comment: {\n        pattern: /\\/\\*[\\s\\S]*?(?:\\*\\/|$)|--.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /(^|[^\"\\\\@])(?:\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"|@\"[^\"]*\")/,\n        lookbehind: true,\n        greedy: true\n      },\n      path: {\n        pattern: /\\$(?:[\\w/\\\\.*?]|'[^']*')*/,\n        greedy: true,\n        alias: 'string'\n      },\n      'function-call': {\n        pattern: RegExp(\n          '((?:' + // start of line\n            (/^/.source +\n              '|' + // operators and other language constructs\n              /[;=<>+\\-*/^({\\[]/.source +\n              '|' + // keywords as part of statements\n              /\\b(?:and|by|case|catch|collect|do|else|if|in|not|or|return|then|to|try|where|while|with)\\b/\n                .source) +\n            ')[ \\t]*)' +\n            '(?!' +\n            keywords.source +\n            ')' +\n            /[a-z_]\\w*\\b/.source +\n            '(?=[ \\t]*(?:' + // variable\n            ('(?!' +\n              keywords.source +\n              ')' +\n              /[a-z_]/.source +\n              '|' + // number\n              /\\d|-\\.?\\d/.source +\n              '|' + // other expressions or literals\n              /[({'\"$@#?]/.source) +\n            '))',\n          'im'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'function'\n      },\n      'function-definition': {\n        pattern: /(\\b(?:fn|function)\\s+)\\w+\\b/i,\n        lookbehind: true,\n        alias: 'function'\n      },\n      argument: {\n        pattern: /\\b[a-z_]\\w*(?=:)/i,\n        alias: 'attr-name'\n      },\n      keyword: keywords,\n      boolean: /\\b(?:false|true)\\b/,\n      time: {\n        pattern:\n          /(^|[^\\w.])(?:(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eEdD][+-]\\d+|[LP])?[msft])+|\\d+:\\d+(?:\\.\\d*)?)(?![\\w.:])/,\n        lookbehind: true,\n        alias: 'number'\n      },\n      number: [\n        {\n          pattern:\n            /(^|[^\\w.])(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eEdD][+-]\\d+|[LP])?|0x[a-fA-F0-9]+)(?![\\w.:])/,\n          lookbehind: true\n        },\n        /\\b(?:e|pi)\\b/\n      ],\n      constant: /\\b(?:dontcollect|ok|silentValue|undefined|unsupplied)\\b/,\n      color: {\n        pattern: /\\b(?:black|blue|brown|gray|green|orange|red|white|yellow)\\b/i,\n        alias: 'constant'\n      },\n      operator: /[-+*/<>=!]=?|[&^?]|#(?!\\()/,\n      punctuation: /[()\\[\\]{}.:,;]|#(?=\\()|\\\\$/m\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,cAAU,cAAc;AACxB,cAAU,UAAU,CAAC;AACrB,aAAS,UAAU,OAAO;AACxB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,WACF;AACF,QAAAA,OAAM,UAAU,YAAY;AAAA,UAC1B,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,UACA,MAAM;AAAA,YACJ,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,iBAAiB;AAAA,YACf,SAAS;AAAA,cACP;AAAA,eACG,IAAI,SACH;AAAA,cACA,mBAAmB,SACnB;AAAA,cACA,6FACG,UACL,eAEA,SAAS,SACT,MACA,cAAc,SACd;AAAA,eACC,QACC,SAAS,SACT,MACA,SAAS,SACT;AAAA,cACA,YAAY,SACZ;AAAA,cACA,aAAa,UACf;AAAA,cACF;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,uBAAuB;AAAA,YACrB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM;AAAA,YACJ,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,YACN;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,UAAU;AAAA,UACV,OAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}