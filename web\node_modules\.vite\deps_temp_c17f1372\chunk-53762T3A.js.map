{"version": 3, "sources": ["../../refractor/lang/brightscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = brightscript\nbrightscript.displayName = 'brightscript'\nbrightscript.aliases = []\nfunction brightscript(Prism) {\n  Prism.languages.brightscript = {\n    comment: /(?:\\brem|').*/i,\n    'directive-statement': {\n      pattern: /(^[\\t ]*)#(?:const|else(?:[\\t ]+if)?|end[\\t ]+if|error|if).*/im,\n      lookbehind: true,\n      alias: 'property',\n      inside: {\n        'error-message': {\n          pattern: /(^#error).+/,\n          lookbehind: true\n        },\n        directive: {\n          pattern: /^#(?:const|else(?:[\\t ]+if)?|end[\\t ]+if|error|if)/,\n          alias: 'keyword'\n        },\n        expression: {\n          pattern: /[\\s\\S]+/,\n          inside: null // see below\n        }\n      }\n    },\n    property: {\n      pattern:\n        /([\\r\\n{,][\\t ]*)(?:(?!\\d)\\w+|\"(?:[^\"\\r\\n]|\"\")*\"(?!\"))(?=[ \\t]*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:[^\"\\r\\n]|\"\")*\"(?!\")/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\bAs[\\t ]+)\\w+/i,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:As|Dim|Each|Else|Elseif|End|Exit|For|Function|Goto|If|In|Print|Return|Step|Stop|Sub|Then|To|While)\\b/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    function: /\\b(?!\\d)\\w+(?=[\\t ]*\\()/,\n    number: /(?:\\b\\d+(?:\\.\\d+)?(?:[ed][+-]\\d+)?|&h[a-f\\d]+)\\b[%&!#]?/i,\n    operator:\n      /--|\\+\\+|>>=?|<<=?|<>|[-+*/\\\\<>]=?|[:^=?]|\\b(?:and|mod|not|or)\\b/i,\n    punctuation: /[.,;()[\\]{}]/,\n    constant: /\\b(?:LINE_NUM)\\b/i\n  }\n  Prism.languages.brightscript['directive-statement'].inside.expression.inside =\n    Prism.languages.brightscript\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,iBAAa,cAAc;AAC3B,iBAAa,UAAU,CAAC;AACxB,aAAS,aAAa,OAAO;AAC3B,YAAM,UAAU,eAAe;AAAA,QAC7B,SAAS;AAAA,QACT,uBAAuB;AAAA,UACrB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,iBAAiB;AAAA,cACf,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,WAAW;AAAA,cACT,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,YAAY;AAAA,cACV,SAAS;AAAA,cACT,QAAQ;AAAA;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,SACE;AAAA,QACF,SAAS;AAAA,QACT,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UACE;AAAA,QACF,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AACA,YAAM,UAAU,aAAa,qBAAqB,EAAE,OAAO,WAAW,SACpE,MAAM,UAAU;AAAA,IACpB;AAAA;AAAA;", "names": []}