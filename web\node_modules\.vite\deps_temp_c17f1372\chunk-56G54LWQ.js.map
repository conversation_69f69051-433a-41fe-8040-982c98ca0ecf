{"version": 3, "sources": ["../../refractor/lang/xml-doc.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = xmlDoc\nxmlDoc.displayName = 'xmlDoc'\nxmlDoc.aliases = []\nfunction xmlDoc(Prism) {\n  ;(function (Prism) {\n    /**\n     * If the given language is present, it will insert the given doc comment grammar token into it.\n     *\n     * @param {string} lang\n     * @param {any} docComment\n     */\n    function insertDocComment(lang, docComment) {\n      if (Prism.languages[lang]) {\n        Prism.languages.insertBefore(lang, 'comment', {\n          'doc-comment': docComment\n        })\n      }\n    }\n    var tag = Prism.languages.markup.tag\n    var slashDocComment = {\n      pattern: /\\/\\/\\/.*/,\n      greedy: true,\n      alias: 'comment',\n      inside: {\n        tag: tag\n      }\n    }\n    var tickDocComment = {\n      pattern: /'''.*/,\n      greedy: true,\n      alias: 'comment',\n      inside: {\n        tag: tag\n      }\n    }\n    insertDocComment('csharp', slashDocComment)\n    insertDocComment('fsharp', slashDocComment)\n    insertDocComment('vbnet', tickDocComment)\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB;AAAC,OAAC,SAAUA,QAAO;AAOjB,iBAAS,iBAAiB,MAAM,YAAY;AAC1C,cAAIA,OAAM,UAAU,IAAI,GAAG;AACzB,YAAAA,OAAM,UAAU,aAAa,MAAM,WAAW;AAAA,cAC5C,eAAe;AAAA,YACjB,CAAC;AAAA,UACH;AAAA,QACF;AACA,YAAI,MAAMA,OAAM,UAAU,OAAO;AACjC,YAAI,kBAAkB;AAAA,UACpB,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,YAAI,iBAAiB;AAAA,UACnB,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,QAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,yBAAiB,UAAU,eAAe;AAC1C,yBAAiB,UAAU,eAAe;AAC1C,yBAAiB,SAAS,cAAc;AAAA,MAC1C,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}