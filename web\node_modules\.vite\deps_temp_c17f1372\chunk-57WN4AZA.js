import {
  require_markup_templating
} from "./chunk-GK7VE4GW.js";
import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// node_modules/refractor/lang/php.js
var require_php = __commonJS({
  "node_modules/refractor/lang/php.js"(exports, module) {
    var refractorMarkupTemplating = require_markup_templating();
    module.exports = php;
    php.displayName = "php";
    php.aliases = [];
    function php(Prism) {
      Prism.register(refractorMarkupTemplating);
      (function(Prism2) {
        var comment = /\/\*[\s\S]*?\*\/|\/\/.*|#(?!\[).*/;
        var constant = [
          {
            pattern: /\b(?:false|true)\b/i,
            alias: "boolean"
          },
          {
            pattern: /(::\s*)\b[a-z_]\w*\b(?!\s*\()/i,
            greedy: true,
            lookbehind: true
          },
          {
            pattern: /(\b(?:case|const)\s+)\b[a-z_]\w*(?=\s*[;=])/i,
            greedy: true,
            lookbehind: true
          },
          /\b(?:null)\b/i,
          /\b[A-Z_][A-Z0-9_]*\b(?!\s*\()/
        ];
        var number = /\b0b[01]+(?:_[01]+)*\b|\b0o[0-7]+(?:_[0-7]+)*\b|\b0x[\da-f]+(?:_[\da-f]+)*\b|(?:\b\d+(?:_\d+)*\.?(?:\d+(?:_\d+)*)?|\B\.\d+)(?:e[+-]?\d+)?/i;
        var operator = /<?=>|\?\?=?|\.{3}|\??->|[!=]=?=?|::|\*\*=?|--|\+\+|&&|\|\||<<|>>|[?~]|[/^|%*&<>.+-]=?/;
        var punctuation = /[{}\[\](),:;]/;
        Prism2.languages.php = {
          delimiter: {
            pattern: /\?>$|^<\?(?:php(?=\s)|=)?/i,
            alias: "important"
          },
          comment,
          variable: /\$+(?:\w+\b|(?=\{))/,
          package: {
            pattern: /(namespace\s+|use\s+(?:function\s+)?)(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,
            lookbehind: true,
            inside: {
              punctuation: /\\/
            }
          },
          "class-name-definition": {
            pattern: /(\b(?:class|enum|interface|trait)\s+)\b[a-z_]\w*(?!\\)\b/i,
            lookbehind: true,
            alias: "class-name"
          },
          "function-definition": {
            pattern: /(\bfunction\s+)[a-z_]\w*(?=\s*\()/i,
            lookbehind: true,
            alias: "function"
          },
          keyword: [
            {
              pattern: /(\(\s*)\b(?:array|bool|boolean|float|int|integer|object|string)\b(?=\s*\))/i,
              alias: "type-casting",
              greedy: true,
              lookbehind: true
            },
            {
              pattern: /([(,?]\s*)\b(?:array(?!\s*\()|bool|callable|(?:false|null)(?=\s*\|)|float|int|iterable|mixed|object|self|static|string)\b(?=\s*\$)/i,
              alias: "type-hint",
              greedy: true,
              lookbehind: true
            },
            {
              pattern: /(\)\s*:\s*(?:\?\s*)?)\b(?:array(?!\s*\()|bool|callable|(?:false|null)(?=\s*\|)|float|int|iterable|mixed|object|self|static|string|void)\b/i,
              alias: "return-type",
              greedy: true,
              lookbehind: true
            },
            {
              pattern: /\b(?:array(?!\s*\()|bool|float|int|iterable|mixed|object|string|void)\b/i,
              alias: "type-declaration",
              greedy: true
            },
            {
              pattern: /(\|\s*)(?:false|null)\b|\b(?:false|null)(?=\s*\|)/i,
              alias: "type-declaration",
              greedy: true,
              lookbehind: true
            },
            {
              pattern: /\b(?:parent|self|static)(?=\s*::)/i,
              alias: "static-context",
              greedy: true
            },
            {
              // yield from
              pattern: /(\byield\s+)from\b/i,
              lookbehind: true
            },
            // `class` is always a keyword unlike other keywords
            /\bclass\b/i,
            {
              // https://www.php.net/manual/en/reserved.keywords.php
              //
              // keywords cannot be preceded by "->"
              // the complex lookbehind means `(?<!(?:->|::)\s*)`
              pattern: /((?:^|[^\s>:]|(?:^|[^-])>|(?:^|[^:]):)\s*)\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|new|or|parent|print|private|protected|public|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\b/i,
              lookbehind: true
            }
          ],
          "argument-name": {
            pattern: /([(,]\s+)\b[a-z_]\w*(?=\s*:(?!:))/i,
            lookbehind: true
          },
          "class-name": [
            {
              pattern: /(\b(?:extends|implements|instanceof|new(?!\s+self|\s+static))\s+|\bcatch\s*\()\b[a-z_]\w*(?!\\)\b/i,
              greedy: true,
              lookbehind: true
            },
            {
              pattern: /(\|\s*)\b[a-z_]\w*(?!\\)\b/i,
              greedy: true,
              lookbehind: true
            },
            {
              pattern: /\b[a-z_]\w*(?!\\)\b(?=\s*\|)/i,
              greedy: true
            },
            {
              pattern: /(\|\s*)(?:\\?\b[a-z_]\w*)+\b/i,
              alias: "class-name-fully-qualified",
              greedy: true,
              lookbehind: true,
              inside: {
                punctuation: /\\/
              }
            },
            {
              pattern: /(?:\\?\b[a-z_]\w*)+\b(?=\s*\|)/i,
              alias: "class-name-fully-qualified",
              greedy: true,
              inside: {
                punctuation: /\\/
              }
            },
            {
              pattern: /(\b(?:extends|implements|instanceof|new(?!\s+self\b|\s+static\b))\s+|\bcatch\s*\()(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,
              alias: "class-name-fully-qualified",
              greedy: true,
              lookbehind: true,
              inside: {
                punctuation: /\\/
              }
            },
            {
              pattern: /\b[a-z_]\w*(?=\s*\$)/i,
              alias: "type-declaration",
              greedy: true
            },
            {
              pattern: /(?:\\?\b[a-z_]\w*)+(?=\s*\$)/i,
              alias: ["class-name-fully-qualified", "type-declaration"],
              greedy: true,
              inside: {
                punctuation: /\\/
              }
            },
            {
              pattern: /\b[a-z_]\w*(?=\s*::)/i,
              alias: "static-context",
              greedy: true
            },
            {
              pattern: /(?:\\?\b[a-z_]\w*)+(?=\s*::)/i,
              alias: ["class-name-fully-qualified", "static-context"],
              greedy: true,
              inside: {
                punctuation: /\\/
              }
            },
            {
              pattern: /([(,?]\s*)[a-z_]\w*(?=\s*\$)/i,
              alias: "type-hint",
              greedy: true,
              lookbehind: true
            },
            {
              pattern: /([(,?]\s*)(?:\\?\b[a-z_]\w*)+(?=\s*\$)/i,
              alias: ["class-name-fully-qualified", "type-hint"],
              greedy: true,
              lookbehind: true,
              inside: {
                punctuation: /\\/
              }
            },
            {
              pattern: /(\)\s*:\s*(?:\?\s*)?)\b[a-z_]\w*(?!\\)\b/i,
              alias: "return-type",
              greedy: true,
              lookbehind: true
            },
            {
              pattern: /(\)\s*:\s*(?:\?\s*)?)(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,
              alias: ["class-name-fully-qualified", "return-type"],
              greedy: true,
              lookbehind: true,
              inside: {
                punctuation: /\\/
              }
            }
          ],
          constant,
          function: {
            pattern: /(^|[^\\\w])\\?[a-z_](?:[\w\\]*\w)?(?=\s*\()/i,
            lookbehind: true,
            inside: {
              punctuation: /\\/
            }
          },
          property: {
            pattern: /(->\s*)\w+/,
            lookbehind: true
          },
          number,
          operator,
          punctuation
        };
        var string_interpolation = {
          pattern: /\{\$(?:\{(?:\{[^{}]+\}|[^{}]+)\}|[^{}])+\}|(^|[^\\{])\$+(?:\w+(?:\[[^\r\n\[\]]+\]|->\w+)?)/,
          lookbehind: true,
          inside: Prism2.languages.php
        };
        var string = [
          {
            pattern: /<<<'([^']+)'[\r\n](?:.*[\r\n])*?\1;/,
            alias: "nowdoc-string",
            greedy: true,
            inside: {
              delimiter: {
                pattern: /^<<<'[^']+'|[a-z_]\w*;$/i,
                alias: "symbol",
                inside: {
                  punctuation: /^<<<'?|[';]$/
                }
              }
            }
          },
          {
            pattern: /<<<(?:"([^"]+)"[\r\n](?:.*[\r\n])*?\1;|([a-z_]\w*)[\r\n](?:.*[\r\n])*?\2;)/i,
            alias: "heredoc-string",
            greedy: true,
            inside: {
              delimiter: {
                pattern: /^<<<(?:"[^"]+"|[a-z_]\w*)|[a-z_]\w*;$/i,
                alias: "symbol",
                inside: {
                  punctuation: /^<<<"?|[";]$/
                }
              },
              interpolation: string_interpolation
            }
          },
          {
            pattern: /`(?:\\[\s\S]|[^\\`])*`/,
            alias: "backtick-quoted-string",
            greedy: true
          },
          {
            pattern: /'(?:\\[\s\S]|[^\\'])*'/,
            alias: "single-quoted-string",
            greedy: true
          },
          {
            pattern: /"(?:\\[\s\S]|[^\\"])*"/,
            alias: "double-quoted-string",
            greedy: true,
            inside: {
              interpolation: string_interpolation
            }
          }
        ];
        Prism2.languages.insertBefore("php", "variable", {
          string,
          attribute: {
            pattern: /#\[(?:[^"'\/#]|\/(?![*/])|\/\/.*$|#(?!\[).*$|\/\*(?:[^*]|\*(?!\/))*\*\/|"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*')+\](?=\s*[a-z$#])/im,
            greedy: true,
            inside: {
              "attribute-content": {
                pattern: /^(#\[)[\s\S]+(?=\]$)/,
                lookbehind: true,
                // inside can appear subset of php
                inside: {
                  comment,
                  string,
                  "attribute-class-name": [
                    {
                      pattern: /([^:]|^)\b[a-z_]\w*(?!\\)\b/i,
                      alias: "class-name",
                      greedy: true,
                      lookbehind: true
                    },
                    {
                      pattern: /([^:]|^)(?:\\?\b[a-z_]\w*)+/i,
                      alias: ["class-name", "class-name-fully-qualified"],
                      greedy: true,
                      lookbehind: true,
                      inside: {
                        punctuation: /\\/
                      }
                    }
                  ],
                  constant,
                  number,
                  operator,
                  punctuation
                }
              },
              delimiter: {
                pattern: /^#\[|\]$/,
                alias: "punctuation"
              }
            }
          }
        });
        Prism2.hooks.add("before-tokenize", function(env) {
          if (!/<\?/.test(env.code)) {
            return;
          }
          var phpPattern = /<\?(?:[^"'/#]|\/(?![*/])|("|')(?:\\[\s\S]|(?!\1)[^\\])*\1|(?:\/\/|#(?!\[))(?:[^?\n\r]|\?(?!>))*(?=$|\?>|[\r\n])|#\[|\/\*(?:[^*]|\*(?!\/))*(?:\*\/|$))*?(?:\?>|$)/g;
          Prism2.languages["markup-templating"].buildPlaceholders(
            env,
            "php",
            phpPattern
          );
        });
        Prism2.hooks.add("after-tokenize", function(env) {
          Prism2.languages["markup-templating"].tokenizePlaceholders(env, "php");
        });
      })(Prism);
    }
  }
});

export {
  require_php
};
//# sourceMappingURL=chunk-57WN4AZA.js.map
