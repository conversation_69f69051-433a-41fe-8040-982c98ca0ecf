{"version": 3, "sources": ["../../refractor/lang/php.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = php\nphp.displayName = 'php'\nphp.aliases = []\nfunction php(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  /**\n   * Original by <PERSON>: http://aahacreative.com/2012/07/31/php-syntax-highlighting-prism/\n   * Modified by <PERSON>: http://milesj.me\n   * Rewritten by <PERSON>\n   *\n   * Supports PHP 5.3 - 8.0\n   */\n  ;(function (Prism) {\n    var comment = /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*|#(?!\\[).*/\n    var constant = [\n      {\n        pattern: /\\b(?:false|true)\\b/i,\n        alias: 'boolean'\n      },\n      {\n        pattern: /(::\\s*)\\b[a-z_]\\w*\\b(?!\\s*\\()/i,\n        greedy: true,\n        lookbehind: true\n      },\n      {\n        pattern: /(\\b(?:case|const)\\s+)\\b[a-z_]\\w*(?=\\s*[;=])/i,\n        greedy: true,\n        lookbehind: true\n      },\n      /\\b(?:null)\\b/i,\n      /\\b[A-Z_][A-Z0-9_]*\\b(?!\\s*\\()/\n    ]\n    var number =\n      /\\b0b[01]+(?:_[01]+)*\\b|\\b0o[0-7]+(?:_[0-7]+)*\\b|\\b0x[\\da-f]+(?:_[\\da-f]+)*\\b|(?:\\b\\d+(?:_\\d+)*\\.?(?:\\d+(?:_\\d+)*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i\n    var operator =\n      /<?=>|\\?\\?=?|\\.{3}|\\??->|[!=]=?=?|::|\\*\\*=?|--|\\+\\+|&&|\\|\\||<<|>>|[?~]|[/^|%*&<>.+-]=?/\n    var punctuation = /[{}\\[\\](),:;]/\n    Prism.languages.php = {\n      delimiter: {\n        pattern: /\\?>$|^<\\?(?:php(?=\\s)|=)?/i,\n        alias: 'important'\n      },\n      comment: comment,\n      variable: /\\$+(?:\\w+\\b|(?=\\{))/,\n      package: {\n        pattern:\n          /(namespace\\s+|use\\s+(?:function\\s+)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      },\n      'class-name-definition': {\n        pattern: /(\\b(?:class|enum|interface|trait)\\s+)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      'function-definition': {\n        pattern: /(\\bfunction\\s+)[a-z_]\\w*(?=\\s*\\()/i,\n        lookbehind: true,\n        alias: 'function'\n      },\n      keyword: [\n        {\n          pattern:\n            /(\\(\\s*)\\b(?:array|bool|boolean|float|int|integer|object|string)\\b(?=\\s*\\))/i,\n          alias: 'type-casting',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /([(,?]\\s*)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string)\\b(?=\\s*\\$)/i,\n          alias: 'type-hint',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b(?:array(?!\\s*\\()|bool|callable|(?:false|null)(?=\\s*\\|)|float|int|iterable|mixed|object|self|static|string|void)\\b/i,\n          alias: 'return-type',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /\\b(?:array(?!\\s*\\()|bool|float|int|iterable|mixed|object|string|void)\\b/i,\n          alias: 'type-declaration',\n          greedy: true\n        },\n        {\n          pattern: /(\\|\\s*)(?:false|null)\\b|\\b(?:false|null)(?=\\s*\\|)/i,\n          alias: 'type-declaration',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /\\b(?:parent|self|static)(?=\\s*::)/i,\n          alias: 'static-context',\n          greedy: true\n        },\n        {\n          // yield from\n          pattern: /(\\byield\\s+)from\\b/i,\n          lookbehind: true\n        }, // `class` is always a keyword unlike other keywords\n        /\\bclass\\b/i,\n        {\n          // https://www.php.net/manual/en/reserved.keywords.php\n          //\n          // keywords cannot be preceded by \"->\"\n          // the complex lookbehind means `(?<!(?:->|::)\\s*)`\n          pattern:\n            /((?:^|[^\\s>:]|(?:^|[^-])>|(?:^|[^:]):)\\s*)\\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|new|or|parent|print|private|protected|public|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\\b/i,\n          lookbehind: true\n        }\n      ],\n      'argument-name': {\n        pattern: /([(,]\\s+)\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n        lookbehind: true\n      },\n      'class-name': [\n        {\n          pattern:\n            /(\\b(?:extends|implements|instanceof|new(?!\\s+self|\\s+static))\\s+|\\bcatch\\s*\\()\\b[a-z_]\\w*(?!\\\\)\\b/i,\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /(\\|\\s*)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /\\b[a-z_]\\w*(?!\\\\)\\b(?=\\s*\\|)/i,\n          greedy: true\n        },\n        {\n          pattern: /(\\|\\s*)(?:\\\\?\\b[a-z_]\\w*)+\\b/i,\n          alias: 'class-name-fully-qualified',\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /(?:\\\\?\\b[a-z_]\\w*)+\\b(?=\\s*\\|)/i,\n          alias: 'class-name-fully-qualified',\n          greedy: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern:\n            /(\\b(?:extends|implements|instanceof|new(?!\\s+self\\b|\\s+static\\b))\\s+|\\bcatch\\s*\\()(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n          alias: 'class-name-fully-qualified',\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /\\b[a-z_]\\w*(?=\\s*\\$)/i,\n          alias: 'type-declaration',\n          greedy: true\n        },\n        {\n          pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n          alias: ['class-name-fully-qualified', 'type-declaration'],\n          greedy: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /\\b[a-z_]\\w*(?=\\s*::)/i,\n          alias: 'static-context',\n          greedy: true\n        },\n        {\n          pattern: /(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*::)/i,\n          alias: ['class-name-fully-qualified', 'static-context'],\n          greedy: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /([(,?]\\s*)[a-z_]\\w*(?=\\s*\\$)/i,\n          alias: 'type-hint',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /([(,?]\\s*)(?:\\\\?\\b[a-z_]\\w*)+(?=\\s*\\$)/i,\n          alias: ['class-name-fully-qualified', 'type-hint'],\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        },\n        {\n          pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n          alias: 'return-type',\n          greedy: true,\n          lookbehind: true\n        },\n        {\n          pattern: /(\\)\\s*:\\s*(?:\\?\\s*)?)(?:\\\\?\\b[a-z_]\\w*)+\\b(?!\\\\)/i,\n          alias: ['class-name-fully-qualified', 'return-type'],\n          greedy: true,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\\\/\n          }\n        }\n      ],\n      constant: constant,\n      function: {\n        pattern: /(^|[^\\\\\\w])\\\\?[a-z_](?:[\\w\\\\]*\\w)?(?=\\s*\\()/i,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\\\/\n        }\n      },\n      property: {\n        pattern: /(->\\s*)\\w+/,\n        lookbehind: true\n      },\n      number: number,\n      operator: operator,\n      punctuation: punctuation\n    }\n    var string_interpolation = {\n      pattern:\n        /\\{\\$(?:\\{(?:\\{[^{}]+\\}|[^{}]+)\\}|[^{}])+\\}|(^|[^\\\\{])\\$+(?:\\w+(?:\\[[^\\r\\n\\[\\]]+\\]|->\\w+)?)/,\n      lookbehind: true,\n      inside: Prism.languages.php\n    }\n    var string = [\n      {\n        pattern: /<<<'([^']+)'[\\r\\n](?:.*[\\r\\n])*?\\1;/,\n        alias: 'nowdoc-string',\n        greedy: true,\n        inside: {\n          delimiter: {\n            pattern: /^<<<'[^']+'|[a-z_]\\w*;$/i,\n            alias: 'symbol',\n            inside: {\n              punctuation: /^<<<'?|[';]$/\n            }\n          }\n        }\n      },\n      {\n        pattern:\n          /<<<(?:\"([^\"]+)\"[\\r\\n](?:.*[\\r\\n])*?\\1;|([a-z_]\\w*)[\\r\\n](?:.*[\\r\\n])*?\\2;)/i,\n        alias: 'heredoc-string',\n        greedy: true,\n        inside: {\n          delimiter: {\n            pattern: /^<<<(?:\"[^\"]+\"|[a-z_]\\w*)|[a-z_]\\w*;$/i,\n            alias: 'symbol',\n            inside: {\n              punctuation: /^<<<\"?|[\";]$/\n            }\n          },\n          interpolation: string_interpolation\n        }\n      },\n      {\n        pattern: /`(?:\\\\[\\s\\S]|[^\\\\`])*`/,\n        alias: 'backtick-quoted-string',\n        greedy: true\n      },\n      {\n        pattern: /'(?:\\\\[\\s\\S]|[^\\\\'])*'/,\n        alias: 'single-quoted-string',\n        greedy: true\n      },\n      {\n        pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n        alias: 'double-quoted-string',\n        greedy: true,\n        inside: {\n          interpolation: string_interpolation\n        }\n      }\n    ]\n    Prism.languages.insertBefore('php', 'variable', {\n      string: string,\n      attribute: {\n        pattern:\n          /#\\[(?:[^\"'\\/#]|\\/(?![*/])|\\/\\/.*$|#(?!\\[).*$|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|'(?:\\\\[\\s\\S]|[^\\\\'])*')+\\](?=\\s*[a-z$#])/im,\n        greedy: true,\n        inside: {\n          'attribute-content': {\n            pattern: /^(#\\[)[\\s\\S]+(?=\\]$)/,\n            lookbehind: true,\n            // inside can appear subset of php\n            inside: {\n              comment: comment,\n              string: string,\n              'attribute-class-name': [\n                {\n                  pattern: /([^:]|^)\\b[a-z_]\\w*(?!\\\\)\\b/i,\n                  alias: 'class-name',\n                  greedy: true,\n                  lookbehind: true\n                },\n                {\n                  pattern: /([^:]|^)(?:\\\\?\\b[a-z_]\\w*)+/i,\n                  alias: ['class-name', 'class-name-fully-qualified'],\n                  greedy: true,\n                  lookbehind: true,\n                  inside: {\n                    punctuation: /\\\\/\n                  }\n                }\n              ],\n              constant: constant,\n              number: number,\n              operator: operator,\n              punctuation: punctuation\n            }\n          },\n          delimiter: {\n            pattern: /^#\\[|\\]$/,\n            alias: 'punctuation'\n          }\n        }\n      }\n    })\n    Prism.hooks.add('before-tokenize', function (env) {\n      if (!/<\\?/.test(env.code)) {\n        return\n      }\n      var phpPattern =\n        /<\\?(?:[^\"'/#]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|(?:\\/\\/|#(?!\\[))(?:[^?\\n\\r]|\\?(?!>))*(?=$|\\?>|[\\r\\n])|#\\[|\\/\\*(?:[^*]|\\*(?!\\/))*(?:\\*\\/|$))*?(?:\\?>|$)/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'php',\n        phpPattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'php')\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,SAAS,yBAAyB;AAQvC,OAAC,SAAUA,QAAO;AACjB,YAAI,UAAU;AACd,YAAI,WAAW;AAAA,UACb;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,YAAY;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,SACF;AACF,YAAI,WACF;AACF,YAAI,cAAc;AAClB,QAAAA,OAAM,UAAU,MAAM;AAAA,UACpB,WAAW;AAAA,YACT,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,UACA,UAAU;AAAA,UACV,SAAS;AAAA,YACP,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,yBAAyB;AAAA,YACvB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,uBAAuB;AAAA,YACrB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,SACE;AAAA,cACF,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,OAAO;AAAA,cACP,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA;AAAA,YACA;AAAA,YACA;AAAA;AAAA;AAAA;AAAA;AAAA,cAKE,SACE;AAAA,cACF,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,iBAAiB;AAAA,YACf,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,cAAc;AAAA,YACZ;AAAA,cACE,SACE;AAAA,cACF,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO,CAAC,8BAA8B,kBAAkB;AAAA,cACxD,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO,CAAC,8BAA8B,gBAAgB;AAAA,cACtD,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO,CAAC,8BAA8B,WAAW;AAAA,cACjD,QAAQ;AAAA,cACR,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO,CAAC,8BAA8B,aAAa;AAAA,cACnD,QAAQ;AAAA,cACR,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,YAAI,uBAAuB;AAAA,UACzB,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQA,OAAM,UAAU;AAAA,QAC1B;AACA,YAAI,SAAS;AAAA,UACX;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,eAAe;AAAA,YACjB;AAAA,UACF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,eAAe;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,aAAa,OAAO,YAAY;AAAA,UAC9C;AAAA,UACA,WAAW;AAAA,YACT,SACE;AAAA,YACF,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,qBAAqB;AAAA,gBACnB,SAAS;AAAA,gBACT,YAAY;AAAA;AAAA,gBAEZ,QAAQ;AAAA,kBACN;AAAA,kBACA;AAAA,kBACA,wBAAwB;AAAA,oBACtB;AAAA,sBACE,SAAS;AAAA,sBACT,OAAO;AAAA,sBACP,QAAQ;AAAA,sBACR,YAAY;AAAA,oBACd;AAAA,oBACA;AAAA,sBACE,SAAS;AAAA,sBACT,OAAO,CAAC,cAAc,4BAA4B;AAAA,sBAClD,QAAQ;AAAA,sBACR,YAAY;AAAA,sBACZ,QAAQ;AAAA,wBACN,aAAa;AAAA,sBACf;AAAA,oBACF;AAAA,kBACF;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,cACA,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,cAAI,CAAC,MAAM,KAAK,IAAI,IAAI,GAAG;AACzB;AAAA,UACF;AACA,cAAI,aACF;AACF,UAAAA,OAAM,UAAU,mBAAmB,EAAE;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,UAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,KAAK;AAAA,QACtE,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}