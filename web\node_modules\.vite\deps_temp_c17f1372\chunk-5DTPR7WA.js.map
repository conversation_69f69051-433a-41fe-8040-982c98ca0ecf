{"version": 3, "sources": ["../../refractor/lang/antlr4.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = antlr4\nantlr4.displayName = 'antlr4'\nantlr4.aliases = ['g4']\nfunction antlr4(Prism) {\n  Prism.languages.antlr4 = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    string: {\n      pattern: /'(?:\\\\.|[^\\\\'\\r\\n])*'/,\n      greedy: true\n    },\n    'character-class': {\n      pattern: /\\[(?:\\\\.|[^\\\\\\]\\r\\n])*\\]/,\n      greedy: true,\n      alias: 'regex',\n      inside: {\n        range: {\n          pattern: /([^[]|(?:^|[^\\\\])(?:\\\\\\\\)*\\\\\\[)-(?!\\])/,\n          lookbehind: true,\n          alias: 'punctuation'\n        },\n        escape:\n          /\\\\(?:u(?:[a-fA-F\\d]{4}|\\{[a-fA-F\\d]+\\})|[pP]\\{[=\\w-]+\\}|[^\\r\\nupP])/,\n        punctuation: /[\\[\\]]/\n      }\n    },\n    action: {\n      pattern: /\\{(?:[^{}]|\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})*\\})*\\}/,\n      greedy: true,\n      inside: {\n        content: {\n          // this might be C, C++, Python, Java, C#, or any other language ANTLR4 compiles to\n          pattern: /(\\{)[\\s\\S]+(?=\\})/,\n          lookbehind: true\n        },\n        punctuation: /[{}]/\n      }\n    },\n    command: {\n      pattern:\n        /(->\\s*(?!\\s))(?:\\s*(?:,\\s*)?\\b[a-z]\\w*(?:\\s*\\([^()\\r\\n]*\\))?)+(?=\\s*;)/i,\n      lookbehind: true,\n      inside: {\n        function: /\\b\\w+(?=\\s*(?:[,(]|$))/,\n        punctuation: /[,()]/\n      }\n    },\n    annotation: {\n      pattern: /@\\w+(?:::\\w+)*/,\n      alias: 'keyword'\n    },\n    label: {\n      pattern: /#[ \\t]*\\w+/,\n      alias: 'punctuation'\n    },\n    keyword:\n      /\\b(?:catch|channels|finally|fragment|grammar|import|lexer|locals|mode|options|parser|returns|throws|tokens)\\b/,\n    definition: [\n      {\n        pattern: /\\b[a-z]\\w*(?=\\s*:)/,\n        alias: ['rule', 'class-name']\n      },\n      {\n        pattern: /\\b[A-Z]\\w*(?=\\s*:)/,\n        alias: ['token', 'constant']\n      }\n    ],\n    constant: /\\b[A-Z][A-Z_]*\\b/,\n    operator: /\\.\\.|->|[|~]|[*+?]\\??/,\n    punctuation: /[;:()=]/\n  }\n  Prism.languages.g4 = Prism.languages.antlr4\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC,IAAI;AACtB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS;AAAA,QACvB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,mBAAmB;AAAA,UACjB,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,OAAO;AAAA,cACL,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,QACE;AAAA,YACF,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,SAAS;AAAA;AAAA,cAEP,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,UAAU;AAAA,YACV,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,YAAY;AAAA,UACV,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,SACE;AAAA,QACF,YAAY;AAAA,UACV;AAAA,YACE,SAAS;AAAA,YACT,OAAO,CAAC,QAAQ,YAAY;AAAA,UAC9B;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO,CAAC,SAAS,UAAU;AAAA,UAC7B;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AACA,YAAM,UAAU,KAAK,MAAM,UAAU;AAAA,IACvC;AAAA;AAAA;", "names": []}