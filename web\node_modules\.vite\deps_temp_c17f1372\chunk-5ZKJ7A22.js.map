{"version": 3, "sources": ["../../refractor/lang/rest.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = rest\nrest.displayName = 'rest'\nrest.aliases = []\nfunction rest(Prism) {\n  Prism.languages.rest = {\n    table: [\n      {\n        pattern:\n          /(^[\\t ]*)(?:\\+[=-]+)+\\+(?:\\r?\\n|\\r)(?:\\1[+|].+[+|](?:\\r?\\n|\\r))+\\1(?:\\+[=-]+)+\\+/m,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\||(?:\\+[=-]+)+\\+/\n        }\n      },\n      {\n        pattern:\n          /(^[\\t ]*)=+ [ =]*=(?:(?:\\r?\\n|\\r)\\1.+)+(?:\\r?\\n|\\r)\\1=+ [ =]*=(?=(?:\\r?\\n|\\r){2}|\\s*$)/m,\n        lookbehind: true,\n        inside: {\n          punctuation: /[=-]+/\n        }\n      }\n    ],\n    // Directive-like patterns\n    'substitution-def': {\n      pattern: /(^[\\t ]*\\.\\. )\\|(?:[^|\\s](?:[^|]*[^|\\s])?)\\| [^:]+::/m,\n      lookbehind: true,\n      inside: {\n        substitution: {\n          pattern: /^\\|(?:[^|\\s]|[^|\\s][^|]*[^|\\s])\\|/,\n          alias: 'attr-value',\n          inside: {\n            punctuation: /^\\||\\|$/\n          }\n        },\n        directive: {\n          pattern: /( )(?! )[^:]+::/,\n          lookbehind: true,\n          alias: 'function',\n          inside: {\n            punctuation: /::$/\n          }\n        }\n      }\n    },\n    'link-target': [\n      {\n        pattern: /(^[\\t ]*\\.\\. )\\[[^\\]]+\\]/m,\n        lookbehind: true,\n        alias: 'string',\n        inside: {\n          punctuation: /^\\[|\\]$/\n        }\n      },\n      {\n        pattern: /(^[\\t ]*\\.\\. )_(?:`[^`]+`|(?:[^:\\\\]|\\\\.)+):/m,\n        lookbehind: true,\n        alias: 'string',\n        inside: {\n          punctuation: /^_|:$/\n        }\n      }\n    ],\n    directive: {\n      pattern: /(^[\\t ]*\\.\\. )[^:]+::/m,\n      lookbehind: true,\n      alias: 'function',\n      inside: {\n        punctuation: /::$/\n      }\n    },\n    comment: {\n      // The two alternatives try to prevent highlighting of blank comments\n      pattern:\n        /(^[\\t ]*\\.\\.)(?:(?: .+)?(?:(?:\\r?\\n|\\r).+)+| .+)(?=(?:\\r?\\n|\\r){2}|$)/m,\n      lookbehind: true\n    },\n    title: [\n      // Overlined and underlined\n      {\n        pattern:\n          /^(([!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~])\\2+)(?:\\r?\\n|\\r).+(?:\\r?\\n|\\r)\\1$/m,\n        inside: {\n          punctuation:\n            /^[!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~]+|[!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~]+$/,\n          important: /.+/\n        }\n      }, // Underlined only\n      {\n        pattern:\n          /(^|(?:\\r?\\n|\\r){2}).+(?:\\r?\\n|\\r)([!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~])\\2+(?=\\r?\\n|\\r|$)/,\n        lookbehind: true,\n        inside: {\n          punctuation: /[!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~]+$/,\n          important: /.+/\n        }\n      }\n    ],\n    hr: {\n      pattern:\n        /((?:\\r?\\n|\\r){2})([!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~])\\2{3,}(?=(?:\\r?\\n|\\r){2})/,\n      lookbehind: true,\n      alias: 'punctuation'\n    },\n    field: {\n      pattern: /(^[\\t ]*):[^:\\r\\n]+:(?= )/m,\n      lookbehind: true,\n      alias: 'attr-name'\n    },\n    'command-line-option': {\n      pattern:\n        /(^[\\t ]*)(?:[+-][a-z\\d]|(?:--|\\/)[a-z\\d-]+)(?:[ =](?:[a-z][\\w-]*|<[^<>]+>))?(?:, (?:[+-][a-z\\d]|(?:--|\\/)[a-z\\d-]+)(?:[ =](?:[a-z][\\w-]*|<[^<>]+>))?)*(?=(?:\\r?\\n|\\r)? {2,}\\S)/im,\n      lookbehind: true,\n      alias: 'symbol'\n    },\n    'literal-block': {\n      pattern: /::(?:\\r?\\n|\\r){2}([ \\t]+)(?![ \\t]).+(?:(?:\\r?\\n|\\r)\\1.+)*/,\n      inside: {\n        'literal-block-punctuation': {\n          pattern: /^::/,\n          alias: 'punctuation'\n        }\n      }\n    },\n    'quoted-literal-block': {\n      pattern:\n        /::(?:\\r?\\n|\\r){2}([!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~]).*(?:(?:\\r?\\n|\\r)\\1.*)*/,\n      inside: {\n        'literal-block-punctuation': {\n          pattern: /^(?:::|([!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~])\\1*)/m,\n          alias: 'punctuation'\n        }\n      }\n    },\n    'list-bullet': {\n      pattern:\n        /(^[\\t ]*)(?:[*+\\-•‣⁃]|\\(?(?:\\d+|[a-z]|[ivxdclm]+)\\)|(?:\\d+|[a-z]|[ivxdclm]+)\\.)(?= )/im,\n      lookbehind: true,\n      alias: 'punctuation'\n    },\n    'doctest-block': {\n      pattern: /(^[\\t ]*)>>> .+(?:(?:\\r?\\n|\\r).+)*/m,\n      lookbehind: true,\n      inside: {\n        punctuation: /^>>>/\n      }\n    },\n    inline: [\n      {\n        pattern:\n          /(^|[\\s\\-:\\/'\"<(\\[{])(?::[^:]+:`.*?`|`.*?`:[^:]+:|(\\*\\*?|``?|\\|)(?!\\s)(?:(?!\\2).)*\\S\\2(?=[\\s\\-.,:;!?\\\\\\/'\")\\]}]|$))/m,\n        lookbehind: true,\n        inside: {\n          bold: {\n            pattern: /(^\\*\\*).+(?=\\*\\*$)/,\n            lookbehind: true\n          },\n          italic: {\n            pattern: /(^\\*).+(?=\\*$)/,\n            lookbehind: true\n          },\n          'inline-literal': {\n            pattern: /(^``).+(?=``$)/,\n            lookbehind: true,\n            alias: 'symbol'\n          },\n          role: {\n            pattern: /^:[^:]+:|:[^:]+:$/,\n            alias: 'function',\n            inside: {\n              punctuation: /^:|:$/\n            }\n          },\n          'interpreted-text': {\n            pattern: /(^`).+(?=`$)/,\n            lookbehind: true,\n            alias: 'attr-value'\n          },\n          substitution: {\n            pattern: /(^\\|).+(?=\\|$)/,\n            lookbehind: true,\n            alias: 'attr-value'\n          },\n          punctuation: /\\*\\*?|``?|\\|/\n        }\n      }\n    ],\n    link: [\n      {\n        pattern: /\\[[^\\[\\]]+\\]_(?=[\\s\\-.,:;!?\\\\\\/'\")\\]}]|$)/,\n        alias: 'string',\n        inside: {\n          punctuation: /^\\[|\\]_$/\n        }\n      },\n      {\n        pattern:\n          /(?:\\b[a-z\\d]+(?:[_.:+][a-z\\d]+)*_?_|`[^`]+`_?_|_`[^`]+`)(?=[\\s\\-.,:;!?\\\\\\/'\")\\]}]|$)/i,\n        alias: 'string',\n        inside: {\n          punctuation: /^_?`|`$|`?_?_$/\n        }\n      }\n    ],\n    // Line block start,\n    // quote attribution,\n    // explicit markup start,\n    // and anonymous hyperlink target shortcut (__)\n    punctuation: {\n      pattern: /(^[\\t ]*)(?:\\|(?= |$)|(?:---?|—|\\.\\.|__)(?= )|\\.\\.$)/m,\n      lookbehind: true\n    }\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,UAAU,OAAO;AAAA,QACrB,OAAO;AAAA,UACL;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA,oBAAoB;AAAA,UAClB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,WAAW;AAAA,cACT,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,eAAe;AAAA,UACb;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,SAAS;AAAA;AAAA,UAEP,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,OAAO;AAAA;AAAA,UAEL;AAAA,YACE,SACE;AAAA,YACF,QAAQ;AAAA,cACN,aACE;AAAA,cACF,WAAW;AAAA,YACb;AAAA,UACF;AAAA;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,cACb,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,uBAAuB;AAAA,UACrB,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,iBAAiB;AAAA,UACf,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,6BAA6B;AAAA,cAC3B,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA,wBAAwB;AAAA,UACtB,SACE;AAAA,UACF,QAAQ;AAAA,YACN,6BAA6B;AAAA,cAC3B,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA,eAAe;AAAA,UACb,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,iBAAiB;AAAA,UACf,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA,kBAAkB;AAAA,gBAChB,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,cACA,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,oBAAoB;AAAA,gBAClB,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,cACA,cAAc;AAAA,gBACZ,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM;AAAA,UACJ;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,aAAa;AAAA,UACX,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}