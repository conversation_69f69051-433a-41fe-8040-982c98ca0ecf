{"version": 3, "sources": ["../../highlight.js/lib/languages/smali.js"], "sourcesContent": ["/*\nLanguage: Smali\nAuthor: <PERSON> <<EMAIL>>\nDescription: Basic Smali highlighting\nWebsite: https://github.com/JesusFreke/smali\n*/\n\nfunction smali(hljs) {\n  const smali_instr_low_prio = [\n    'add',\n    'and',\n    'cmp',\n    'cmpg',\n    'cmpl',\n    'const',\n    'div',\n    'double',\n    'float',\n    'goto',\n    'if',\n    'int',\n    'long',\n    'move',\n    'mul',\n    'neg',\n    'new',\n    'nop',\n    'not',\n    'or',\n    'rem',\n    'return',\n    'shl',\n    'shr',\n    'sput',\n    'sub',\n    'throw',\n    'ushr',\n    'xor'\n  ];\n  const smali_instr_high_prio = [\n    'aget',\n    'aput',\n    'array',\n    'check',\n    'execute',\n    'fill',\n    'filled',\n    'goto/16',\n    'goto/32',\n    'iget',\n    'instance',\n    'invoke',\n    'iput',\n    'monitor',\n    'packed',\n    'sget',\n    'sparse'\n  ];\n  const smali_keywords = [\n    'transient',\n    'constructor',\n    'abstract',\n    'final',\n    'synthetic',\n    'public',\n    'private',\n    'protected',\n    'static',\n    'bridge',\n    'system'\n  ];\n  return {\n    name: 'Smali',\n    contains: [\n      {\n        className: 'string',\n        begin: '\"',\n        end: '\"',\n        relevance: 0\n      },\n      hljs.COMMENT(\n        '#',\n        '$',\n        {\n          relevance: 0\n        }\n      ),\n      {\n        className: 'keyword',\n        variants: [\n          {\n            begin: '\\\\s*\\\\.end\\\\s[a-zA-Z0-9]*'\n          },\n          {\n            begin: '^[ ]*\\\\.[a-zA-Z]*',\n            relevance: 0\n          },\n          {\n            begin: '\\\\s:[a-zA-Z_0-9]*',\n            relevance: 0\n          },\n          {\n            begin: '\\\\s(' + smali_keywords.join('|') + ')'\n          }\n        ]\n      },\n      {\n        className: 'built_in',\n        variants: [\n          {\n            begin: '\\\\s(' + smali_instr_low_prio.join('|') + ')\\\\s'\n          },\n          {\n            begin: '\\\\s(' + smali_instr_low_prio.join('|') + ')((-|/)[a-zA-Z0-9]+)+\\\\s',\n            relevance: 10\n          },\n          {\n            begin: '\\\\s(' + smali_instr_high_prio.join('|') + ')((-|/)[a-zA-Z0-9]+)*\\\\s',\n            relevance: 10\n          }\n        ]\n      },\n      {\n        className: 'class',\n        begin: 'L[^\\(;:\\n]*;',\n        relevance: 0\n      },\n      {\n        begin: '[vp][0-9]+'\n      }\n    ]\n  };\n}\n\nmodule.exports = smali;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,MAAM,MAAM;AACnB,YAAM,uBAAuB;AAAA,QAC3B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,wBAAwB;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,iBAAiB;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,YACb;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,OAAO,SAAS,eAAe,KAAK,GAAG,IAAI;AAAA,cAC7C;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,SAAS,qBAAqB,KAAK,GAAG,IAAI;AAAA,cACnD;AAAA,cACA;AAAA,gBACE,OAAO,SAAS,qBAAqB,KAAK,GAAG,IAAI;AAAA,gBACjD,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,OAAO,SAAS,sBAAsB,KAAK,GAAG,IAAI;AAAA,gBAClD,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}