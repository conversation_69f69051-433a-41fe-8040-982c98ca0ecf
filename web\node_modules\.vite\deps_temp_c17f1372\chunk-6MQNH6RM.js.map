{"version": 3, "sources": ["../../refractor/lang/wren.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = wren\nwren.displayName = 'wren'\nwren.aliases = []\nfunction wren(Prism) {\n  // https://wren.io/\n  Prism.languages.wren = {\n    // Multiline comments in Wren can have nested multiline comments\n    // Comments: // and /* */\n    comment: [\n      {\n        // support 3 levels of nesting\n        // regex: \\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|<self>)*\\*\\/\n        pattern:\n          /\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*))*\\*\\/)*\\*\\/)*\\*\\//,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    // Triple quoted strings are multiline but cannot have interpolation (raw strings)\n    // Based on prism-python.js\n    'triple-quoted-string': {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    // see below\n    'string-literal': null,\n    // #!/usr/bin/env wren on the first line\n    hashbang: {\n      pattern: /^#!\\/.+/,\n      greedy: true,\n      alias: 'comment'\n    },\n    // Attributes are special keywords to add meta data to classes\n    attribute: {\n      // #! attributes are stored in class properties\n      // #!myvar = true\n      // #attributes are not stored and dismissed at compilation\n      pattern: /#!?[ \\t\\u3000]*\\w+/,\n      alias: 'keyword'\n    },\n    'class-name': [\n      {\n        // class definition\n        // class Meta {}\n        pattern: /(\\bclass\\s+)\\w+/,\n        lookbehind: true\n      }, // A class must always start with an uppercase.\n      // File.read\n      /\\b[A-Z][a-z\\d_]*\\b/\n    ],\n    // A constant can be a variable, class, property or method. Just named in all uppercase letters\n    constant: /\\b[A-Z][A-Z\\d_]*\\b/,\n    null: {\n      pattern: /\\bnull\\b/,\n      alias: 'keyword'\n    },\n    keyword:\n      /\\b(?:as|break|class|construct|continue|else|for|foreign|if|import|in|is|return|static|super|this|var|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number: /\\b(?:0x[\\da-f]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/i,\n    // Functions can be Class.method()\n    function: /\\b[a-z_]\\w*(?=\\s*[({])/i,\n    operator: /<<|>>|[=!<>]=?|&&|\\|\\||[-+*/%~^&|?:]|\\.{2,3}/,\n    punctuation: /[\\[\\](){}.,;]/\n  }\n  Prism.languages.wren['string-literal'] = {\n    // A single quote string is multiline and can have interpolation (similar to JS backticks ``)\n    pattern:\n      /(^|[^\\\\\"])\"(?:[^\\\\\"%]|\\\\[\\s\\S]|%(?!\\()|%\\((?:[^()]|\\((?:[^()]|\\([^)]*\\))*\\))*\\))*\"/,\n    lookbehind: true,\n    greedy: true,\n    inside: {\n      interpolation: {\n        // \"%(interpolation)\"\n        pattern:\n          /((?:^|[^\\\\])(?:\\\\{2})*)%\\((?:[^()]|\\((?:[^()]|\\([^)]*\\))*\\))*\\)/,\n        lookbehind: true,\n        inside: {\n          expression: {\n            pattern: /^(%\\()[\\s\\S]+(?=\\)$)/,\n            lookbehind: true,\n            inside: Prism.languages.wren\n          },\n          'interpolation-punctuation': {\n            pattern: /^%\\(|\\)$/,\n            alias: 'punctuation'\n          }\n        }\n      },\n      string: /[\\s\\S]+/\n    }\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AAEnB,YAAM,UAAU,OAAO;AAAA;AAAA;AAAA,QAGrB,SAAS;AAAA,UACP;AAAA;AAAA;AAAA,YAGE,SACE;AAAA,YACF,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF;AAAA;AAAA;AAAA,QAGA,wBAAwB;AAAA,UACtB,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA;AAAA,QAEA,kBAAkB;AAAA;AAAA,QAElB,UAAU;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA;AAAA,QAEA,WAAW;AAAA;AAAA;AAAA;AAAA,UAIT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,cAAc;AAAA,UACZ;AAAA;AAAA;AAAA,YAGE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA;AAAA;AAAA,UAEA;AAAA,QACF;AAAA;AAAA,QAEA,UAAU;AAAA,QACV,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,SACE;AAAA,QACF,SAAS;AAAA,QACT,QAAQ;AAAA;AAAA,QAER,UAAU;AAAA,QACV,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AACA,YAAM,UAAU,KAAK,gBAAgB,IAAI;AAAA;AAAA,QAEvC,SACE;AAAA,QACF,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,eAAe;AAAA;AAAA,YAEb,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ,MAAM,UAAU;AAAA,cAC1B;AAAA,cACA,6BAA6B;AAAA,gBAC3B,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}