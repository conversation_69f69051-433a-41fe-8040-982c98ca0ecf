{"version": 3, "sources": ["../../xtend/immutable.js", "../../hastscript/node_modules/property-information/lib/util/schema.js", "../../hastscript/node_modules/property-information/lib/util/merge.js", "../../hastscript/node_modules/property-information/normalize.js", "../../hastscript/node_modules/property-information/lib/util/info.js", "../../hastscript/node_modules/property-information/lib/util/types.js", "../../hastscript/node_modules/property-information/lib/util/defined-info.js", "../../hastscript/node_modules/property-information/lib/util/create.js", "../../hastscript/node_modules/property-information/lib/xlink.js", "../../hastscript/node_modules/property-information/lib/xml.js", "../../hastscript/node_modules/property-information/lib/util/case-sensitive-transform.js", "../../hastscript/node_modules/property-information/lib/util/case-insensitive-transform.js", "../../hastscript/node_modules/property-information/lib/xmlns.js", "../../hastscript/node_modules/property-information/lib/aria.js", "../../hastscript/node_modules/property-information/lib/html.js", "../../hastscript/node_modules/property-information/html.js", "../../hastscript/node_modules/property-information/find.js", "../../hast-util-parse-selector/index.js", "../../hastscript/node_modules/space-separated-tokens/index.js", "../../hastscript/node_modules/comma-separated-tokens/index.js", "../../hastscript/factory.js", "../../hastscript/html.js", "../../hastscript/index.js", "../../refractor/node_modules/character-entities-legacy/index.json", "../../refractor/node_modules/character-reference-invalid/index.json", "../../refractor/node_modules/is-decimal/index.js", "../../refractor/node_modules/is-hexadecimal/index.js", "../../refractor/node_modules/is-alphabetical/index.js", "../../refractor/node_modules/is-alphanumerical/index.js", "../../refractor/node_modules/parse-entities/decode-entity.browser.js", "../../refractor/node_modules/parse-entities/index.js", "../../refractor/node_modules/prismjs/components/prism-core.js", "../../refractor/core.js"], "sourcesContent": ["module.exports = extend\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nfunction extend() {\n    var target = {}\n\n    for (var i = 0; i < arguments.length; i++) {\n        var source = arguments[i]\n\n        for (var key in source) {\n            if (hasOwnProperty.call(source, key)) {\n                target[key] = source[key]\n            }\n        }\n    }\n\n    return target\n}\n", "'use strict'\n\nmodule.exports = Schema\n\nvar proto = Schema.prototype\n\nproto.space = null\nproto.normal = {}\nproto.property = {}\n\nfunction Schema(property, normal, space) {\n  this.property = property\n  this.normal = normal\n\n  if (space) {\n    this.space = space\n  }\n}\n", "'use strict'\n\nvar xtend = require('xtend')\nvar Schema = require('./schema')\n\nmodule.exports = merge\n\nfunction merge(definitions) {\n  var length = definitions.length\n  var property = []\n  var normal = []\n  var index = -1\n  var info\n  var space\n\n  while (++index < length) {\n    info = definitions[index]\n    property.push(info.property)\n    normal.push(info.normal)\n    space = info.space\n  }\n\n  return new Schema(\n    xtend.apply(null, property),\n    xtend.apply(null, normal),\n    space\n  )\n}\n", "'use strict'\n\nmodule.exports = normalize\n\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n", "'use strict'\n\nmodule.exports = Info\n\nvar proto = Info.prototype\n\nproto.space = null\nproto.attribute = null\nproto.property = null\nproto.boolean = false\nproto.booleanish = false\nproto.overloadedBoolean = false\nproto.number = false\nproto.commaSeparated = false\nproto.spaceSeparated = false\nproto.commaOrSpaceSeparated = false\nproto.mustUseProperty = false\nproto.defined = false\n\nfunction Info(property, attribute) {\n  this.property = property\n  this.attribute = attribute\n}\n", "'use strict'\n\nvar powers = 0\n\nexports.boolean = increment()\nexports.booleanish = increment()\nexports.overloadedBoolean = increment()\nexports.number = increment()\nexports.spaceSeparated = increment()\nexports.commaSeparated = increment()\nexports.commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return Math.pow(2, ++powers)\n}\n", "'use strict'\n\nvar Info = require('./info')\nvar types = require('./types')\n\nmodule.exports = DefinedInfo\n\nDefinedInfo.prototype = new Info()\nDefinedInfo.prototype.defined = true\n\nvar checks = [\n  'boolean',\n  'booleanish',\n  'overloadedBoolean',\n  'number',\n  'commaSeparated',\n  'spaceSeparated',\n  'commaOrSpaceSeparated'\n]\nvar checksLength = checks.length\n\nfunction DefinedInfo(property, attribute, mask, space) {\n  var index = -1\n  var check\n\n  mark(this, 'space', space)\n\n  Info.call(this, property, attribute)\n\n  while (++index < checksLength) {\n    check = checks[index]\n    mark(this, check, (mask & types[check]) === types[check])\n  }\n}\n\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value\n  }\n}\n", "'use strict'\n\nvar normalize = require('../../normalize')\nvar Schema = require('./schema')\nvar DefinedInfo = require('./defined-info')\n\nmodule.exports = create\n\nfunction create(definition) {\n  var space = definition.space\n  var mustUseProperty = definition.mustUseProperty || []\n  var attributes = definition.attributes || {}\n  var props = definition.properties\n  var transform = definition.transform\n  var property = {}\n  var normal = {}\n  var prop\n  var info\n\n  for (prop in props) {\n    info = new DefinedInfo(\n      prop,\n      transform(attributes, prop),\n      props[prop],\n      space\n    )\n\n    if (mustUseProperty.indexOf(prop) !== -1) {\n      info.mustUseProperty = true\n    }\n\n    property[prop] = info\n\n    normal[normalize(prop)] = prop\n    normal[normalize(info.attribute)] = prop\n  }\n\n  return new Schema(property, normal, space)\n}\n", "'use strict'\n\nvar create = require('./util/create')\n\nmodule.exports = create({\n  space: 'xlink',\n  transform: xlinkTransform,\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n})\n\nfunction xlinkTransform(_, prop) {\n  return 'xlink:' + prop.slice(5).toLowerCase()\n}\n", "'use strict'\n\nvar create = require('./util/create')\n\nmodule.exports = create({\n  space: 'xml',\n  transform: xmlTransform,\n  properties: {\n    xmlLang: null,\n    xmlBase: null,\n    xmlSpace: null\n  }\n})\n\nfunction xmlTransform(_, prop) {\n  return 'xml:' + prop.slice(3).toLowerCase()\n}\n", "'use strict'\n\nmodule.exports = caseSensitiveTransform\n\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n", "'use strict'\n\nvar caseSensitiveTransform = require('./case-sensitive-transform')\n\nmodule.exports = caseInsensitiveTransform\n\nfunction caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase())\n}\n", "'use strict'\n\nvar create = require('./util/create')\nvar caseInsensitiveTransform = require('./util/case-insensitive-transform')\n\nmodule.exports = create({\n  space: 'xmlns',\n  attributes: {\n    xmlnsxlink: 'xmlns:xlink'\n  },\n  transform: caseInsensitiveTransform,\n  properties: {\n    xmlns: null,\n    xmlnsXLink: null\n  }\n})\n", "'use strict'\n\nvar types = require('./util/types')\nvar create = require('./util/create')\n\nvar booleanish = types.booleanish\nvar number = types.number\nvar spaceSeparated = types.spaceSeparated\n\nmodule.exports = create({\n  transform: ariaTransform,\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  }\n})\n\nfunction ariaTransform(_, prop) {\n  return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n}\n", "'use strict'\n\nvar types = require('./util/types')\nvar create = require('./util/create')\nvar caseInsensitiveTransform = require('./util/case-insensitive-transform')\n\nvar boolean = types.boolean\nvar overloadedBoolean = types.overloadedBoolean\nvar booleanish = types.booleanish\nvar number = types.number\nvar spaceSeparated = types.spaceSeparated\nvar commaSeparated = types.commaSeparated\n\nmodule.exports = create({\n  space: 'html',\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  transform: caseInsensitiveTransform,\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: commaSeparated,\n    acceptCharset: spaceSeparated,\n    accessKey: spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: boolean,\n    allowPaymentRequest: boolean,\n    allowUserMedia: boolean,\n    alt: null,\n    as: null,\n    async: boolean,\n    autoCapitalize: null,\n    autoComplete: spaceSeparated,\n    autoFocus: boolean,\n    autoPlay: boolean,\n    capture: boolean,\n    charSet: null,\n    checked: boolean,\n    cite: null,\n    className: spaceSeparated,\n    cols: number,\n    colSpan: null,\n    content: null,\n    contentEditable: booleanish,\n    controls: boolean,\n    controlsList: spaceSeparated,\n    coords: number | commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: boolean,\n    defer: boolean,\n    dir: null,\n    dirName: null,\n    disabled: boolean,\n    download: overloadedBoolean,\n    draggable: booleanish,\n    encType: null,\n    enterKeyHint: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: boolean,\n    formTarget: null,\n    headers: spaceSeparated,\n    height: number,\n    hidden: boolean,\n    high: number,\n    href: null,\n    hrefLang: null,\n    htmlFor: spaceSeparated,\n    httpEquiv: spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: commaSeparated,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: boolean,\n    itemId: null,\n    itemProp: spaceSeparated,\n    itemRef: spaceSeparated,\n    itemScope: boolean,\n    itemType: spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: boolean,\n    low: number,\n    manifest: null,\n    max: null,\n    maxLength: number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: number,\n    multiple: boolean,\n    muted: boolean,\n    name: null,\n    nonce: null,\n    noModule: boolean,\n    noValidate: boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforePrint: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextMenu: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: boolean,\n    optimum: number,\n    pattern: null,\n    ping: spaceSeparated,\n    placeholder: null,\n    playsInline: boolean,\n    poster: null,\n    preload: null,\n    readOnly: boolean,\n    referrerPolicy: null,\n    rel: spaceSeparated,\n    required: boolean,\n    reversed: boolean,\n    rows: number,\n    rowSpan: number,\n    sandbox: spaceSeparated,\n    scope: null,\n    scoped: boolean,\n    seamless: boolean,\n    selected: boolean,\n    shape: null,\n    size: number,\n    sizes: null,\n    slot: null,\n    span: number,\n    spellCheck: booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: commaSeparated,\n    start: number,\n    step: null,\n    style: null,\n    tabIndex: number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: boolean,\n    useMap: null,\n    value: booleanish,\n    width: number,\n    wrap: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: boolean, // Lists. Use CSS to reduce space between items instead\n    declare: boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: number, // `<img>` and `<object>`\n    leftMargin: number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: number, // `<body>`\n    marginWidth: number, // `<body>`\n    noResize: boolean, // `<frame>`\n    noHref: boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: boolean,\n    disableRemotePlayback: boolean,\n    prefix: null,\n    property: null,\n    results: number,\n    security: null,\n    unselectable: null\n  }\n})\n", "'use strict'\n\nvar merge = require('./lib/util/merge')\nvar xlink = require('./lib/xlink')\nvar xml = require('./lib/xml')\nvar xmlns = require('./lib/xmlns')\nvar aria = require('./lib/aria')\nvar html = require('./lib/html')\n\nmodule.exports = merge([xml, xlink, xmlns, aria, html])\n", "'use strict'\n\nvar normalize = require('./normalize')\nvar DefinedInfo = require('./lib/util/defined-info')\nvar Info = require('./lib/util/info')\n\nvar data = 'data'\n\nmodule.exports = find\n\nvar valid = /^data[-\\w.:]+$/i\nvar dash = /-[a-z]/g\nvar cap = /[A-Z]/g\n\nfunction find(schema, value) {\n  var normal = normalize(value)\n  var prop = value\n  var Type = Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === data && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      prop = datasetToProperty(value)\n    } else {\n      value = datasetToAttribute(value)\n    }\n\n    Type = DefinedInfo\n  }\n\n  return new Type(prop, value)\n}\n\nfunction datasetToProperty(attribute) {\n  var value = attribute.slice(5).replace(dash, camelcase)\n  return data + value.charAt(0).toUpperCase() + value.slice(1)\n}\n\nfunction datasetToAttribute(property) {\n  var value = property.slice(4)\n\n  if (dash.test(value)) {\n    return property\n  }\n\n  value = value.replace(cap, kebab)\n\n  if (value.charAt(0) !== '-') {\n    value = '-' + value\n  }\n\n  return data + value\n}\n\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n", "'use strict'\n\nmodule.exports = parse\n\nvar search = /[#.]/g\n\n// Create a hast element from a simple CSS selector.\nfunction parse(selector, defaultTagName) {\n  var value = selector || ''\n  var name = defaultTagName || 'div'\n  var props = {}\n  var start = 0\n  var subvalue\n  var previous\n  var match\n\n  while (start < value.length) {\n    search.lastIndex = start\n    match = search.exec(value)\n    subvalue = value.slice(start, match ? match.index : value.length)\n\n    if (subvalue) {\n      if (!previous) {\n        name = subvalue\n      } else if (previous === '#') {\n        props.id = subvalue\n      } else if (props.className) {\n        props.className.push(subvalue)\n      } else {\n        props.className = [subvalue]\n      }\n\n      start += subvalue.length\n    }\n\n    if (match) {\n      previous = match[0]\n      start++\n    }\n  }\n\n  return {type: 'element', tagName: name, properties: props, children: []}\n}\n", "'use strict'\n\nexports.parse = parse\nexports.stringify = stringify\n\nvar empty = ''\nvar space = ' '\nvar whiteSpace = /[ \\t\\n\\r\\f]+/g\n\nfunction parse(value) {\n  var input = String(value || empty).trim()\n  return input === empty ? [] : input.split(whiteSpace)\n}\n\nfunction stringify(values) {\n  return values.join(space).trim()\n}\n", "'use strict'\n\nexports.parse = parse\nexports.stringify = stringify\n\nvar comma = ','\nvar space = ' '\nvar empty = ''\n\n// Parse comma-separated tokens to an array.\nfunction parse(value) {\n  var values = []\n  var input = String(value || empty)\n  var index = input.indexOf(comma)\n  var lastIndex = 0\n  var end = false\n  var val\n\n  while (!end) {\n    if (index === -1) {\n      index = input.length\n      end = true\n    }\n\n    val = input.slice(lastIndex, index).trim()\n\n    if (val || !end) {\n      values.push(val)\n    }\n\n    lastIndex = index + 1\n    index = input.indexOf(comma, lastIndex)\n  }\n\n  return values\n}\n\n// Compile an array to comma-separated tokens.\n// `options.padLeft` (default: `true`) pads a space left of each token, and\n// `options.padRight` (default: `false`) pads a space to the right of each token.\nfunction stringify(values, options) {\n  var settings = options || {}\n  var left = settings.padLeft === false ? empty : space\n  var right = settings.padRight ? space : empty\n\n  // Ensure the last empty entry is seen.\n  if (values[values.length - 1] === empty) {\n    values = values.concat(empty)\n  }\n\n  return values.join(right + comma + left).trim()\n}\n", "'use strict'\n\nvar find = require('property-information/find')\nvar normalize = require('property-information/normalize')\nvar parseSelector = require('hast-util-parse-selector')\nvar spaces = require('space-separated-tokens').parse\nvar commas = require('comma-separated-tokens').parse\n\nmodule.exports = factory\n\nvar own = {}.hasOwnProperty\n\nfunction factory(schema, defaultTagName, caseSensitive) {\n  var adjust = caseSensitive ? createAdjustMap(caseSensitive) : null\n\n  return h\n\n  // Hyperscript compatible DSL for creating virtual hast trees.\n  function h(selector, properties) {\n    var node = parseSelector(selector, defaultTagName)\n    var children = Array.prototype.slice.call(arguments, 2)\n    var name = node.tagName.toLowerCase()\n    var property\n\n    node.tagName = adjust && own.call(adjust, name) ? adjust[name] : name\n\n    if (properties && isChildren(properties, node)) {\n      children.unshift(properties)\n      properties = null\n    }\n\n    if (properties) {\n      for (property in properties) {\n        addProperty(node.properties, property, properties[property])\n      }\n    }\n\n    addChild(node.children, children)\n\n    if (node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  function addProperty(properties, key, value) {\n    var info\n    var property\n    var result\n\n    // Ignore nullish and NaN values.\n    if (value === null || value === undefined || value !== value) {\n      return\n    }\n\n    info = find(schema, key)\n    property = info.property\n    result = value\n\n    // Handle list values.\n    if (typeof result === 'string') {\n      if (info.spaceSeparated) {\n        result = spaces(result)\n      } else if (info.commaSeparated) {\n        result = commas(result)\n      } else if (info.commaOrSpaceSeparated) {\n        result = spaces(commas(result).join(' '))\n      }\n    }\n\n    // Accept `object` on style.\n    if (property === 'style' && typeof value !== 'string') {\n      result = style(result)\n    }\n\n    // Class-names (which can be added both on the `selector` and here).\n    if (property === 'className' && properties.className) {\n      result = properties.className.concat(result)\n    }\n\n    properties[property] = parsePrimitives(info, property, result)\n  }\n}\n\nfunction isChildren(value, node) {\n  return (\n    typeof value === 'string' ||\n    'length' in value ||\n    isNode(node.tagName, value)\n  )\n}\n\nfunction isNode(tagName, value) {\n  var type = value.type\n\n  if (tagName === 'input' || !type || typeof type !== 'string') {\n    return false\n  }\n\n  if (typeof value.children === 'object' && 'length' in value.children) {\n    return true\n  }\n\n  type = type.toLowerCase()\n\n  if (tagName === 'button') {\n    return (\n      type !== 'menu' &&\n      type !== 'submit' &&\n      type !== 'reset' &&\n      type !== 'button'\n    )\n  }\n\n  return 'value' in value\n}\n\nfunction addChild(nodes, value) {\n  var index\n  var length\n\n  if (typeof value === 'string' || typeof value === 'number') {\n    nodes.push({type: 'text', value: String(value)})\n    return\n  }\n\n  if (typeof value === 'object' && 'length' in value) {\n    index = -1\n    length = value.length\n\n    while (++index < length) {\n      addChild(nodes, value[index])\n    }\n\n    return\n  }\n\n  if (typeof value !== 'object' || !('type' in value)) {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n\n  nodes.push(value)\n}\n\n// Parse a (list of) primitives.\nfunction parsePrimitives(info, name, value) {\n  var index\n  var length\n  var result\n\n  if (typeof value !== 'object' || !('length' in value)) {\n    return parsePrimitive(info, name, value)\n  }\n\n  length = value.length\n  index = -1\n  result = []\n\n  while (++index < length) {\n    result[index] = parsePrimitive(info, name, value[index])\n  }\n\n  return result\n}\n\n// Parse a single primitives.\nfunction parsePrimitive(info, name, value) {\n  var result = value\n\n  if (info.number || info.positiveNumber) {\n    if (!isNaN(result) && result !== '') {\n      result = Number(result)\n    }\n  } else if (info.boolean || info.overloadedBoolean) {\n    // Accept `boolean` and `string`.\n    if (\n      typeof result === 'string' &&\n      (result === '' || normalize(value) === normalize(name))\n    ) {\n      result = true\n    }\n  }\n\n  return result\n}\n\nfunction style(value) {\n  var result = []\n  var key\n\n  for (key in value) {\n    result.push([key, value[key]].join(': '))\n  }\n\n  return result.join('; ')\n}\n\nfunction createAdjustMap(values) {\n  var length = values.length\n  var index = -1\n  var result = {}\n  var value\n\n  while (++index < length) {\n    value = values[index]\n    result[value.toLowerCase()] = value\n  }\n\n  return result\n}\n", "'use strict'\n\nvar schema = require('property-information/html')\nvar factory = require('./factory')\n\nvar html = factory(schema, 'div')\nhtml.displayName = 'html'\n\nmodule.exports = html\n", "'use strict'\n\nmodule.exports = require('./html')\n", "{\n  \"AElig\": \"Æ\",\n  \"AMP\": \"&\",\n  \"Aacute\": \"Á\",\n  \"Acirc\": \"Â\",\n  \"Agrave\": \"À\",\n  \"Aring\": \"Å\",\n  \"Atilde\": \"Ã\",\n  \"Auml\": \"Ä\",\n  \"COPY\": \"©\",\n  \"Ccedil\": \"Ç\",\n  \"ETH\": \"Ð\",\n  \"Eacute\": \"É\",\n  \"Ecirc\": \"Ê\",\n  \"Egrave\": \"È\",\n  \"Euml\": \"Ë\",\n  \"GT\": \">\",\n  \"Iacute\": \"Í\",\n  \"Icirc\": \"Î\",\n  \"Igrave\": \"Ì\",\n  \"Iuml\": \"Ï\",\n  \"LT\": \"<\",\n  \"Ntilde\": \"Ñ\",\n  \"Oacute\": \"Ó\",\n  \"Ocirc\": \"Ô\",\n  \"Ograve\": \"Ò\",\n  \"Oslash\": \"Ø\",\n  \"Otilde\": \"Õ\",\n  \"Ouml\": \"Ö\",\n  \"QUOT\": \"\\\"\",\n  \"REG\": \"®\",\n  \"THORN\": \"Þ\",\n  \"Uacute\": \"Ú\",\n  \"Ucirc\": \"Û\",\n  \"Ugrave\": \"Ù\",\n  \"Uuml\": \"Ü\",\n  \"Yacute\": \"Ý\",\n  \"aacute\": \"á\",\n  \"acirc\": \"â\",\n  \"acute\": \"´\",\n  \"aelig\": \"æ\",\n  \"agrave\": \"à\",\n  \"amp\": \"&\",\n  \"aring\": \"å\",\n  \"atilde\": \"ã\",\n  \"auml\": \"ä\",\n  \"brvbar\": \"¦\",\n  \"ccedil\": \"ç\",\n  \"cedil\": \"¸\",\n  \"cent\": \"¢\",\n  \"copy\": \"©\",\n  \"curren\": \"¤\",\n  \"deg\": \"°\",\n  \"divide\": \"÷\",\n  \"eacute\": \"é\",\n  \"ecirc\": \"ê\",\n  \"egrave\": \"è\",\n  \"eth\": \"ð\",\n  \"euml\": \"ë\",\n  \"frac12\": \"½\",\n  \"frac14\": \"¼\",\n  \"frac34\": \"¾\",\n  \"gt\": \">\",\n  \"iacute\": \"í\",\n  \"icirc\": \"î\",\n  \"iexcl\": \"¡\",\n  \"igrave\": \"ì\",\n  \"iquest\": \"¿\",\n  \"iuml\": \"ï\",\n  \"laquo\": \"«\",\n  \"lt\": \"<\",\n  \"macr\": \"¯\",\n  \"micro\": \"µ\",\n  \"middot\": \"·\",\n  \"nbsp\": \" \",\n  \"not\": \"¬\",\n  \"ntilde\": \"ñ\",\n  \"oacute\": \"ó\",\n  \"ocirc\": \"ô\",\n  \"ograve\": \"ò\",\n  \"ordf\": \"ª\",\n  \"ordm\": \"º\",\n  \"oslash\": \"ø\",\n  \"otilde\": \"õ\",\n  \"ouml\": \"ö\",\n  \"para\": \"¶\",\n  \"plusmn\": \"±\",\n  \"pound\": \"£\",\n  \"quot\": \"\\\"\",\n  \"raquo\": \"»\",\n  \"reg\": \"®\",\n  \"sect\": \"§\",\n  \"shy\": \"­\",\n  \"sup1\": \"¹\",\n  \"sup2\": \"²\",\n  \"sup3\": \"³\",\n  \"szlig\": \"ß\",\n  \"thorn\": \"þ\",\n  \"times\": \"×\",\n  \"uacute\": \"ú\",\n  \"ucirc\": \"û\",\n  \"ugrave\": \"ù\",\n  \"uml\": \"¨\",\n  \"uuml\": \"ü\",\n  \"yacute\": \"ý\",\n  \"yen\": \"¥\",\n  \"yuml\": \"ÿ\"\n}\n", "{\n  \"0\": \"�\",\n  \"128\": \"€\",\n  \"130\": \"‚\",\n  \"131\": \"ƒ\",\n  \"132\": \"„\",\n  \"133\": \"…\",\n  \"134\": \"†\",\n  \"135\": \"‡\",\n  \"136\": \"ˆ\",\n  \"137\": \"‰\",\n  \"138\": \"Š\",\n  \"139\": \"‹\",\n  \"140\": \"Œ\",\n  \"142\": \"Ž\",\n  \"145\": \"‘\",\n  \"146\": \"’\",\n  \"147\": \"“\",\n  \"148\": \"”\",\n  \"149\": \"•\",\n  \"150\": \"–\",\n  \"151\": \"—\",\n  \"152\": \"˜\",\n  \"153\": \"™\",\n  \"154\": \"š\",\n  \"155\": \"›\",\n  \"156\": \"œ\",\n  \"158\": \"ž\",\n  \"159\": \"Ÿ\"\n}\n", "'use strict'\n\nmodule.exports = decimal\n\n// Check if the given character code, or the character code at the first\n// character, is decimal.\nfunction decimal(character) {\n  var code = typeof character === 'string' ? character.charCodeAt(0) : character\n\n  return code >= 48 && code <= 57 /* 0-9 */\n}\n", "'use strict'\n\nmodule.exports = hexadecimal\n\n// Check if the given character code, or the character code at the first\n// character, is hexadecimal.\nfunction hexadecimal(character) {\n  var code = typeof character === 'string' ? character.charCodeAt(0) : character\n\n  return (\n    (code >= 97 /* a */ && code <= 102) /* z */ ||\n    (code >= 65 /* A */ && code <= 70) /* Z */ ||\n    (code >= 48 /* A */ && code <= 57) /* Z */\n  )\n}\n", "'use strict'\n\nmodule.exports = alphabetical\n\n// Check if the given character code, or the character code at the first\n// character, is alphabetical.\nfunction alphabetical(character) {\n  var code = typeof character === 'string' ? character.charCodeAt(0) : character\n\n  return (\n    (code >= 97 && code <= 122) /* a-z */ ||\n    (code >= 65 && code <= 90) /* A-Z */\n  )\n}\n", "'use strict'\n\nvar alphabetical = require('is-alphabetical')\nvar decimal = require('is-decimal')\n\nmodule.exports = alphanumerical\n\n// Check if the given character code, or the character code at the first\n// character, is alphanumerical.\nfunction alphanumerical(character) {\n  return alphabetical(character) || decimal(character)\n}\n", "'use strict'\n\n/* eslint-env browser */\n\nvar el\n\nvar semicolon = 59 //  ';'\n\nmodule.exports = decodeEntity\n\nfunction decodeEntity(characters) {\n  var entity = '&' + characters + ';'\n  var char\n\n  el = el || document.createElement('i')\n  el.innerHTML = entity\n  char = el.textContent\n\n  // Some entities do not require the closing semicolon (`&not` - for instance),\n  // which leads to situations where parsing the assumed entity of &notit; will\n  // result in the string `¬it;`.  When we encounter a trailing semicolon after\n  // parsing and the entity to decode was not a semicolon (`&semi;`), we can\n  // assume that the matching was incomplete\n  if (char.charCodeAt(char.length - 1) === semicolon && characters !== 'semi') {\n    return false\n  }\n\n  // If the decoded string is equal to the input, the entity was not valid\n  return char === entity ? false : char\n}\n", "'use strict'\n\nvar legacy = require('character-entities-legacy')\nvar invalid = require('character-reference-invalid')\nvar decimal = require('is-decimal')\nvar hexadecimal = require('is-hexadecimal')\nvar alphanumerical = require('is-alphanumerical')\nvar decodeEntity = require('./decode-entity')\n\nmodule.exports = parseEntities\n\nvar own = {}.hasOwnProperty\nvar fromCharCode = String.fromCharCode\nvar noop = Function.prototype\n\n// Default settings.\nvar defaults = {\n  warning: null,\n  reference: null,\n  text: null,\n  warningContext: null,\n  referenceContext: null,\n  textContext: null,\n  position: {},\n  additional: null,\n  attribute: false,\n  nonTerminated: true\n}\n\n// Characters.\nvar tab = 9 // '\\t'\nvar lineFeed = 10 // '\\n'\nvar formFeed = 12 // '\\f'\nvar space = 32 // ' '\nvar ampersand = 38 // '&'\nvar semicolon = 59 // ';'\nvar lessThan = 60 // '<'\nvar equalsTo = 61 // '='\nvar numberSign = 35 // '#'\nvar uppercaseX = 88 // 'X'\nvar lowercaseX = 120 // 'x'\nvar replacementCharacter = 65533 // '�'\n\n// Reference types.\nvar name = 'named'\nvar hexa = 'hexadecimal'\nvar deci = 'decimal'\n\n// Map of bases.\nvar bases = {}\n\nbases[hexa] = 16\nbases[deci] = 10\n\n// Map of types to tests.\n// Each type of character reference accepts different characters.\n// This test is used to detect whether a reference has ended (as the semicolon\n// is not strictly needed).\nvar tests = {}\n\ntests[name] = alphanumerical\ntests[deci] = decimal\ntests[hexa] = hexadecimal\n\n// Warning types.\nvar namedNotTerminated = 1\nvar numericNotTerminated = 2\nvar namedEmpty = 3\nvar numericEmpty = 4\nvar namedUnknown = 5\nvar numericDisallowed = 6\nvar numericProhibited = 7\n\n// Warning messages.\nvar messages = {}\n\nmessages[namedNotTerminated] =\n  'Named character references must be terminated by a semicolon'\nmessages[numericNotTerminated] =\n  'Numeric character references must be terminated by a semicolon'\nmessages[namedEmpty] = 'Named character references cannot be empty'\nmessages[numericEmpty] = 'Numeric character references cannot be empty'\nmessages[namedUnknown] = 'Named character references must be known'\nmessages[numericDisallowed] =\n  'Numeric character references cannot be disallowed'\nmessages[numericProhibited] =\n  'Numeric character references cannot be outside the permissible Unicode range'\n\n// Wrap to ensure clean parameters are given to `parse`.\nfunction parseEntities(value, options) {\n  var settings = {}\n  var option\n  var key\n\n  if (!options) {\n    options = {}\n  }\n\n  for (key in defaults) {\n    option = options[key]\n    settings[key] =\n      option === null || option === undefined ? defaults[key] : option\n  }\n\n  if (settings.position.indent || settings.position.start) {\n    settings.indent = settings.position.indent || []\n    settings.position = settings.position.start\n  }\n\n  return parse(value, settings)\n}\n\n// Parse entities.\n// eslint-disable-next-line complexity\nfunction parse(value, settings) {\n  var additional = settings.additional\n  var nonTerminated = settings.nonTerminated\n  var handleText = settings.text\n  var handleReference = settings.reference\n  var handleWarning = settings.warning\n  var textContext = settings.textContext\n  var referenceContext = settings.referenceContext\n  var warningContext = settings.warningContext\n  var pos = settings.position\n  var indent = settings.indent || []\n  var length = value.length\n  var index = 0\n  var lines = -1\n  var column = pos.column || 1\n  var line = pos.line || 1\n  var queue = ''\n  var result = []\n  var entityCharacters\n  var namedEntity\n  var terminated\n  var characters\n  var character\n  var reference\n  var following\n  var warning\n  var reason\n  var output\n  var entity\n  var begin\n  var start\n  var type\n  var test\n  var prev\n  var next\n  var diff\n  var end\n\n  if (typeof additional === 'string') {\n    additional = additional.charCodeAt(0)\n  }\n\n  // Cache the current point.\n  prev = now()\n\n  // Wrap `handleWarning`.\n  warning = handleWarning ? parseError : noop\n\n  // Ensure the algorithm walks over the first character and the end\n  // (inclusive).\n  index--\n  length++\n\n  while (++index < length) {\n    // If the previous character was a newline.\n    if (character === lineFeed) {\n      column = indent[lines] || 1\n    }\n\n    character = value.charCodeAt(index)\n\n    if (character === ampersand) {\n      following = value.charCodeAt(index + 1)\n\n      // The behaviour depends on the identity of the next character.\n      if (\n        following === tab ||\n        following === lineFeed ||\n        following === formFeed ||\n        following === space ||\n        following === ampersand ||\n        following === lessThan ||\n        following !== following ||\n        (additional && following === additional)\n      ) {\n        // Not a character reference.\n        // No characters are consumed, and nothing is returned.\n        // This is not an error, either.\n        queue += fromCharCode(character)\n        column++\n\n        continue\n      }\n\n      start = index + 1\n      begin = start\n      end = start\n\n      if (following === numberSign) {\n        // Numerical entity.\n        end = ++begin\n\n        // The behaviour further depends on the next character.\n        following = value.charCodeAt(end)\n\n        if (following === uppercaseX || following === lowercaseX) {\n          // ASCII hex digits.\n          type = hexa\n          end = ++begin\n        } else {\n          // ASCII digits.\n          type = deci\n        }\n      } else {\n        // Named entity.\n        type = name\n      }\n\n      entityCharacters = ''\n      entity = ''\n      characters = ''\n      test = tests[type]\n      end--\n\n      while (++end < length) {\n        following = value.charCodeAt(end)\n\n        if (!test(following)) {\n          break\n        }\n\n        characters += fromCharCode(following)\n\n        // Check if we can match a legacy named reference.\n        // If so, we cache that as the last viable named reference.\n        // This ensures we do not need to walk backwards later.\n        if (type === name && own.call(legacy, characters)) {\n          entityCharacters = characters\n          entity = legacy[characters]\n        }\n      }\n\n      terminated = value.charCodeAt(end) === semicolon\n\n      if (terminated) {\n        end++\n\n        namedEntity = type === name ? decodeEntity(characters) : false\n\n        if (namedEntity) {\n          entityCharacters = characters\n          entity = namedEntity\n        }\n      }\n\n      diff = 1 + end - start\n\n      if (!terminated && !nonTerminated) {\n        // Empty.\n      } else if (!characters) {\n        // An empty (possible) entity is valid, unless it’s numeric (thus an\n        // ampersand followed by an octothorp).\n        if (type !== name) {\n          warning(numericEmpty, diff)\n        }\n      } else if (type === name) {\n        // An ampersand followed by anything unknown, and not terminated, is\n        // invalid.\n        if (terminated && !entity) {\n          warning(namedUnknown, 1)\n        } else {\n          // If theres something after an entity name which is not known, cap\n          // the reference.\n          if (entityCharacters !== characters) {\n            end = begin + entityCharacters.length\n            diff = 1 + end - begin\n            terminated = false\n          }\n\n          // If the reference is not terminated, warn.\n          if (!terminated) {\n            reason = entityCharacters ? namedNotTerminated : namedEmpty\n\n            if (settings.attribute) {\n              following = value.charCodeAt(end)\n\n              if (following === equalsTo) {\n                warning(reason, diff)\n                entity = null\n              } else if (alphanumerical(following)) {\n                entity = null\n              } else {\n                warning(reason, diff)\n              }\n            } else {\n              warning(reason, diff)\n            }\n          }\n        }\n\n        reference = entity\n      } else {\n        if (!terminated) {\n          // All non-terminated numeric entities are not rendered, and trigger a\n          // warning.\n          warning(numericNotTerminated, diff)\n        }\n\n        // When terminated and number, parse as either hexadecimal or decimal.\n        reference = parseInt(characters, bases[type])\n\n        // Trigger a warning when the parsed number is prohibited, and replace\n        // with replacement character.\n        if (prohibited(reference)) {\n          warning(numericProhibited, diff)\n          reference = fromCharCode(replacementCharacter)\n        } else if (reference in invalid) {\n          // Trigger a warning when the parsed number is disallowed, and replace\n          // by an alternative.\n          warning(numericDisallowed, diff)\n          reference = invalid[reference]\n        } else {\n          // Parse the number.\n          output = ''\n\n          // Trigger a warning when the parsed number should not be used.\n          if (disallowed(reference)) {\n            warning(numericDisallowed, diff)\n          }\n\n          // Stringify the number.\n          if (reference > 0xffff) {\n            reference -= 0x10000\n            output += fromCharCode((reference >>> (10 & 0x3ff)) | 0xd800)\n            reference = 0xdc00 | (reference & 0x3ff)\n          }\n\n          reference = output + fromCharCode(reference)\n        }\n      }\n\n      // Found it!\n      // First eat the queued characters as normal text, then eat an entity.\n      if (reference) {\n        flush()\n\n        prev = now()\n        index = end - 1\n        column += end - start + 1\n        result.push(reference)\n        next = now()\n        next.offset++\n\n        if (handleReference) {\n          handleReference.call(\n            referenceContext,\n            reference,\n            {start: prev, end: next},\n            value.slice(start - 1, end)\n          )\n        }\n\n        prev = next\n      } else {\n        // If we could not find a reference, queue the checked characters (as\n        // normal characters), and move the pointer to their end.\n        // This is possible because we can be certain neither newlines nor\n        // ampersands are included.\n        characters = value.slice(start - 1, end)\n        queue += characters\n        column += characters.length\n        index = end - 1\n      }\n    } else {\n      // Handle anything other than an ampersand, including newlines and EOF.\n      if (\n        character === 10 // Line feed\n      ) {\n        line++\n        lines++\n        column = 0\n      }\n\n      if (character === character) {\n        queue += fromCharCode(character)\n        column++\n      } else {\n        flush()\n      }\n    }\n  }\n\n  // Return the reduced nodes.\n  return result.join('')\n\n  // Get current position.\n  function now() {\n    return {\n      line: line,\n      column: column,\n      offset: index + (pos.offset || 0)\n    }\n  }\n\n  // “Throw” a parse-error: a warning.\n  function parseError(code, offset) {\n    var position = now()\n\n    position.column += offset\n    position.offset += offset\n\n    handleWarning.call(warningContext, messages[code], position, code)\n  }\n\n  // Flush `queue` (normal text).\n  // Macro invoked before each entity and at the end of `value`.\n  // Does nothing when `queue` is empty.\n  function flush() {\n    if (queue) {\n      result.push(queue)\n\n      if (handleText) {\n        handleText.call(textContext, queue, {start: prev, end: now()})\n      }\n\n      queue = ''\n    }\n  }\n}\n\n// Check if `character` is outside the permissible unicode range.\nfunction prohibited(code) {\n  return (code >= 0xd800 && code <= 0xdfff) || code > 0x10ffff\n}\n\n// Check if `character` is disallowed.\nfunction disallowed(code) {\n  return (\n    (code >= 0x0001 && code <= 0x0008) ||\n    code === 0x000b ||\n    (code >= 0x000d && code <= 0x001f) ||\n    (code >= 0x007f && code <= 0x009f) ||\n    (code >= 0xfdd0 && code <= 0xfdef) ||\n    (code & 0xffff) === 0xffff ||\n    (code & 0xffff) === 0xfffe\n  )\n}\n", "/// <reference lib=\"WebWorker\"/>\n\nvar _self = (typeof window !== 'undefined')\n\t? window   // if in browser\n\t: (\n\t\t(typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope)\n\t\t\t? self // if in worker\n\t\t\t: {}   // if in node js\n\t);\n\n/**\n * Prism: Lightweight, robust, elegant syntax highlighting\n *\n * @license MIT <https://opensource.org/licenses/MIT>\n * <AUTHOR> Verou <https://lea.verou.me>\n * @namespace\n * @public\n */\nvar Prism = (function (_self) {\n\n\t// Private helper vars\n\tvar lang = /(?:^|\\s)lang(?:uage)?-([\\w-]+)(?=\\s|$)/i;\n\tvar uniqueId = 0;\n\n\t// The grammar object for plaintext\n\tvar plainTextGrammar = {};\n\n\n\tvar _ = {\n\t\t/**\n\t\t * By default, Prism will attempt to highlight all code elements (by calling {@link Prism.highlightAll}) on the\n\t\t * current page after the page finished loading. This might be a problem if e.g. you wanted to asynchronously load\n\t\t * additional languages or plugins yourself.\n\t\t *\n\t\t * By setting this value to `true`, Prism will not automatically highlight all code elements on the page.\n\t\t *\n\t\t * You obviously have to change this value before the automatic highlighting started. To do this, you can add an\n\t\t * empty Prism object into the global scope before loading the Prism script like this:\n\t\t *\n\t\t * ```js\n\t\t * window.Prism = window.Prism || {};\n\t\t * Prism.manual = true;\n\t\t * // add a new <script> to load Prism's script\n\t\t * ```\n\t\t *\n\t\t * @default false\n\t\t * @type {boolean}\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tmanual: _self.Prism && _self.Prism.manual,\n\t\t/**\n\t\t * By default, if Prism is in a web worker, it assumes that it is in a worker it created itself, so it uses\n\t\t * `addEventListener` to communicate with its parent instance. However, if you're using Prism manually in your\n\t\t * own worker, you don't want it to do this.\n\t\t *\n\t\t * By setting this value to `true`, Prism will not add its own listeners to the worker.\n\t\t *\n\t\t * You obviously have to change this value before Prism executes. To do this, you can add an\n\t\t * empty Prism object into the global scope before loading the Prism script like this:\n\t\t *\n\t\t * ```js\n\t\t * window.Prism = window.Prism || {};\n\t\t * Prism.disableWorkerMessageHandler = true;\n\t\t * // Load Prism's script\n\t\t * ```\n\t\t *\n\t\t * @default false\n\t\t * @type {boolean}\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tdisableWorkerMessageHandler: _self.Prism && _self.Prism.disableWorkerMessageHandler,\n\n\t\t/**\n\t\t * A namespace for utility methods.\n\t\t *\n\t\t * All function in this namespace that are not explicitly marked as _public_ are for __internal use only__ and may\n\t\t * change or disappear at any time.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t */\n\t\tutil: {\n\t\t\tencode: function encode(tokens) {\n\t\t\t\tif (tokens instanceof Token) {\n\t\t\t\t\treturn new Token(tokens.type, encode(tokens.content), tokens.alias);\n\t\t\t\t} else if (Array.isArray(tokens)) {\n\t\t\t\t\treturn tokens.map(encode);\n\t\t\t\t} else {\n\t\t\t\t\treturn tokens.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/\\u00a0/g, ' ');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the name of the type of the given value.\n\t\t\t *\n\t\t\t * @param {any} o\n\t\t\t * @returns {string}\n\t\t\t * @example\n\t\t\t * type(null)      === 'Null'\n\t\t\t * type(undefined) === 'Undefined'\n\t\t\t * type(123)       === 'Number'\n\t\t\t * type('foo')     === 'String'\n\t\t\t * type(true)      === 'Boolean'\n\t\t\t * type([1, 2])    === 'Array'\n\t\t\t * type({})        === 'Object'\n\t\t\t * type(String)    === 'Function'\n\t\t\t * type(/abc+/)    === 'RegExp'\n\t\t\t */\n\t\t\ttype: function (o) {\n\t\t\t\treturn Object.prototype.toString.call(o).slice(8, -1);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns a unique number for the given object. Later calls will still return the same number.\n\t\t\t *\n\t\t\t * @param {Object} obj\n\t\t\t * @returns {number}\n\t\t\t */\n\t\t\tobjId: function (obj) {\n\t\t\t\tif (!obj['__id']) {\n\t\t\t\t\tObject.defineProperty(obj, '__id', { value: ++uniqueId });\n\t\t\t\t}\n\t\t\t\treturn obj['__id'];\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Creates a deep clone of the given object.\n\t\t\t *\n\t\t\t * The main intended use of this function is to clone language definitions.\n\t\t\t *\n\t\t\t * @param {T} o\n\t\t\t * @param {Record<number, any>} [visited]\n\t\t\t * @returns {T}\n\t\t\t * @template T\n\t\t\t */\n\t\t\tclone: function deepClone(o, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar clone; var id;\n\t\t\t\tswitch (_.util.type(o)) {\n\t\t\t\t\tcase 'Object':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = /** @type {Record<string, any>} */ ({});\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\tfor (var key in o) {\n\t\t\t\t\t\t\tif (o.hasOwnProperty(key)) {\n\t\t\t\t\t\t\t\tclone[key] = deepClone(o[key], visited);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tcase 'Array':\n\t\t\t\t\t\tid = _.util.objId(o);\n\t\t\t\t\t\tif (visited[id]) {\n\t\t\t\t\t\t\treturn visited[id];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tclone = [];\n\t\t\t\t\t\tvisited[id] = clone;\n\n\t\t\t\t\t\t(/** @type {Array} */(/** @type {any} */(o))).forEach(function (v, i) {\n\t\t\t\t\t\t\tclone[i] = deepClone(v, visited);\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\treturn /** @type {any} */ (clone);\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn o;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the Prism language of the given element set by a `language-xxxx` or `lang-xxxx` class.\n\t\t\t *\n\t\t\t * If no language is set for the element or the element is `null` or `undefined`, `none` will be returned.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @returns {string}\n\t\t\t */\n\t\t\tgetLanguage: function (element) {\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar m = lang.exec(element.className);\n\t\t\t\t\tif (m) {\n\t\t\t\t\t\treturn m[1].toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn 'none';\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Sets the Prism `language-xxxx` class of the given element.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} language\n\t\t\t * @returns {void}\n\t\t\t */\n\t\t\tsetLanguage: function (element, language) {\n\t\t\t\t// remove all `language-xxxx` classes\n\t\t\t\t// (this might leave behind a leading space)\n\t\t\t\telement.className = element.className.replace(RegExp(lang, 'gi'), '');\n\n\t\t\t\t// add the new `language-xxxx` class\n\t\t\t\t// (using `classList` will automatically clean up spaces for us)\n\t\t\t\telement.classList.add('language-' + language);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns the script element that is currently executing.\n\t\t\t *\n\t\t\t * This does __not__ work for line script element.\n\t\t\t *\n\t\t\t * @returns {HTMLScriptElement | null}\n\t\t\t */\n\t\t\tcurrentScript: function () {\n\t\t\t\tif (typeof document === 'undefined') {\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t\tif ('currentScript' in document && 1 < 2 /* hack to trip TS' flow analysis */) {\n\t\t\t\t\treturn /** @type {any} */ (document.currentScript);\n\t\t\t\t}\n\n\t\t\t\t// IE11 workaround\n\t\t\t\t// we'll get the src of the current script by parsing IE11's error stack trace\n\t\t\t\t// this will not work for inline scripts\n\n\t\t\t\ttry {\n\t\t\t\t\tthrow new Error();\n\t\t\t\t} catch (err) {\n\t\t\t\t\t// Get file src url from stack. Specifically works with the format of stack traces in IE.\n\t\t\t\t\t// A stack will look like this:\n\t\t\t\t\t//\n\t\t\t\t\t// Error\n\t\t\t\t\t//    at _.util.currentScript (http://localhost/components/prism-core.js:119:5)\n\t\t\t\t\t//    at Global code (http://localhost/components/prism-core.js:606:1)\n\n\t\t\t\t\tvar src = (/at [^(\\r\\n]*\\((.*):[^:]+:[^:]+\\)$/i.exec(err.stack) || [])[1];\n\t\t\t\t\tif (src) {\n\t\t\t\t\t\tvar scripts = document.getElementsByTagName('script');\n\t\t\t\t\t\tfor (var i in scripts) {\n\t\t\t\t\t\t\tif (scripts[i].src == src) {\n\t\t\t\t\t\t\t\treturn scripts[i];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Returns whether a given class is active for `element`.\n\t\t\t *\n\t\t\t * The class can be activated if `element` or one of its ancestors has the given class and it can be deactivated\n\t\t\t * if `element` or one of its ancestors has the negated version of the given class. The _negated version_ of the\n\t\t\t * given class is just the given class with a `no-` prefix.\n\t\t\t *\n\t\t\t * Whether the class is active is determined by the closest ancestor of `element` (where `element` itself is\n\t\t\t * closest ancestor) that has the given class or the negated version of it. If neither `element` nor any of its\n\t\t\t * ancestors have the given class or the negated version of it, then the default activation will be returned.\n\t\t\t *\n\t\t\t * In the paradoxical situation where the closest ancestor contains __both__ the given class and the negated\n\t\t\t * version of it, the class is considered active.\n\t\t\t *\n\t\t\t * @param {Element} element\n\t\t\t * @param {string} className\n\t\t\t * @param {boolean} [defaultActivation=false]\n\t\t\t * @returns {boolean}\n\t\t\t */\n\t\t\tisActive: function (element, className, defaultActivation) {\n\t\t\t\tvar no = 'no-' + className;\n\n\t\t\t\twhile (element) {\n\t\t\t\t\tvar classList = element.classList;\n\t\t\t\t\tif (classList.contains(className)) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tif (classList.contains(no)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\telement = element.parentElement;\n\t\t\t\t}\n\t\t\t\treturn !!defaultActivation;\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * This namespace contains all currently loaded languages and the some helper functions to create and modify languages.\n\t\t *\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\tlanguages: {\n\t\t\t/**\n\t\t\t * The grammar for plain, unformatted text.\n\t\t\t */\n\t\t\tplain: plainTextGrammar,\n\t\t\tplaintext: plainTextGrammar,\n\t\t\ttext: plainTextGrammar,\n\t\t\ttxt: plainTextGrammar,\n\n\t\t\t/**\n\t\t\t * Creates a deep copy of the language with the given id and appends the given tokens.\n\t\t\t *\n\t\t\t * If a token in `redef` also appears in the copied language, then the existing token in the copied language\n\t\t\t * will be overwritten at its original position.\n\t\t\t *\n\t\t\t * ## Best practices\n\t\t\t *\n\t\t\t * Since the position of overwriting tokens (token in `redef` that overwrite tokens in the copied language)\n\t\t\t * doesn't matter, they can technically be in any order. However, this can be confusing to others that trying to\n\t\t\t * understand the language definition because, normally, the order of tokens matters in Prism grammars.\n\t\t\t *\n\t\t\t * Therefore, it is encouraged to order overwriting tokens according to the positions of the overwritten tokens.\n\t\t\t * Furthermore, all non-overwriting tokens should be placed after the overwriting ones.\n\t\t\t *\n\t\t\t * @param {string} id The id of the language to extend. This has to be a key in `Prism.languages`.\n\t\t\t * @param {Grammar} redef The new tokens to append.\n\t\t\t * @returns {Grammar} The new language created.\n\t\t\t * @public\n\t\t\t * @example\n\t\t\t * Prism.languages['css-with-colors'] = Prism.languages.extend('css', {\n\t\t\t *     // Prism.languages.css already has a 'comment' token, so this token will overwrite CSS' 'comment' token\n\t\t\t *     // at its original position\n\t\t\t *     'comment': { ... },\n\t\t\t *     // CSS doesn't have a 'color' token, so this token will be appended\n\t\t\t *     'color': /\\b(?:red|green|blue)\\b/\n\t\t\t * });\n\t\t\t */\n\t\t\textend: function (id, redef) {\n\t\t\t\tvar lang = _.util.clone(_.languages[id]);\n\n\t\t\t\tfor (var key in redef) {\n\t\t\t\t\tlang[key] = redef[key];\n\t\t\t\t}\n\n\t\t\t\treturn lang;\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Inserts tokens _before_ another token in a language definition or any other grammar.\n\t\t\t *\n\t\t\t * ## Usage\n\t\t\t *\n\t\t\t * This helper method makes it easy to modify existing languages. For example, the CSS language definition\n\t\t\t * not only defines CSS highlighting for CSS documents, but also needs to define highlighting for CSS embedded\n\t\t\t * in HTML through `<style>` elements. To do this, it needs to modify `Prism.languages.markup` and add the\n\t\t\t * appropriate tokens. However, `Prism.languages.markup` is a regular JavaScript object literal, so if you do\n\t\t\t * this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.markup.style = {\n\t\t\t *     // token\n\t\t\t * };\n\t\t\t * ```\n\t\t\t *\n\t\t\t * then the `style` token will be added (and processed) at the end. `insertBefore` allows you to insert tokens\n\t\t\t * before existing tokens. For the CSS example above, you would use it like this:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'cdata', {\n\t\t\t *     'style': {\n\t\t\t *         // token\n\t\t\t *     }\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Special cases\n\t\t\t *\n\t\t\t * If the grammars of `inside` and `insert` have tokens with the same name, the tokens in `inside`'s grammar\n\t\t\t * will be ignored.\n\t\t\t *\n\t\t\t * This behavior can be used to insert tokens after `before`:\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * Prism.languages.insertBefore('markup', 'comment', {\n\t\t\t *     'comment': Prism.languages.markup.comment,\n\t\t\t *     // tokens after 'comment'\n\t\t\t * });\n\t\t\t * ```\n\t\t\t *\n\t\t\t * ## Limitations\n\t\t\t *\n\t\t\t * The main problem `insertBefore` has to solve is iteration order. Since ES2015, the iteration order for object\n\t\t\t * properties is guaranteed to be the insertion order (except for integer keys) but some browsers behave\n\t\t\t * differently when keys are deleted and re-inserted. So `insertBefore` can't be implemented by temporarily\n\t\t\t * deleting properties which is necessary to insert at arbitrary positions.\n\t\t\t *\n\t\t\t * To solve this problem, `insertBefore` doesn't actually insert the given tokens into the target object.\n\t\t\t * Instead, it will create a new object and replace all references to the target object with the new one. This\n\t\t\t * can be done without temporarily deleting properties, so the iteration order is well-defined.\n\t\t\t *\n\t\t\t * However, only references that can be reached from `Prism.languages` or `insert` will be replaced. I.e. if\n\t\t\t * you hold the target object in a variable, then the value of the variable will not change.\n\t\t\t *\n\t\t\t * ```js\n\t\t\t * var oldMarkup = Prism.languages.markup;\n\t\t\t * var newMarkup = Prism.languages.insertBefore('markup', 'comment', { ... });\n\t\t\t *\n\t\t\t * assert(oldMarkup !== Prism.languages.markup);\n\t\t\t * assert(newMarkup === Prism.languages.markup);\n\t\t\t * ```\n\t\t\t *\n\t\t\t * @param {string} inside The property of `root` (e.g. a language id in `Prism.languages`) that contains the\n\t\t\t * object to be modified.\n\t\t\t * @param {string} before The key to insert before.\n\t\t\t * @param {Grammar} insert An object containing the key-value pairs to be inserted.\n\t\t\t * @param {Object<string, any>} [root] The object containing `inside`, i.e. the object that contains the\n\t\t\t * object to be modified.\n\t\t\t *\n\t\t\t * Defaults to `Prism.languages`.\n\t\t\t * @returns {Grammar} The new grammar object.\n\t\t\t * @public\n\t\t\t */\n\t\t\tinsertBefore: function (inside, before, insert, root) {\n\t\t\t\troot = root || /** @type {any} */ (_.languages);\n\t\t\t\tvar grammar = root[inside];\n\t\t\t\t/** @type {Grammar} */\n\t\t\t\tvar ret = {};\n\n\t\t\t\tfor (var token in grammar) {\n\t\t\t\t\tif (grammar.hasOwnProperty(token)) {\n\n\t\t\t\t\t\tif (token == before) {\n\t\t\t\t\t\t\tfor (var newToken in insert) {\n\t\t\t\t\t\t\t\tif (insert.hasOwnProperty(newToken)) {\n\t\t\t\t\t\t\t\t\tret[newToken] = insert[newToken];\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Do not insert token which also occur in insert. See #1525\n\t\t\t\t\t\tif (!insert.hasOwnProperty(token)) {\n\t\t\t\t\t\t\tret[token] = grammar[token];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar old = root[inside];\n\t\t\t\troot[inside] = ret;\n\n\t\t\t\t// Update references in other language definitions\n\t\t\t\t_.languages.DFS(_.languages, function (key, value) {\n\t\t\t\t\tif (value === old && key != inside) {\n\t\t\t\t\t\tthis[key] = ret;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn ret;\n\t\t\t},\n\n\t\t\t// Traverse a language definition with Depth First Search\n\t\t\tDFS: function DFS(o, callback, type, visited) {\n\t\t\t\tvisited = visited || {};\n\n\t\t\t\tvar objId = _.util.objId;\n\n\t\t\t\tfor (var i in o) {\n\t\t\t\t\tif (o.hasOwnProperty(i)) {\n\t\t\t\t\t\tcallback.call(o, i, o[i], type || i);\n\n\t\t\t\t\t\tvar property = o[i];\n\t\t\t\t\t\tvar propertyType = _.util.type(property);\n\n\t\t\t\t\t\tif (propertyType === 'Object' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, null, visited);\n\t\t\t\t\t\t} else if (propertyType === 'Array' && !visited[objId(property)]) {\n\t\t\t\t\t\t\tvisited[objId(property)] = true;\n\t\t\t\t\t\t\tDFS(property, callback, i, visited);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tplugins: {},\n\n\t\t/**\n\t\t * This is the most high-level function in Prism’s API.\n\t\t * It fetches all the elements that have a `.language-xxxx` class and then calls {@link Prism.highlightElement} on\n\t\t * each one of them.\n\t\t *\n\t\t * This is equivalent to `Prism.highlightAllUnder(document, async, callback)`.\n\t\t *\n\t\t * @param {boolean} [async=false] Same as in {@link Prism.highlightAllUnder}.\n\t\t * @param {HighlightCallback} [callback] Same as in {@link Prism.highlightAllUnder}.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightAll: function (async, callback) {\n\t\t\t_.highlightAllUnder(document, async, callback);\n\t\t},\n\n\t\t/**\n\t\t * Fetches all the descendants of `container` that have a `.language-xxxx` class and then calls\n\t\t * {@link Prism.highlightElement} on each one of them.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-highlightall`\n\t\t * 2. `before-all-elements-highlight`\n\t\t * 3. All hooks of {@link Prism.highlightElement} for each element.\n\t\t *\n\t\t * @param {ParentNode} container The root element, whose descendants that have a `.language-xxxx` class will be highlighted.\n\t\t * @param {boolean} [async=false] Whether each element is to be highlighted asynchronously using Web Workers.\n\t\t * @param {HighlightCallback} [callback] An optional callback to be invoked on each element after its highlighting is done.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightAllUnder: function (container, async, callback) {\n\t\t\tvar env = {\n\t\t\t\tcallback: callback,\n\t\t\t\tcontainer: container,\n\t\t\t\tselector: 'code[class*=\"language-\"], [class*=\"language-\"] code, code[class*=\"lang-\"], [class*=\"lang-\"] code'\n\t\t\t};\n\n\t\t\t_.hooks.run('before-highlightall', env);\n\n\t\t\tenv.elements = Array.prototype.slice.apply(env.container.querySelectorAll(env.selector));\n\n\t\t\t_.hooks.run('before-all-elements-highlight', env);\n\n\t\t\tfor (var i = 0, element; (element = env.elements[i++]);) {\n\t\t\t\t_.highlightElement(element, async === true, env.callback);\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Highlights the code inside a single element.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-sanity-check`\n\t\t * 2. `before-highlight`\n\t\t * 3. All hooks of {@link Prism.highlight}. These hooks will be run by an asynchronous worker if `async` is `true`.\n\t\t * 4. `before-insert`\n\t\t * 5. `after-highlight`\n\t\t * 6. `complete`\n\t\t *\n\t\t * Some the above hooks will be skipped if the element doesn't contain any text or there is no grammar loaded for\n\t\t * the element's language.\n\t\t *\n\t\t * @param {Element} element The element containing the code.\n\t\t * It must have a class of `language-xxxx` to be processed, where `xxxx` is a valid language identifier.\n\t\t * @param {boolean} [async=false] Whether the element is to be highlighted asynchronously using Web Workers\n\t\t * to improve performance and avoid blocking the UI when highlighting very large chunks of code. This option is\n\t\t * [disabled by default](https://prismjs.com/faq.html#why-is-asynchronous-highlighting-disabled-by-default).\n\t\t *\n\t\t * Note: All language definitions required to highlight the code must be included in the main `prism.js` file for\n\t\t * asynchronous highlighting to work. You can build your own bundle on the\n\t\t * [Download page](https://prismjs.com/download.html).\n\t\t * @param {HighlightCallback} [callback] An optional callback to be invoked after the highlighting is done.\n\t\t * Mostly useful when `async` is `true`, since in that case, the highlighting is done asynchronously.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thighlightElement: function (element, async, callback) {\n\t\t\t// Find language\n\t\t\tvar language = _.util.getLanguage(element);\n\t\t\tvar grammar = _.languages[language];\n\n\t\t\t// Set language on the element, if not present\n\t\t\t_.util.setLanguage(element, language);\n\n\t\t\t// Set language on the parent, for styling\n\t\t\tvar parent = element.parentElement;\n\t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre') {\n\t\t\t\t_.util.setLanguage(parent, language);\n\t\t\t}\n\n\t\t\tvar code = element.textContent;\n\n\t\t\tvar env = {\n\t\t\t\telement: element,\n\t\t\t\tlanguage: language,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tcode: code\n\t\t\t};\n\n\t\t\tfunction insertHighlightedCode(highlightedCode) {\n\t\t\t\tenv.highlightedCode = highlightedCode;\n\n\t\t\t\t_.hooks.run('before-insert', env);\n\n\t\t\t\tenv.element.innerHTML = env.highlightedCode;\n\n\t\t\t\t_.hooks.run('after-highlight', env);\n\t\t\t\t_.hooks.run('complete', env);\n\t\t\t\tcallback && callback.call(env.element);\n\t\t\t}\n\n\t\t\t_.hooks.run('before-sanity-check', env);\n\n\t\t\t// plugins may change/add the parent/element\n\t\t\tparent = env.element.parentElement;\n\t\t\tif (parent && parent.nodeName.toLowerCase() === 'pre' && !parent.hasAttribute('tabindex')) {\n\t\t\t\tparent.setAttribute('tabindex', '0');\n\t\t\t}\n\n\t\t\tif (!env.code) {\n\t\t\t\t_.hooks.run('complete', env);\n\t\t\t\tcallback && callback.call(env.element);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t_.hooks.run('before-highlight', env);\n\n\t\t\tif (!env.grammar) {\n\t\t\t\tinsertHighlightedCode(_.util.encode(env.code));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (async && _self.Worker) {\n\t\t\t\tvar worker = new Worker(_.filename);\n\n\t\t\t\tworker.onmessage = function (evt) {\n\t\t\t\t\tinsertHighlightedCode(evt.data);\n\t\t\t\t};\n\n\t\t\t\tworker.postMessage(JSON.stringify({\n\t\t\t\t\tlanguage: env.language,\n\t\t\t\t\tcode: env.code,\n\t\t\t\t\timmediateClose: true\n\t\t\t\t}));\n\t\t\t} else {\n\t\t\t\tinsertHighlightedCode(_.highlight(env.code, env.grammar, env.language));\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * Low-level function, only use if you know what you’re doing. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns a string with the HTML produced.\n\t\t *\n\t\t * The following hooks will be run:\n\t\t * 1. `before-tokenize`\n\t\t * 2. `after-tokenize`\n\t\t * 3. `wrap`: On each {@link Token}.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @param {string} language The name of the language definition passed to `grammar`.\n\t\t * @returns {string} The highlighted HTML.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * Prism.highlight('var foo = true;', Prism.languages.javascript, 'javascript');\n\t\t */\n\t\thighlight: function (text, grammar, language) {\n\t\t\tvar env = {\n\t\t\t\tcode: text,\n\t\t\t\tgrammar: grammar,\n\t\t\t\tlanguage: language\n\t\t\t};\n\t\t\t_.hooks.run('before-tokenize', env);\n\t\t\tif (!env.grammar) {\n\t\t\t\tthrow new Error('The language \"' + env.language + '\" has no grammar.');\n\t\t\t}\n\t\t\tenv.tokens = _.tokenize(env.code, env.grammar);\n\t\t\t_.hooks.run('after-tokenize', env);\n\t\t\treturn Token.stringify(_.util.encode(env.tokens), env.language);\n\t\t},\n\n\t\t/**\n\t\t * This is the heart of Prism, and the most low-level function you can use. It accepts a string of text as input\n\t\t * and the language definitions to use, and returns an array with the tokenized code.\n\t\t *\n\t\t * When the language definition includes nested tokens, the function is called recursively on each of these tokens.\n\t\t *\n\t\t * This method could be useful in other contexts as well, as a very crude parser.\n\t\t *\n\t\t * @param {string} text A string with the code to be highlighted.\n\t\t * @param {Grammar} grammar An object containing the tokens to use.\n\t\t *\n\t\t * Usually a language definition like `Prism.languages.markup`.\n\t\t * @returns {TokenStream} An array of strings and tokens, a token stream.\n\t\t * @memberof Prism\n\t\t * @public\n\t\t * @example\n\t\t * let code = `var foo = 0;`;\n\t\t * let tokens = Prism.tokenize(code, Prism.languages.javascript);\n\t\t * tokens.forEach(token => {\n\t\t *     if (token instanceof Prism.Token && token.type === 'number') {\n\t\t *         console.log(`Found numeric literal: ${token.content}`);\n\t\t *     }\n\t\t * });\n\t\t */\n\t\ttokenize: function (text, grammar) {\n\t\t\tvar rest = grammar.rest;\n\t\t\tif (rest) {\n\t\t\t\tfor (var token in rest) {\n\t\t\t\t\tgrammar[token] = rest[token];\n\t\t\t\t}\n\n\t\t\t\tdelete grammar.rest;\n\t\t\t}\n\n\t\t\tvar tokenList = new LinkedList();\n\t\t\taddAfter(tokenList, tokenList.head, text);\n\n\t\t\tmatchGrammar(text, tokenList, grammar, tokenList.head, 0);\n\n\t\t\treturn toArray(tokenList);\n\t\t},\n\n\t\t/**\n\t\t * @namespace\n\t\t * @memberof Prism\n\t\t * @public\n\t\t */\n\t\thooks: {\n\t\t\tall: {},\n\n\t\t\t/**\n\t\t\t * Adds the given callback to the list of callbacks for the given hook.\n\t\t\t *\n\t\t\t * The callback will be invoked when the hook it is registered for is run.\n\t\t\t * Hooks are usually directly run by a highlight function but you can also run hooks yourself.\n\t\t\t *\n\t\t\t * One callback function can be registered to multiple hooks and the same hook multiple times.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {HookCallback} callback The callback function which is given environment variables.\n\t\t\t * @public\n\t\t\t */\n\t\t\tadd: function (name, callback) {\n\t\t\t\tvar hooks = _.hooks.all;\n\n\t\t\t\thooks[name] = hooks[name] || [];\n\n\t\t\t\thooks[name].push(callback);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * Runs a hook invoking all registered callbacks with the given environment variables.\n\t\t\t *\n\t\t\t * Callbacks will be invoked synchronously and in the order in which they were registered.\n\t\t\t *\n\t\t\t * @param {string} name The name of the hook.\n\t\t\t * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n\t\t\t * @public\n\t\t\t */\n\t\t\trun: function (name, env) {\n\t\t\t\tvar callbacks = _.hooks.all[name];\n\n\t\t\t\tif (!callbacks || !callbacks.length) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tfor (var i = 0, callback; (callback = callbacks[i++]);) {\n\t\t\t\t\tcallback(env);\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tToken: Token\n\t};\n\t_self.Prism = _;\n\n\n\t// Typescript note:\n\t// The following can be used to import the Token type in JSDoc:\n\t//\n\t//   @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token\n\n\t/**\n\t * Creates a new token.\n\t *\n\t * @param {string} type See {@link Token#type type}\n\t * @param {string | TokenStream} content See {@link Token#content content}\n\t * @param {string|string[]} [alias] The alias(es) of the token.\n\t * @param {string} [matchedStr=\"\"] A copy of the full string this token was created from.\n\t * @class\n\t * @global\n\t * @public\n\t */\n\tfunction Token(type, content, alias, matchedStr) {\n\t\t/**\n\t\t * The type of the token.\n\t\t *\n\t\t * This is usually the key of a pattern in a {@link Grammar}.\n\t\t *\n\t\t * @type {string}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.type = type;\n\t\t/**\n\t\t * The strings or tokens contained by this token.\n\t\t *\n\t\t * This will be a token stream if the pattern matched also defined an `inside` grammar.\n\t\t *\n\t\t * @type {string | TokenStream}\n\t\t * @public\n\t\t */\n\t\tthis.content = content;\n\t\t/**\n\t\t * The alias(es) of the token.\n\t\t *\n\t\t * @type {string|string[]}\n\t\t * @see GrammarToken\n\t\t * @public\n\t\t */\n\t\tthis.alias = alias;\n\t\t// Copy of the full string this token was created from\n\t\tthis.length = (matchedStr || '').length | 0;\n\t}\n\n\t/**\n\t * A token stream is an array of strings and {@link Token Token} objects.\n\t *\n\t * Token streams have to fulfill a few properties that are assumed by most functions (mostly internal ones) that process\n\t * them.\n\t *\n\t * 1. No adjacent strings.\n\t * 2. No empty strings.\n\t *\n\t *    The only exception here is the token stream that only contains the empty string and nothing else.\n\t *\n\t * @typedef {Array<string | Token>} TokenStream\n\t * @global\n\t * @public\n\t */\n\n\t/**\n\t * Converts the given token or token stream to an HTML representation.\n\t *\n\t * The following hooks will be run:\n\t * 1. `wrap`: On each {@link Token}.\n\t *\n\t * @param {string | Token | TokenStream} o The token or token stream to be converted.\n\t * @param {string} language The name of current language.\n\t * @returns {string} The HTML representation of the token or token stream.\n\t * @memberof Token\n\t * @static\n\t */\n\tToken.stringify = function stringify(o, language) {\n\t\tif (typeof o == 'string') {\n\t\t\treturn o;\n\t\t}\n\t\tif (Array.isArray(o)) {\n\t\t\tvar s = '';\n\t\t\to.forEach(function (e) {\n\t\t\t\ts += stringify(e, language);\n\t\t\t});\n\t\t\treturn s;\n\t\t}\n\n\t\tvar env = {\n\t\t\ttype: o.type,\n\t\t\tcontent: stringify(o.content, language),\n\t\t\ttag: 'span',\n\t\t\tclasses: ['token', o.type],\n\t\t\tattributes: {},\n\t\t\tlanguage: language\n\t\t};\n\n\t\tvar aliases = o.alias;\n\t\tif (aliases) {\n\t\t\tif (Array.isArray(aliases)) {\n\t\t\t\tArray.prototype.push.apply(env.classes, aliases);\n\t\t\t} else {\n\t\t\t\tenv.classes.push(aliases);\n\t\t\t}\n\t\t}\n\n\t\t_.hooks.run('wrap', env);\n\n\t\tvar attributes = '';\n\t\tfor (var name in env.attributes) {\n\t\t\tattributes += ' ' + name + '=\"' + (env.attributes[name] || '').replace(/\"/g, '&quot;') + '\"';\n\t\t}\n\n\t\treturn '<' + env.tag + ' class=\"' + env.classes.join(' ') + '\"' + attributes + '>' + env.content + '</' + env.tag + '>';\n\t};\n\n\t/**\n\t * @param {RegExp} pattern\n\t * @param {number} pos\n\t * @param {string} text\n\t * @param {boolean} lookbehind\n\t * @returns {RegExpExecArray | null}\n\t */\n\tfunction matchPattern(pattern, pos, text, lookbehind) {\n\t\tpattern.lastIndex = pos;\n\t\tvar match = pattern.exec(text);\n\t\tif (match && lookbehind && match[1]) {\n\t\t\t// change the match to remove the text matched by the Prism lookbehind group\n\t\t\tvar lookbehindLength = match[1].length;\n\t\t\tmatch.index += lookbehindLength;\n\t\t\tmatch[0] = match[0].slice(lookbehindLength);\n\t\t}\n\t\treturn match;\n\t}\n\n\t/**\n\t * @param {string} text\n\t * @param {LinkedList<string | Token>} tokenList\n\t * @param {any} grammar\n\t * @param {LinkedListNode<string | Token>} startNode\n\t * @param {number} startPos\n\t * @param {RematchOptions} [rematch]\n\t * @returns {void}\n\t * @private\n\t *\n\t * @typedef RematchOptions\n\t * @property {string} cause\n\t * @property {number} reach\n\t */\n\tfunction matchGrammar(text, tokenList, grammar, startNode, startPos, rematch) {\n\t\tfor (var token in grammar) {\n\t\t\tif (!grammar.hasOwnProperty(token) || !grammar[token]) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tvar patterns = grammar[token];\n\t\t\tpatterns = Array.isArray(patterns) ? patterns : [patterns];\n\n\t\t\tfor (var j = 0; j < patterns.length; ++j) {\n\t\t\t\tif (rematch && rematch.cause == token + ',' + j) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar patternObj = patterns[j];\n\t\t\t\tvar inside = patternObj.inside;\n\t\t\t\tvar lookbehind = !!patternObj.lookbehind;\n\t\t\t\tvar greedy = !!patternObj.greedy;\n\t\t\t\tvar alias = patternObj.alias;\n\n\t\t\t\tif (greedy && !patternObj.pattern.global) {\n\t\t\t\t\t// Without the global flag, lastIndex won't work\n\t\t\t\t\tvar flags = patternObj.pattern.toString().match(/[imsuy]*$/)[0];\n\t\t\t\t\tpatternObj.pattern = RegExp(patternObj.pattern.source, flags + 'g');\n\t\t\t\t}\n\n\t\t\t\t/** @type {RegExp} */\n\t\t\t\tvar pattern = patternObj.pattern || patternObj;\n\n\t\t\t\tfor ( // iterate the token list and keep track of the current token/string position\n\t\t\t\t\tvar currentNode = startNode.next, pos = startPos;\n\t\t\t\t\tcurrentNode !== tokenList.tail;\n\t\t\t\t\tpos += currentNode.value.length, currentNode = currentNode.next\n\t\t\t\t) {\n\n\t\t\t\t\tif (rematch && pos >= rematch.reach) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar str = currentNode.value;\n\n\t\t\t\t\tif (tokenList.length > text.length) {\n\t\t\t\t\t\t// Something went terribly wrong, ABORT, ABORT!\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (str instanceof Token) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeCount = 1; // this is the to parameter of removeBetween\n\t\t\t\t\tvar match;\n\n\t\t\t\t\tif (greedy) {\n\t\t\t\t\t\tmatch = matchPattern(pattern, pos, text, lookbehind);\n\t\t\t\t\t\tif (!match || match.index >= text.length) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar from = match.index;\n\t\t\t\t\t\tvar to = match.index + match[0].length;\n\t\t\t\t\t\tvar p = pos;\n\n\t\t\t\t\t\t// find the node that contains the match\n\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\twhile (from >= p) {\n\t\t\t\t\t\t\tcurrentNode = currentNode.next;\n\t\t\t\t\t\t\tp += currentNode.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// adjust pos (and p)\n\t\t\t\t\t\tp -= currentNode.value.length;\n\t\t\t\t\t\tpos = p;\n\n\t\t\t\t\t\t// the current node is a Token, then the match starts inside another Token, which is invalid\n\t\t\t\t\t\tif (currentNode.value instanceof Token) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// find the last node which is affected by this match\n\t\t\t\t\t\tfor (\n\t\t\t\t\t\t\tvar k = currentNode;\n\t\t\t\t\t\t\tk !== tokenList.tail && (p < to || typeof k.value === 'string');\n\t\t\t\t\t\t\tk = k.next\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tremoveCount++;\n\t\t\t\t\t\t\tp += k.value.length;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tremoveCount--;\n\n\t\t\t\t\t\t// replace with the new match\n\t\t\t\t\t\tstr = text.slice(pos, p);\n\t\t\t\t\t\tmatch.index -= pos;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmatch = matchPattern(pattern, 0, str, lookbehind);\n\t\t\t\t\t\tif (!match) {\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// eslint-disable-next-line no-redeclare\n\t\t\t\t\tvar from = match.index;\n\t\t\t\t\tvar matchStr = match[0];\n\t\t\t\t\tvar before = str.slice(0, from);\n\t\t\t\t\tvar after = str.slice(from + matchStr.length);\n\n\t\t\t\t\tvar reach = pos + str.length;\n\t\t\t\t\tif (rematch && reach > rematch.reach) {\n\t\t\t\t\t\trematch.reach = reach;\n\t\t\t\t\t}\n\n\t\t\t\t\tvar removeFrom = currentNode.prev;\n\n\t\t\t\t\tif (before) {\n\t\t\t\t\t\tremoveFrom = addAfter(tokenList, removeFrom, before);\n\t\t\t\t\t\tpos += before.length;\n\t\t\t\t\t}\n\n\t\t\t\t\tremoveRange(tokenList, removeFrom, removeCount);\n\n\t\t\t\t\tvar wrapped = new Token(token, inside ? _.tokenize(matchStr, inside) : matchStr, alias, matchStr);\n\t\t\t\t\tcurrentNode = addAfter(tokenList, removeFrom, wrapped);\n\n\t\t\t\t\tif (after) {\n\t\t\t\t\t\taddAfter(tokenList, currentNode, after);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (removeCount > 1) {\n\t\t\t\t\t\t// at least one Token object was removed, so we have to do some rematching\n\t\t\t\t\t\t// this can only happen if the current pattern is greedy\n\n\t\t\t\t\t\t/** @type {RematchOptions} */\n\t\t\t\t\t\tvar nestedRematch = {\n\t\t\t\t\t\t\tcause: token + ',' + j,\n\t\t\t\t\t\t\treach: reach\n\t\t\t\t\t\t};\n\t\t\t\t\t\tmatchGrammar(text, tokenList, grammar, currentNode.prev, pos, nestedRematch);\n\n\t\t\t\t\t\t// the reach might have been extended because of the rematching\n\t\t\t\t\t\tif (rematch && nestedRematch.reach > rematch.reach) {\n\t\t\t\t\t\t\trematch.reach = nestedRematch.reach;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @typedef LinkedListNode\n\t * @property {T} value\n\t * @property {LinkedListNode<T> | null} prev The previous node.\n\t * @property {LinkedListNode<T> | null} next The next node.\n\t * @template T\n\t * @private\n\t */\n\n\t/**\n\t * @template T\n\t * @private\n\t */\n\tfunction LinkedList() {\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar head = { value: null, prev: null, next: null };\n\t\t/** @type {LinkedListNode<T>} */\n\t\tvar tail = { value: null, prev: head, next: null };\n\t\thead.next = tail;\n\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.head = head;\n\t\t/** @type {LinkedListNode<T>} */\n\t\tthis.tail = tail;\n\t\tthis.length = 0;\n\t}\n\n\t/**\n\t * Adds a new node with the given value to the list.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {T} value\n\t * @returns {LinkedListNode<T>} The added node.\n\t * @template T\n\t */\n\tfunction addAfter(list, node, value) {\n\t\t// assumes that node != list.tail && values.length >= 0\n\t\tvar next = node.next;\n\n\t\tvar newNode = { value: value, prev: node, next: next };\n\t\tnode.next = newNode;\n\t\tnext.prev = newNode;\n\t\tlist.length++;\n\n\t\treturn newNode;\n\t}\n\t/**\n\t * Removes `count` nodes after the given node. The given node will not be removed.\n\t *\n\t * @param {LinkedList<T>} list\n\t * @param {LinkedListNode<T>} node\n\t * @param {number} count\n\t * @template T\n\t */\n\tfunction removeRange(list, node, count) {\n\t\tvar next = node.next;\n\t\tfor (var i = 0; i < count && next !== list.tail; i++) {\n\t\t\tnext = next.next;\n\t\t}\n\t\tnode.next = next;\n\t\tnext.prev = node;\n\t\tlist.length -= i;\n\t}\n\t/**\n\t * @param {LinkedList<T>} list\n\t * @returns {T[]}\n\t * @template T\n\t */\n\tfunction toArray(list) {\n\t\tvar array = [];\n\t\tvar node = list.head.next;\n\t\twhile (node !== list.tail) {\n\t\t\tarray.push(node.value);\n\t\t\tnode = node.next;\n\t\t}\n\t\treturn array;\n\t}\n\n\n\tif (!_self.document) {\n\t\tif (!_self.addEventListener) {\n\t\t\t// in Node.js\n\t\t\treturn _;\n\t\t}\n\n\t\tif (!_.disableWorkerMessageHandler) {\n\t\t\t// In worker\n\t\t\t_self.addEventListener('message', function (evt) {\n\t\t\t\tvar message = JSON.parse(evt.data);\n\t\t\t\tvar lang = message.language;\n\t\t\t\tvar code = message.code;\n\t\t\t\tvar immediateClose = message.immediateClose;\n\n\t\t\t\t_self.postMessage(_.highlight(code, _.languages[lang], lang));\n\t\t\t\tif (immediateClose) {\n\t\t\t\t\t_self.close();\n\t\t\t\t}\n\t\t\t}, false);\n\t\t}\n\n\t\treturn _;\n\t}\n\n\t// Get current script and highlight\n\tvar script = _.util.currentScript();\n\n\tif (script) {\n\t\t_.filename = script.src;\n\n\t\tif (script.hasAttribute('data-manual')) {\n\t\t\t_.manual = true;\n\t\t}\n\t}\n\n\tfunction highlightAutomaticallyCallback() {\n\t\tif (!_.manual) {\n\t\t\t_.highlightAll();\n\t\t}\n\t}\n\n\tif (!_.manual) {\n\t\t// If the document state is \"loading\", then we'll use DOMContentLoaded.\n\t\t// If the document state is \"interactive\" and the prism.js script is deferred, then we'll also use the\n\t\t// DOMContentLoaded event because there might be some plugins or languages which have also been deferred and they\n\t\t// might take longer one animation frame to execute which can create a race condition where only some plugins have\n\t\t// been loaded when Prism.highlightAll() is executed, depending on how fast resources are loaded.\n\t\t// See https://github.com/PrismJS/prism/issues/2102\n\t\tvar readyState = document.readyState;\n\t\tif (readyState === 'loading' || readyState === 'interactive' && script && script.defer) {\n\t\t\tdocument.addEventListener('DOMContentLoaded', highlightAutomaticallyCallback);\n\t\t} else {\n\t\t\tif (window.requestAnimationFrame) {\n\t\t\t\twindow.requestAnimationFrame(highlightAutomaticallyCallback);\n\t\t\t} else {\n\t\t\t\twindow.setTimeout(highlightAutomaticallyCallback, 16);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn _;\n\n}(_self));\n\nif (typeof module !== 'undefined' && module.exports) {\n\tmodule.exports = Prism;\n}\n\n// hack for components to work correctly in node.js\nif (typeof global !== 'undefined') {\n\tglobal.Prism = Prism;\n}\n\n// some additional documentation/types\n\n/**\n * The expansion of a simple `RegExp` literal to support additional properties.\n *\n * @typedef GrammarToken\n * @property {RegExp} pattern The regular expression of the token.\n * @property {boolean} [lookbehind=false] If `true`, then the first capturing group of `pattern` will (effectively)\n * behave as a lookbehind group meaning that the captured text will not be part of the matched text of the new token.\n * @property {boolean} [greedy=false] Whether the token is greedy.\n * @property {string|string[]} [alias] An optional alias or list of aliases.\n * @property {Grammar} [inside] The nested grammar of this token.\n *\n * The `inside` grammar will be used to tokenize the text value of each token of this kind.\n *\n * This can be used to make nested and even recursive language definitions.\n *\n * Note: This can cause infinite recursion. Be careful when you embed different languages or even the same language into\n * each another.\n * @global\n * @public\n */\n\n/**\n * @typedef Grammar\n * @type {Object<string, RegExp | GrammarToken | Array<RegExp | GrammarToken>>}\n * @property {Grammar} [rest] An optional grammar object that will be appended to this grammar.\n * @global\n * @public\n */\n\n/**\n * A function which will invoked after an element was successfully highlighted.\n *\n * @callback HighlightCallback\n * @param {Element} element The element successfully highlighted.\n * @returns {void}\n * @global\n * @public\n */\n\n/**\n * @callback HookCallback\n * @param {Object<string, any>} env The environment variables of the hook.\n * @returns {void}\n * @global\n * @public\n */\n", "'use strict'\n\n/* global window, self */\n\n// istanbul ignore next - Don't allow Prism to run on page load in browser or\n// to start messaging from workers.\nvar ctx =\n  typeof globalThis === 'object'\n    ? globalThis\n    : typeof self === 'object'\n    ? self\n    : typeof window === 'object'\n    ? window\n    : typeof global === 'object'\n    ? global\n    : {}\n\nvar restore = capture()\n\nctx.Prism = {manual: true, disableWorkerMessageHandler: true}\n\n// Load all stuff in `prism.js` itself, except for `prism-file-highlight.js`.\n// The wrapped non-leaky grammars are loaded instead of Prism’s originals.\nvar h = require('hastscript')\nvar decode = require('parse-entities')\nvar Prism = require('prismjs/components/prism-core')\nvar markup = require('./lang/markup')\nvar css = require('./lang/css')\nvar clike = require('./lang/clike')\nvar js = require('./lang/javascript')\n\nrestore()\n\nvar own = {}.hasOwnProperty\n\n// Inherit.\nfunction Refractor() {}\n\nRefractor.prototype = Prism\n\n// Construct.\nvar refract = new Refractor()\n\n// Expose.\nmodule.exports = refract\n\n// Create.\nrefract.highlight = highlight\nrefract.register = register\nrefract.alias = alias\nrefract.registered = registered\nrefract.listLanguages = listLanguages\n\n// Register bundled grammars.\nregister(markup)\nregister(css)\nregister(clike)\nregister(js)\n\nrefract.util.encode = encode\nrefract.Token.stringify = stringify\n\nfunction register(grammar) {\n  if (typeof grammar !== 'function' || !grammar.displayName) {\n    throw new Error('Expected `function` for `grammar`, got `' + grammar + '`')\n  }\n\n  // Do not duplicate registrations.\n  if (refract.languages[grammar.displayName] === undefined) {\n    grammar(refract)\n  }\n}\n\nfunction alias(name, alias) {\n  var languages = refract.languages\n  var map = name\n  var key\n  var list\n  var length\n  var index\n\n  if (alias) {\n    map = {}\n    map[name] = alias\n  }\n\n  for (key in map) {\n    list = map[key]\n    list = typeof list === 'string' ? [list] : list\n    length = list.length\n    index = -1\n\n    while (++index < length) {\n      languages[list[index]] = languages[key]\n    }\n  }\n}\n\nfunction highlight(value, name) {\n  var sup = Prism.highlight\n  var grammar\n\n  if (typeof value !== 'string') {\n    throw new Error('Expected `string` for `value`, got `' + value + '`')\n  }\n\n  // `name` is a grammar object.\n  if (refract.util.type(name) === 'Object') {\n    grammar = name\n    name = null\n  } else {\n    if (typeof name !== 'string') {\n      throw new Error('Expected `string` for `name`, got `' + name + '`')\n    }\n\n    if (own.call(refract.languages, name)) {\n      grammar = refract.languages[name]\n    } else {\n      throw new Error('Unknown language: `' + name + '` is not registered')\n    }\n  }\n\n  return sup.call(this, value, grammar, name)\n}\n\nfunction registered(language) {\n  if (typeof language !== 'string') {\n    throw new Error('Expected `string` for `language`, got `' + language + '`')\n  }\n\n  return own.call(refract.languages, language)\n}\n\nfunction listLanguages() {\n  var languages = refract.languages\n  var list = []\n  var language\n\n  for (language in languages) {\n    if (\n      own.call(languages, language) &&\n      typeof languages[language] === 'object'\n    ) {\n      list.push(language)\n    }\n  }\n\n  return list\n}\n\nfunction stringify(value, language, parent) {\n  var env\n\n  if (typeof value === 'string') {\n    return {type: 'text', value: value}\n  }\n\n  if (refract.util.type(value) === 'Array') {\n    return stringifyAll(value, language)\n  }\n\n  env = {\n    type: value.type,\n    content: refract.Token.stringify(value.content, language, parent),\n    tag: 'span',\n    classes: ['token', value.type],\n    attributes: {},\n    language: language,\n    parent: parent\n  }\n\n  if (value.alias) {\n    env.classes = env.classes.concat(value.alias)\n  }\n\n  refract.hooks.run('wrap', env)\n\n  return h(\n    env.tag + '.' + env.classes.join('.'),\n    attributes(env.attributes),\n    env.content\n  )\n}\n\nfunction stringifyAll(values, language) {\n  var result = []\n  var length = values.length\n  var index = -1\n  var value\n\n  while (++index < length) {\n    value = values[index]\n\n    if (value !== '' && value !== null && value !== undefined) {\n      result.push(value)\n    }\n  }\n\n  index = -1\n  length = result.length\n\n  while (++index < length) {\n    value = result[index]\n    result[index] = refract.Token.stringify(value, language, result)\n  }\n\n  return result\n}\n\nfunction encode(tokens) {\n  return tokens\n}\n\nfunction attributes(attrs) {\n  var key\n\n  for (key in attrs) {\n    attrs[key] = decode(attrs[key])\n  }\n\n  return attrs\n}\n\nfunction capture() {\n  var defined = 'Prism' in ctx\n  /* istanbul ignore next */\n  var current = defined ? ctx.Prism : undefined\n\n  return restore\n\n  function restore() {\n    /* istanbul ignore else - Clean leaks after Prism. */\n    if (defined) {\n      ctx.Prism = current\n    } else {\n      delete ctx.Prism\n    }\n\n    defined = undefined\n    current = undefined\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU;AAEjB,QAAI,iBAAiB,OAAO,UAAU;AAEtC,aAAS,SAAS;AACd,UAAI,SAAS,CAAC;AAEd,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,YAAI,SAAS,UAAU,CAAC;AAExB,iBAAS,OAAO,QAAQ;AACpB,cAAI,eAAe,KAAK,QAAQ,GAAG,GAAG;AAClC,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAC5B;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;;;AClBA;AAAA;AAAA;AAEA,WAAO,UAAU;AAEjB,QAAI,QAAQ,OAAO;AAEnB,UAAM,QAAQ;AACd,UAAM,SAAS,CAAC;AAChB,UAAM,WAAW,CAAC;AAElB,aAAS,OAAO,UAAU,QAAQ,OAAO;AACvC,WAAK,WAAW;AAChB,WAAK,SAAS;AAEd,UAAI,OAAO;AACT,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;ACjBA;AAAA;AAAA;AAEA,QAAI,QAAQ;AACZ,QAAI,SAAS;AAEb,WAAO,UAAU;AAEjB,aAAS,MAAM,aAAa;AAC1B,UAAI,SAAS,YAAY;AACzB,UAAI,WAAW,CAAC;AAChB,UAAI,SAAS,CAAC;AACd,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI;AAEJ,aAAO,EAAE,QAAQ,QAAQ;AACvB,eAAO,YAAY,KAAK;AACxB,iBAAS,KAAK,KAAK,QAAQ;AAC3B,eAAO,KAAK,KAAK,MAAM;AACvB,gBAAQ,KAAK;AAAA,MACf;AAEA,aAAO,IAAI;AAAA,QACT,MAAM,MAAM,MAAM,QAAQ;AAAA,QAC1B,MAAM,MAAM,MAAM,MAAM;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC3BA;AAAA;AAAA;AAEA,WAAO,UAAU;AAEjB,aAAS,UAAU,OAAO;AACxB,aAAO,MAAM,YAAY;AAAA,IAC3B;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAEA,WAAO,UAAU;AAEjB,QAAI,QAAQ,KAAK;AAEjB,UAAM,QAAQ;AACd,UAAM,YAAY;AAClB,UAAM,WAAW;AACjB,UAAM,UAAU;AAChB,UAAM,aAAa;AACnB,UAAM,oBAAoB;AAC1B,UAAM,SAAS;AACf,UAAM,iBAAiB;AACvB,UAAM,iBAAiB;AACvB,UAAM,wBAAwB;AAC9B,UAAM,kBAAkB;AACxB,UAAM,UAAU;AAEhB,aAAS,KAAK,UAAU,WAAW;AACjC,WAAK,WAAW;AAChB,WAAK,YAAY;AAAA,IACnB;AAAA;AAAA;;;ACtBA;AAAA;AAAA;AAEA,QAAI,SAAS;AAEb,YAAQ,UAAU,UAAU;AAC5B,YAAQ,aAAa,UAAU;AAC/B,YAAQ,oBAAoB,UAAU;AACtC,YAAQ,SAAS,UAAU;AAC3B,YAAQ,iBAAiB,UAAU;AACnC,YAAQ,iBAAiB,UAAU;AACnC,YAAQ,wBAAwB,UAAU;AAE1C,aAAS,YAAY;AACnB,aAAO,KAAK,IAAI,GAAG,EAAE,MAAM;AAAA,IAC7B;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,WAAO,UAAU;AAEjB,gBAAY,YAAY,IAAI,KAAK;AACjC,gBAAY,UAAU,UAAU;AAEhC,QAAI,SAAS;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI,eAAe,OAAO;AAE1B,aAAS,YAAY,UAAU,WAAW,MAAM,OAAO;AACrD,UAAI,QAAQ;AACZ,UAAI;AAEJ,WAAK,MAAM,SAAS,KAAK;AAEzB,WAAK,KAAK,MAAM,UAAU,SAAS;AAEnC,aAAO,EAAE,QAAQ,cAAc;AAC7B,gBAAQ,OAAO,KAAK;AACpB,aAAK,MAAM,QAAQ,OAAO,MAAM,KAAK,OAAO,MAAM,KAAK,CAAC;AAAA,MAC1D;AAAA,IACF;AAEA,aAAS,KAAK,QAAQ,KAAK,OAAO;AAChC,UAAI,OAAO;AACT,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF;AAAA;AAAA;;;ACvCA;AAAA;AAAA;AAEA,QAAI,YAAY;AAChB,QAAI,SAAS;AACb,QAAI,cAAc;AAElB,WAAO,UAAU;AAEjB,aAAS,OAAO,YAAY;AAC1B,UAAI,QAAQ,WAAW;AACvB,UAAI,kBAAkB,WAAW,mBAAmB,CAAC;AACrD,UAAI,aAAa,WAAW,cAAc,CAAC;AAC3C,UAAI,QAAQ,WAAW;AACvB,UAAI,YAAY,WAAW;AAC3B,UAAI,WAAW,CAAC;AAChB,UAAI,SAAS,CAAC;AACd,UAAI;AACJ,UAAI;AAEJ,WAAK,QAAQ,OAAO;AAClB,eAAO,IAAI;AAAA,UACT;AAAA,UACA,UAAU,YAAY,IAAI;AAAA,UAC1B,MAAM,IAAI;AAAA,UACV;AAAA,QACF;AAEA,YAAI,gBAAgB,QAAQ,IAAI,MAAM,IAAI;AACxC,eAAK,kBAAkB;AAAA,QACzB;AAEA,iBAAS,IAAI,IAAI;AAEjB,eAAO,UAAU,IAAI,CAAC,IAAI;AAC1B,eAAO,UAAU,KAAK,SAAS,CAAC,IAAI;AAAA,MACtC;AAEA,aAAO,IAAI,OAAO,UAAU,QAAQ,KAAK;AAAA,IAC3C;AAAA;AAAA;;;ACtCA;AAAA;AAAA;AAEA,QAAI,SAAS;AAEb,WAAO,UAAU,OAAO;AAAA,MACtB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,YAAY;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,QACd,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAED,aAAS,eAAe,GAAG,MAAM;AAC/B,aAAO,WAAW,KAAK,MAAM,CAAC,EAAE,YAAY;AAAA,IAC9C;AAAA;AAAA;;;ACpBA;AAAA;AAAA;AAEA,QAAI,SAAS;AAEb,WAAO,UAAU,OAAO;AAAA,MACtB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,YAAY;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAAA,IACF,CAAC;AAED,aAAS,aAAa,GAAG,MAAM;AAC7B,aAAO,SAAS,KAAK,MAAM,CAAC,EAAE,YAAY;AAAA,IAC5C;AAAA;AAAA;;;AChBA;AAAA;AAAA;AAEA,WAAO,UAAU;AAEjB,aAAS,uBAAuB,YAAY,WAAW;AACrD,aAAO,aAAa,aAAa,WAAW,SAAS,IAAI;AAAA,IAC3D;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAEA,QAAI,yBAAyB;AAE7B,WAAO,UAAU;AAEjB,aAAS,yBAAyB,YAAY,UAAU;AACtD,aAAO,uBAAuB,YAAY,SAAS,YAAY,CAAC;AAAA,IAClE;AAAA;AAAA;;;ACRA;AAAA;AAAA;AAEA,QAAI,SAAS;AACb,QAAI,2BAA2B;AAE/B,WAAO,UAAU,OAAO;AAAA,MACtB,OAAO;AAAA,MACP,YAAY;AAAA,QACV,YAAY;AAAA,MACd;AAAA,MACA,WAAW;AAAA,MACX,YAAY;AAAA,QACV,OAAO;AAAA,QACP,YAAY;AAAA,MACd;AAAA,IACF,CAAC;AAAA;AAAA;;;ACfD;AAAA;AAAA;AAEA,QAAI,QAAQ;AACZ,QAAI,SAAS;AAEb,QAAI,aAAa,MAAM;AACvB,QAAI,SAAS,MAAM;AACnB,QAAI,iBAAiB,MAAM;AAE3B,WAAO,UAAU,OAAO;AAAA,MACtB,WAAW;AAAA,MACX,YAAY;AAAA,QACV,sBAAsB;AAAA,QACtB,YAAY;AAAA,QACZ,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,aAAa;AAAA,QACb,cAAc;AAAA,QACd,cAAc;AAAA,QACd,aAAa;AAAA,QACb,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,QACX,eAAe;AAAA,QACf,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,cAAc;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,qBAAqB;AAAA,QACrB,cAAc;AAAA,QACd,cAAc;AAAA,QACd,aAAa;AAAA,QACb,cAAc;AAAA,QACd,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe;AAAA,QACf,MAAM;AAAA,MACR;AAAA,IACF,CAAC;AAED,aAAS,cAAc,GAAG,MAAM;AAC9B,aAAO,SAAS,SAAS,OAAO,UAAU,KAAK,MAAM,CAAC,EAAE,YAAY;AAAA,IACtE;AAAA;AAAA;;;AClEA;AAAA;AAAA;AAEA,QAAI,QAAQ;AACZ,QAAI,SAAS;AACb,QAAI,2BAA2B;AAE/B,QAAI,UAAU,MAAM;AACpB,QAAI,oBAAoB,MAAM;AAC9B,QAAI,aAAa,MAAM;AACvB,QAAI,SAAS,MAAM;AACnB,QAAI,iBAAiB,MAAM;AAC3B,QAAI,iBAAiB,MAAM;AAE3B,WAAO,UAAU,OAAO;AAAA,MACtB,OAAO;AAAA,MACP,YAAY;AAAA,QACV,eAAe;AAAA,QACf,WAAW;AAAA,QACX,SAAS;AAAA,QACT,WAAW;AAAA,MACb;AAAA,MACA,WAAW;AAAA,MACX,iBAAiB,CAAC,WAAW,YAAY,SAAS,UAAU;AAAA,MAC5D,YAAY;AAAA;AAAA,QAEV,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,KAAK;AAAA,QACL,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,QACN,WAAW;AAAA,QACX,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,iBAAiB;AAAA,QACjB,UAAU;AAAA,QACV,cAAc;AAAA,QACd,QAAQ,SAAS;AAAA,QACjB,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,QACX,SAAS;AAAA,QACT,cAAc;AAAA,QACd,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,WAAW;AAAA,QACX,IAAI;AAAA,QACJ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,SAAS;AAAA,QACT,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,KAAK;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,WAAW;AAAA,QACX,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,SAAS;AAAA,QACT,WAAW;AAAA,QACX,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,kBAAkB;AAAA,QAClB,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,aAAa;AAAA,QACb,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,cAAc;AAAA,QACd,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,oBAAoB;AAAA,QACpB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,QACV,2BAA2B;AAAA,QAC3B,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,QACX,cAAc;AAAA,QACd,UAAU;AAAA,QACV,sBAAsB;AAAA,QACtB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,SAAS;AAAA,QACT,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,QACN,aAAa;AAAA,QACb,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,QACT,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,WAAW;AAAA,QACX,MAAM;AAAA,QACN,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA;AAAA;AAAA,QAIN,OAAO;AAAA;AAAA,QACP,OAAO;AAAA;AAAA,QACP,SAAS;AAAA;AAAA,QACT,MAAM;AAAA;AAAA,QACN,YAAY;AAAA;AAAA,QACZ,SAAS;AAAA;AAAA,QACT,QAAQ;AAAA;AAAA,QACR,aAAa;AAAA;AAAA,QACb,cAAc;AAAA;AAAA,QACd,aAAa;AAAA;AAAA,QACb,aAAa;AAAA;AAAA,QACb,MAAM;AAAA;AAAA,QACN,SAAS;AAAA;AAAA,QACT,SAAS;AAAA;AAAA,QACT,OAAO;AAAA;AAAA,QACP,MAAM;AAAA;AAAA,QACN,UAAU;AAAA;AAAA,QACV,UAAU;AAAA;AAAA,QACV,OAAO;AAAA;AAAA,QACP,SAAS;AAAA;AAAA,QACT,SAAS;AAAA;AAAA,QACT,OAAO;AAAA;AAAA,QACP,MAAM;AAAA;AAAA,QACN,OAAO;AAAA;AAAA,QACP,aAAa;AAAA;AAAA,QACb,QAAQ;AAAA;AAAA,QACR,YAAY;AAAA;AAAA,QACZ,MAAM;AAAA;AAAA,QACN,UAAU;AAAA;AAAA,QACV,QAAQ;AAAA;AAAA,QACR,cAAc;AAAA;AAAA,QACd,aAAa;AAAA;AAAA,QACb,UAAU;AAAA;AAAA,QACV,QAAQ;AAAA;AAAA,QACR,SAAS;AAAA;AAAA,QACT,QAAQ;AAAA;AAAA,QACR,QAAQ;AAAA;AAAA,QACR,SAAS;AAAA;AAAA,QACT,QAAQ;AAAA;AAAA,QACR,KAAK;AAAA;AAAA,QACL,aAAa;AAAA;AAAA,QACb,OAAO;AAAA;AAAA,QACP,QAAQ;AAAA;AAAA,QACR,WAAW;AAAA;AAAA,QACX,SAAS;AAAA;AAAA,QACT,SAAS;AAAA;AAAA,QACT,MAAM;AAAA;AAAA,QACN,WAAW;AAAA;AAAA,QACX,WAAW;AAAA;AAAA,QACX,SAAS;AAAA;AAAA,QACT,QAAQ;AAAA;AAAA,QACR,OAAO;AAAA;AAAA,QACP,QAAQ;AAAA;AAAA;AAAA,QAGR,mBAAmB;AAAA,QACnB,aAAa;AAAA,QACb,UAAU;AAAA,QACV,yBAAyB;AAAA,QACzB,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF,CAAC;AAAA;AAAA;;;ACpTD,IAAAA,gBAAA;AAAA;AAAA;AAEA,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,MAAM;AACV,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,QAAI,OAAO;AAEX,WAAO,UAAU,MAAM,CAAC,KAAK,OAAO,OAAO,MAAM,IAAI,CAAC;AAAA;AAAA;;;ACTtD;AAAA;AAAA;AAEA,QAAI,YAAY;AAChB,QAAI,cAAc;AAClB,QAAI,OAAO;AAEX,QAAI,OAAO;AAEX,WAAO,UAAU;AAEjB,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,QAAI,MAAM;AAEV,aAAS,KAAK,QAAQ,OAAO;AAC3B,UAAI,SAAS,UAAU,KAAK;AAC5B,UAAI,OAAO;AACX,UAAI,OAAO;AAEX,UAAI,UAAU,OAAO,QAAQ;AAC3B,eAAO,OAAO,SAAS,OAAO,OAAO,MAAM,CAAC;AAAA,MAC9C;AAEA,UAAI,OAAO,SAAS,KAAK,OAAO,MAAM,GAAG,CAAC,MAAM,QAAQ,MAAM,KAAK,KAAK,GAAG;AAEzE,YAAI,MAAM,OAAO,CAAC,MAAM,KAAK;AAC3B,iBAAO,kBAAkB,KAAK;AAAA,QAChC,OAAO;AACL,kBAAQ,mBAAmB,KAAK;AAAA,QAClC;AAEA,eAAO;AAAA,MACT;AAEA,aAAO,IAAI,KAAK,MAAM,KAAK;AAAA,IAC7B;AAEA,aAAS,kBAAkB,WAAW;AACpC,UAAI,QAAQ,UAAU,MAAM,CAAC,EAAE,QAAQ,MAAM,SAAS;AACtD,aAAO,OAAO,MAAM,OAAO,CAAC,EAAE,YAAY,IAAI,MAAM,MAAM,CAAC;AAAA,IAC7D;AAEA,aAAS,mBAAmB,UAAU;AACpC,UAAI,QAAQ,SAAS,MAAM,CAAC;AAE5B,UAAI,KAAK,KAAK,KAAK,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,cAAQ,MAAM,QAAQ,KAAK,KAAK;AAEhC,UAAI,MAAM,OAAO,CAAC,MAAM,KAAK;AAC3B,gBAAQ,MAAM;AAAA,MAChB;AAEA,aAAO,OAAO;AAAA,IAChB;AAEA,aAAS,MAAM,IAAI;AACjB,aAAO,MAAM,GAAG,YAAY;AAAA,IAC9B;AAEA,aAAS,UAAU,IAAI;AACrB,aAAO,GAAG,OAAO,CAAC,EAAE,YAAY;AAAA,IAClC;AAAA;AAAA;;;AChEA;AAAA;AAAA;AAEA,WAAO,UAAU;AAEjB,QAAI,SAAS;AAGb,aAAS,MAAM,UAAU,gBAAgB;AACvC,UAAI,QAAQ,YAAY;AACxB,UAAI,OAAO,kBAAkB;AAC7B,UAAI,QAAQ,CAAC;AACb,UAAI,QAAQ;AACZ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,aAAO,QAAQ,MAAM,QAAQ;AAC3B,eAAO,YAAY;AACnB,gBAAQ,OAAO,KAAK,KAAK;AACzB,mBAAW,MAAM,MAAM,OAAO,QAAQ,MAAM,QAAQ,MAAM,MAAM;AAEhE,YAAI,UAAU;AACZ,cAAI,CAAC,UAAU;AACb,mBAAO;AAAA,UACT,WAAW,aAAa,KAAK;AAC3B,kBAAM,KAAK;AAAA,UACb,WAAW,MAAM,WAAW;AAC1B,kBAAM,UAAU,KAAK,QAAQ;AAAA,UAC/B,OAAO;AACL,kBAAM,YAAY,CAAC,QAAQ;AAAA,UAC7B;AAEA,mBAAS,SAAS;AAAA,QACpB;AAEA,YAAI,OAAO;AACT,qBAAW,MAAM,CAAC;AAClB;AAAA,QACF;AAAA,MACF;AAEA,aAAO,EAAC,MAAM,WAAW,SAAS,MAAM,YAAY,OAAO,UAAU,CAAC,EAAC;AAAA,IACzE;AAAA;AAAA;;;AC1CA;AAAA;AAAA;AAEA,YAAQ,QAAQ;AAChB,YAAQ,YAAY;AAEpB,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,aAAa;AAEjB,aAAS,MAAM,OAAO;AACpB,UAAI,QAAQ,OAAO,SAAS,KAAK,EAAE,KAAK;AACxC,aAAO,UAAU,QAAQ,CAAC,IAAI,MAAM,MAAM,UAAU;AAAA,IACtD;AAEA,aAAS,UAAU,QAAQ;AACzB,aAAO,OAAO,KAAK,KAAK,EAAE,KAAK;AAAA,IACjC;AAAA;AAAA;;;AChBA;AAAA;AAAA;AAEA,YAAQ,QAAQ;AAChB,YAAQ,YAAY;AAEpB,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,QAAQ;AAGZ,aAAS,MAAM,OAAO;AACpB,UAAI,SAAS,CAAC;AACd,UAAI,QAAQ,OAAO,SAAS,KAAK;AACjC,UAAI,QAAQ,MAAM,QAAQ,KAAK;AAC/B,UAAI,YAAY;AAChB,UAAI,MAAM;AACV,UAAI;AAEJ,aAAO,CAAC,KAAK;AACX,YAAI,UAAU,IAAI;AAChB,kBAAQ,MAAM;AACd,gBAAM;AAAA,QACR;AAEA,cAAM,MAAM,MAAM,WAAW,KAAK,EAAE,KAAK;AAEzC,YAAI,OAAO,CAAC,KAAK;AACf,iBAAO,KAAK,GAAG;AAAA,QACjB;AAEA,oBAAY,QAAQ;AACpB,gBAAQ,MAAM,QAAQ,OAAO,SAAS;AAAA,MACxC;AAEA,aAAO;AAAA,IACT;AAKA,aAAS,UAAU,QAAQ,SAAS;AAClC,UAAI,WAAW,WAAW,CAAC;AAC3B,UAAI,OAAO,SAAS,YAAY,QAAQ,QAAQ;AAChD,UAAI,QAAQ,SAAS,WAAW,QAAQ;AAGxC,UAAI,OAAO,OAAO,SAAS,CAAC,MAAM,OAAO;AACvC,iBAAS,OAAO,OAAO,KAAK;AAAA,MAC9B;AAEA,aAAO,OAAO,KAAK,QAAQ,QAAQ,IAAI,EAAE,KAAK;AAAA,IAChD;AAAA;AAAA;;;ACnDA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,YAAY;AAChB,QAAI,gBAAgB;AACpB,QAAI,SAAS,iCAAkC;AAC/C,QAAI,SAAS,iCAAkC;AAE/C,WAAO,UAAU;AAEjB,QAAI,MAAM,CAAC,EAAE;AAEb,aAAS,QAAQ,QAAQ,gBAAgB,eAAe;AACtD,UAAI,SAAS,gBAAgB,gBAAgB,aAAa,IAAI;AAE9D,aAAO;AAGP,eAAS,EAAE,UAAU,YAAY;AAC/B,YAAI,OAAO,cAAc,UAAU,cAAc;AACjD,YAAI,WAAW,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AACtD,YAAI,OAAO,KAAK,QAAQ,YAAY;AACpC,YAAI;AAEJ,aAAK,UAAU,UAAU,IAAI,KAAK,QAAQ,IAAI,IAAI,OAAO,IAAI,IAAI;AAEjE,YAAI,cAAc,WAAW,YAAY,IAAI,GAAG;AAC9C,mBAAS,QAAQ,UAAU;AAC3B,uBAAa;AAAA,QACf;AAEA,YAAI,YAAY;AACd,eAAK,YAAY,YAAY;AAC3B,wBAAY,KAAK,YAAY,UAAU,WAAW,QAAQ,CAAC;AAAA,UAC7D;AAAA,QACF;AAEA,iBAAS,KAAK,UAAU,QAAQ;AAEhC,YAAI,KAAK,YAAY,YAAY;AAC/B,eAAK,UAAU,EAAC,MAAM,QAAQ,UAAU,KAAK,SAAQ;AACrD,eAAK,WAAW,CAAC;AAAA,QACnB;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,YAAY,KAAK,OAAO;AAC3C,YAAI;AACJ,YAAI;AACJ,YAAI;AAGJ,YAAI,UAAU,QAAQ,UAAU,UAAa,UAAU,OAAO;AAC5D;AAAA,QACF;AAEA,eAAO,KAAK,QAAQ,GAAG;AACvB,mBAAW,KAAK;AAChB,iBAAS;AAGT,YAAI,OAAO,WAAW,UAAU;AAC9B,cAAI,KAAK,gBAAgB;AACvB,qBAAS,OAAO,MAAM;AAAA,UACxB,WAAW,KAAK,gBAAgB;AAC9B,qBAAS,OAAO,MAAM;AAAA,UACxB,WAAW,KAAK,uBAAuB;AACrC,qBAAS,OAAO,OAAO,MAAM,EAAE,KAAK,GAAG,CAAC;AAAA,UAC1C;AAAA,QACF;AAGA,YAAI,aAAa,WAAW,OAAO,UAAU,UAAU;AACrD,mBAAS,MAAM,MAAM;AAAA,QACvB;AAGA,YAAI,aAAa,eAAe,WAAW,WAAW;AACpD,mBAAS,WAAW,UAAU,OAAO,MAAM;AAAA,QAC7C;AAEA,mBAAW,QAAQ,IAAI,gBAAgB,MAAM,UAAU,MAAM;AAAA,MAC/D;AAAA,IACF;AAEA,aAAS,WAAW,OAAO,MAAM;AAC/B,aACE,OAAO,UAAU,YACjB,YAAY,SACZ,OAAO,KAAK,SAAS,KAAK;AAAA,IAE9B;AAEA,aAAS,OAAO,SAAS,OAAO;AAC9B,UAAI,OAAO,MAAM;AAEjB,UAAI,YAAY,WAAW,CAAC,QAAQ,OAAO,SAAS,UAAU;AAC5D,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,MAAM,aAAa,YAAY,YAAY,MAAM,UAAU;AACpE,eAAO;AAAA,MACT;AAEA,aAAO,KAAK,YAAY;AAExB,UAAI,YAAY,UAAU;AACxB,eACE,SAAS,UACT,SAAS,YACT,SAAS,WACT,SAAS;AAAA,MAEb;AAEA,aAAO,WAAW;AAAA,IACpB;AAEA,aAAS,SAAS,OAAO,OAAO;AAC9B,UAAI;AACJ,UAAI;AAEJ,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAC1D,cAAM,KAAK,EAAC,MAAM,QAAQ,OAAO,OAAO,KAAK,EAAC,CAAC;AAC/C;AAAA,MACF;AAEA,UAAI,OAAO,UAAU,YAAY,YAAY,OAAO;AAClD,gBAAQ;AACR,iBAAS,MAAM;AAEf,eAAO,EAAE,QAAQ,QAAQ;AACvB,mBAAS,OAAO,MAAM,KAAK,CAAC;AAAA,QAC9B;AAEA;AAAA,MACF;AAEA,UAAI,OAAO,UAAU,YAAY,EAAE,UAAU,QAAQ;AACnD,cAAM,IAAI,MAAM,2CAA2C,QAAQ,GAAG;AAAA,MACxE;AAEA,YAAM,KAAK,KAAK;AAAA,IAClB;AAGA,aAAS,gBAAgB,MAAM,MAAM,OAAO;AAC1C,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,OAAO,UAAU,YAAY,EAAE,YAAY,QAAQ;AACrD,eAAO,eAAe,MAAM,MAAM,KAAK;AAAA,MACzC;AAEA,eAAS,MAAM;AACf,cAAQ;AACR,eAAS,CAAC;AAEV,aAAO,EAAE,QAAQ,QAAQ;AACvB,eAAO,KAAK,IAAI,eAAe,MAAM,MAAM,MAAM,KAAK,CAAC;AAAA,MACzD;AAEA,aAAO;AAAA,IACT;AAGA,aAAS,eAAe,MAAM,MAAM,OAAO;AACzC,UAAI,SAAS;AAEb,UAAI,KAAK,UAAU,KAAK,gBAAgB;AACtC,YAAI,CAAC,MAAM,MAAM,KAAK,WAAW,IAAI;AACnC,mBAAS,OAAO,MAAM;AAAA,QACxB;AAAA,MACF,WAAW,KAAK,WAAW,KAAK,mBAAmB;AAEjD,YACE,OAAO,WAAW,aACjB,WAAW,MAAM,UAAU,KAAK,MAAM,UAAU,IAAI,IACrD;AACA,mBAAS;AAAA,QACX;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,MAAM,OAAO;AACpB,UAAI,SAAS,CAAC;AACd,UAAI;AAEJ,WAAK,OAAO,OAAO;AACjB,eAAO,KAAK,CAAC,KAAK,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC;AAAA,MAC1C;AAEA,aAAO,OAAO,KAAK,IAAI;AAAA,IACzB;AAEA,aAAS,gBAAgB,QAAQ;AAC/B,UAAI,SAAS,OAAO;AACpB,UAAI,QAAQ;AACZ,UAAI,SAAS,CAAC;AACd,UAAI;AAEJ,aAAO,EAAE,QAAQ,QAAQ;AACvB,gBAAQ,OAAO,KAAK;AACpB,eAAO,MAAM,YAAY,CAAC,IAAI;AAAA,MAChC;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACnNA,IAAAC,gBAAA;AAAA;AAAA;AAEA,QAAI,SAAS;AACb,QAAI,UAAU;AAEd,QAAI,OAAO,QAAQ,QAAQ,KAAK;AAChC,SAAK,cAAc;AAEnB,WAAO,UAAU;AAAA;AAAA;;;ACRjB;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA;AAAA,MACE,OAAS;AAAA,MACT,KAAO;AAAA,MACP,QAAU;AAAA,MACV,OAAS;AAAA,MACT,QAAU;AAAA,MACV,OAAS;AAAA,MACT,QAAU;AAAA,MACV,MAAQ;AAAA,MACR,MAAQ;AAAA,MACR,QAAU;AAAA,MACV,KAAO;AAAA,MACP,QAAU;AAAA,MACV,OAAS;AAAA,MACT,QAAU;AAAA,MACV,MAAQ;AAAA,MACR,IAAM;AAAA,MACN,QAAU;AAAA,MACV,OAAS;AAAA,MACT,QAAU;AAAA,MACV,MAAQ;AAAA,MACR,IAAM;AAAA,MACN,QAAU;AAAA,MACV,QAAU;AAAA,MACV,OAAS;AAAA,MACT,QAAU;AAAA,MACV,QAAU;AAAA,MACV,QAAU;AAAA,MACV,MAAQ;AAAA,MACR,MAAQ;AAAA,MACR,KAAO;AAAA,MACP,OAAS;AAAA,MACT,QAAU;AAAA,MACV,OAAS;AAAA,MACT,QAAU;AAAA,MACV,MAAQ;AAAA,MACR,QAAU;AAAA,MACV,QAAU;AAAA,MACV,OAAS;AAAA,MACT,OAAS;AAAA,MACT,OAAS;AAAA,MACT,QAAU;AAAA,MACV,KAAO;AAAA,MACP,OAAS;AAAA,MACT,QAAU;AAAA,MACV,MAAQ;AAAA,MACR,QAAU;AAAA,MACV,QAAU;AAAA,MACV,OAAS;AAAA,MACT,MAAQ;AAAA,MACR,MAAQ;AAAA,MACR,QAAU;AAAA,MACV,KAAO;AAAA,MACP,QAAU;AAAA,MACV,QAAU;AAAA,MACV,OAAS;AAAA,MACT,QAAU;AAAA,MACV,KAAO;AAAA,MACP,MAAQ;AAAA,MACR,QAAU;AAAA,MACV,QAAU;AAAA,MACV,QAAU;AAAA,MACV,IAAM;AAAA,MACN,QAAU;AAAA,MACV,OAAS;AAAA,MACT,OAAS;AAAA,MACT,QAAU;AAAA,MACV,QAAU;AAAA,MACV,MAAQ;AAAA,MACR,OAAS;AAAA,MACT,IAAM;AAAA,MACN,MAAQ;AAAA,MACR,OAAS;AAAA,MACT,QAAU;AAAA,MACV,MAAQ;AAAA,MACR,KAAO;AAAA,MACP,QAAU;AAAA,MACV,QAAU;AAAA,MACV,OAAS;AAAA,MACT,QAAU;AAAA,MACV,MAAQ;AAAA,MACR,MAAQ;AAAA,MACR,QAAU;AAAA,MACV,QAAU;AAAA,MACV,MAAQ;AAAA,MACR,MAAQ;AAAA,MACR,QAAU;AAAA,MACV,OAAS;AAAA,MACT,MAAQ;AAAA,MACR,OAAS;AAAA,MACT,KAAO;AAAA,MACP,MAAQ;AAAA,MACR,KAAO;AAAA,MACP,MAAQ;AAAA,MACR,MAAQ;AAAA,MACR,MAAQ;AAAA,MACR,OAAS;AAAA,MACT,OAAS;AAAA,MACT,OAAS;AAAA,MACT,QAAU;AAAA,MACV,OAAS;AAAA,MACT,QAAU;AAAA,MACV,KAAO;AAAA,MACP,MAAQ;AAAA,MACR,QAAU;AAAA,MACV,KAAO;AAAA,MACP,MAAQ;AAAA,IACV;AAAA;AAAA;;;AC3GA;AAAA;AAAA;AAAA,MACE,KAAK;AAAA,MACL,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAAA;AAAA;;;AC7BA;AAAA;AAAA;AAEA,WAAO,UAAU;AAIjB,aAAS,QAAQ,WAAW;AAC1B,UAAI,OAAO,OAAO,cAAc,WAAW,UAAU,WAAW,CAAC,IAAI;AAErE,aAAO,QAAQ,MAAM,QAAQ;AAAA,IAC/B;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAEA,WAAO,UAAU;AAIjB,aAAS,YAAY,WAAW;AAC9B,UAAI,OAAO,OAAO,cAAc,WAAW,UAAU,WAAW,CAAC,IAAI;AAErE,aACG,QAAQ,MAAc,QAAQ,OAC9B,QAAQ,MAAc,QAAQ,MAC9B,QAAQ,MAAc,QAAQ;AAAA,IAEnC;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,WAAO,UAAU;AAIjB,aAAS,aAAa,WAAW;AAC/B,UAAI,OAAO,OAAO,cAAc,WAAW,UAAU,WAAW,CAAC,IAAI;AAErE,aACG,QAAQ,MAAM,QAAQ,OACtB,QAAQ,MAAM,QAAQ;AAAA,IAE3B;AAAA;AAAA;;;ACbA;AAAA;AAAA;AAEA,QAAI,eAAe;AACnB,QAAI,UAAU;AAEd,WAAO,UAAU;AAIjB,aAAS,eAAe,WAAW;AACjC,aAAO,aAAa,SAAS,KAAK,QAAQ,SAAS;AAAA,IACrD;AAAA;AAAA;;;ACXA;AAAA;AAAA;AAIA,QAAI;AAEJ,QAAI,YAAY;AAEhB,WAAO,UAAU;AAEjB,aAAS,aAAa,YAAY;AAChC,UAAI,SAAS,MAAM,aAAa;AAChC,UAAI;AAEJ,WAAK,MAAM,SAAS,cAAc,GAAG;AACrC,SAAG,YAAY;AACf,aAAO,GAAG;AAOV,UAAI,KAAK,WAAW,KAAK,SAAS,CAAC,MAAM,aAAa,eAAe,QAAQ;AAC3E,eAAO;AAAA,MACT;AAGA,aAAO,SAAS,SAAS,QAAQ;AAAA,IACnC;AAAA;AAAA;;;AC7BA;AAAA;AAAA;AAEA,QAAI,SAAS;AACb,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,cAAc;AAClB,QAAI,iBAAiB;AACrB,QAAI,eAAe;AAEnB,WAAO,UAAU;AAEjB,QAAI,MAAM,CAAC,EAAE;AACb,QAAI,eAAe,OAAO;AAC1B,QAAI,OAAO,SAAS;AAGpB,QAAI,WAAW;AAAA,MACb,SAAS;AAAA,MACT,WAAW;AAAA,MACX,MAAM;AAAA,MACN,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,UAAU,CAAC;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,eAAe;AAAA,IACjB;AAGA,QAAI,MAAM;AACV,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,QAAQ;AACZ,QAAI,YAAY;AAChB,QAAI,YAAY;AAChB,QAAI,WAAW;AACf,QAAI,WAAW;AACf,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,QAAI,uBAAuB;AAG3B,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AAGX,QAAI,QAAQ,CAAC;AAEb,UAAM,IAAI,IAAI;AACd,UAAM,IAAI,IAAI;AAMd,QAAI,QAAQ,CAAC;AAEb,UAAM,IAAI,IAAI;AACd,UAAM,IAAI,IAAI;AACd,UAAM,IAAI,IAAI;AAGd,QAAI,qBAAqB;AACzB,QAAI,uBAAuB;AAC3B,QAAI,aAAa;AACjB,QAAI,eAAe;AACnB,QAAI,eAAe;AACnB,QAAI,oBAAoB;AACxB,QAAI,oBAAoB;AAGxB,QAAI,WAAW,CAAC;AAEhB,aAAS,kBAAkB,IACzB;AACF,aAAS,oBAAoB,IAC3B;AACF,aAAS,UAAU,IAAI;AACvB,aAAS,YAAY,IAAI;AACzB,aAAS,YAAY,IAAI;AACzB,aAAS,iBAAiB,IACxB;AACF,aAAS,iBAAiB,IACxB;AAGF,aAAS,cAAc,OAAO,SAAS;AACrC,UAAI,WAAW,CAAC;AAChB,UAAI;AACJ,UAAI;AAEJ,UAAI,CAAC,SAAS;AACZ,kBAAU,CAAC;AAAA,MACb;AAEA,WAAK,OAAO,UAAU;AACpB,iBAAS,QAAQ,GAAG;AACpB,iBAAS,GAAG,IACV,WAAW,QAAQ,WAAW,SAAY,SAAS,GAAG,IAAI;AAAA,MAC9D;AAEA,UAAI,SAAS,SAAS,UAAU,SAAS,SAAS,OAAO;AACvD,iBAAS,SAAS,SAAS,SAAS,UAAU,CAAC;AAC/C,iBAAS,WAAW,SAAS,SAAS;AAAA,MACxC;AAEA,aAAO,MAAM,OAAO,QAAQ;AAAA,IAC9B;AAIA,aAAS,MAAM,OAAO,UAAU;AAC9B,UAAI,aAAa,SAAS;AAC1B,UAAI,gBAAgB,SAAS;AAC7B,UAAI,aAAa,SAAS;AAC1B,UAAI,kBAAkB,SAAS;AAC/B,UAAI,gBAAgB,SAAS;AAC7B,UAAI,cAAc,SAAS;AAC3B,UAAI,mBAAmB,SAAS;AAChC,UAAI,iBAAiB,SAAS;AAC9B,UAAI,MAAM,SAAS;AACnB,UAAI,SAAS,SAAS,UAAU,CAAC;AACjC,UAAI,SAAS,MAAM;AACnB,UAAI,QAAQ;AACZ,UAAI,QAAQ;AACZ,UAAI,SAAS,IAAI,UAAU;AAC3B,UAAI,OAAO,IAAI,QAAQ;AACvB,UAAI,QAAQ;AACZ,UAAI,SAAS,CAAC;AACd,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,OAAO,eAAe,UAAU;AAClC,qBAAa,WAAW,WAAW,CAAC;AAAA,MACtC;AAGA,aAAO,IAAI;AAGX,gBAAU,gBAAgB,aAAa;AAIvC;AACA;AAEA,aAAO,EAAE,QAAQ,QAAQ;AAEvB,YAAI,cAAc,UAAU;AAC1B,mBAAS,OAAO,KAAK,KAAK;AAAA,QAC5B;AAEA,oBAAY,MAAM,WAAW,KAAK;AAElC,YAAI,cAAc,WAAW;AAC3B,sBAAY,MAAM,WAAW,QAAQ,CAAC;AAGtC,cACE,cAAc,OACd,cAAc,YACd,cAAc,YACd,cAAc,SACd,cAAc,aACd,cAAc,YACd,cAAc,aACb,cAAc,cAAc,YAC7B;AAIA,qBAAS,aAAa,SAAS;AAC/B;AAEA;AAAA,UACF;AAEA,kBAAQ,QAAQ;AAChB,kBAAQ;AACR,gBAAM;AAEN,cAAI,cAAc,YAAY;AAE5B,kBAAM,EAAE;AAGR,wBAAY,MAAM,WAAW,GAAG;AAEhC,gBAAI,cAAc,cAAc,cAAc,YAAY;AAExD,qBAAO;AACP,oBAAM,EAAE;AAAA,YACV,OAAO;AAEL,qBAAO;AAAA,YACT;AAAA,UACF,OAAO;AAEL,mBAAO;AAAA,UACT;AAEA,6BAAmB;AACnB,mBAAS;AACT,uBAAa;AACb,iBAAO,MAAM,IAAI;AACjB;AAEA,iBAAO,EAAE,MAAM,QAAQ;AACrB,wBAAY,MAAM,WAAW,GAAG;AAEhC,gBAAI,CAAC,KAAK,SAAS,GAAG;AACpB;AAAA,YACF;AAEA,0BAAc,aAAa,SAAS;AAKpC,gBAAI,SAAS,QAAQ,IAAI,KAAK,QAAQ,UAAU,GAAG;AACjD,iCAAmB;AACnB,uBAAS,OAAO,UAAU;AAAA,YAC5B;AAAA,UACF;AAEA,uBAAa,MAAM,WAAW,GAAG,MAAM;AAEvC,cAAI,YAAY;AACd;AAEA,0BAAc,SAAS,OAAO,aAAa,UAAU,IAAI;AAEzD,gBAAI,aAAa;AACf,iCAAmB;AACnB,uBAAS;AAAA,YACX;AAAA,UACF;AAEA,iBAAO,IAAI,MAAM;AAEjB,cAAI,CAAC,cAAc,CAAC,eAAe;AAAA,UAEnC,WAAW,CAAC,YAAY;AAGtB,gBAAI,SAAS,MAAM;AACjB,sBAAQ,cAAc,IAAI;AAAA,YAC5B;AAAA,UACF,WAAW,SAAS,MAAM;AAGxB,gBAAI,cAAc,CAAC,QAAQ;AACzB,sBAAQ,cAAc,CAAC;AAAA,YACzB,OAAO;AAGL,kBAAI,qBAAqB,YAAY;AACnC,sBAAM,QAAQ,iBAAiB;AAC/B,uBAAO,IAAI,MAAM;AACjB,6BAAa;AAAA,cACf;AAGA,kBAAI,CAAC,YAAY;AACf,yBAAS,mBAAmB,qBAAqB;AAEjD,oBAAI,SAAS,WAAW;AACtB,8BAAY,MAAM,WAAW,GAAG;AAEhC,sBAAI,cAAc,UAAU;AAC1B,4BAAQ,QAAQ,IAAI;AACpB,6BAAS;AAAA,kBACX,WAAW,eAAe,SAAS,GAAG;AACpC,6BAAS;AAAA,kBACX,OAAO;AACL,4BAAQ,QAAQ,IAAI;AAAA,kBACtB;AAAA,gBACF,OAAO;AACL,0BAAQ,QAAQ,IAAI;AAAA,gBACtB;AAAA,cACF;AAAA,YACF;AAEA,wBAAY;AAAA,UACd,OAAO;AACL,gBAAI,CAAC,YAAY;AAGf,sBAAQ,sBAAsB,IAAI;AAAA,YACpC;AAGA,wBAAY,SAAS,YAAY,MAAM,IAAI,CAAC;AAI5C,gBAAI,WAAW,SAAS,GAAG;AACzB,sBAAQ,mBAAmB,IAAI;AAC/B,0BAAY,aAAa,oBAAoB;AAAA,YAC/C,WAAW,aAAa,SAAS;AAG/B,sBAAQ,mBAAmB,IAAI;AAC/B,0BAAY,QAAQ,SAAS;AAAA,YAC/B,OAAO;AAEL,uBAAS;AAGT,kBAAI,WAAW,SAAS,GAAG;AACzB,wBAAQ,mBAAmB,IAAI;AAAA,cACjC;AAGA,kBAAI,YAAY,OAAQ;AACtB,6BAAa;AACb,0BAAU,aAAc,eAAe,KAAK,QAAU,KAAM;AAC5D,4BAAY,QAAU,YAAY;AAAA,cACpC;AAEA,0BAAY,SAAS,aAAa,SAAS;AAAA,YAC7C;AAAA,UACF;AAIA,cAAI,WAAW;AACb,kBAAM;AAEN,mBAAO,IAAI;AACX,oBAAQ,MAAM;AACd,sBAAU,MAAM,QAAQ;AACxB,mBAAO,KAAK,SAAS;AACrB,mBAAO,IAAI;AACX,iBAAK;AAEL,gBAAI,iBAAiB;AACnB,8BAAgB;AAAA,gBACd;AAAA,gBACA;AAAA,gBACA,EAAC,OAAO,MAAM,KAAK,KAAI;AAAA,gBACvB,MAAM,MAAM,QAAQ,GAAG,GAAG;AAAA,cAC5B;AAAA,YACF;AAEA,mBAAO;AAAA,UACT,OAAO;AAKL,yBAAa,MAAM,MAAM,QAAQ,GAAG,GAAG;AACvC,qBAAS;AACT,sBAAU,WAAW;AACrB,oBAAQ,MAAM;AAAA,UAChB;AAAA,QACF,OAAO;AAEL,cACE,cAAc,IACd;AACA;AACA;AACA,qBAAS;AAAA,UACX;AAEA,cAAI,cAAc,WAAW;AAC3B,qBAAS,aAAa,SAAS;AAC/B;AAAA,UACF,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAGA,aAAO,OAAO,KAAK,EAAE;AAGrB,eAAS,MAAM;AACb,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,QAAQ,SAAS,IAAI,UAAU;AAAA,QACjC;AAAA,MACF;AAGA,eAAS,WAAW,MAAM,QAAQ;AAChC,YAAI,WAAW,IAAI;AAEnB,iBAAS,UAAU;AACnB,iBAAS,UAAU;AAEnB,sBAAc,KAAK,gBAAgB,SAAS,IAAI,GAAG,UAAU,IAAI;AAAA,MACnE;AAKA,eAAS,QAAQ;AACf,YAAI,OAAO;AACT,iBAAO,KAAK,KAAK;AAEjB,cAAI,YAAY;AACd,uBAAW,KAAK,aAAa,OAAO,EAAC,OAAO,MAAM,KAAK,IAAI,EAAC,CAAC;AAAA,UAC/D;AAEA,kBAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAGA,aAAS,WAAW,MAAM;AACxB,aAAQ,QAAQ,SAAU,QAAQ,SAAW,OAAO;AAAA,IACtD;AAGA,aAAS,WAAW,MAAM;AACxB,aACG,QAAQ,KAAU,QAAQ,KAC3B,SAAS,MACR,QAAQ,MAAU,QAAQ,MAC1B,QAAQ,OAAU,QAAQ,OAC1B,QAAQ,SAAU,QAAQ,UAC1B,OAAO,WAAY,UACnB,OAAO,WAAY;AAAA,IAExB;AAAA;AAAA;;;AClcA;AAAA;AAEA,QAAI,QAAS,OAAO,WAAW,cAC5B,SAEA,OAAO,sBAAsB,eAAe,gBAAgB,oBAC1D,OACA,CAAC;AAWN,QAAI,QAAS,SAAUC,QAAO;AAG7B,UAAI,OAAO;AACX,UAAI,WAAW;AAGf,UAAI,mBAAmB,CAAC;AAGxB,UAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAsBP,QAAQA,OAAM,SAASA,OAAM,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAsBnC,6BAA6BA,OAAM,SAASA,OAAM,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAWxD,MAAM;AAAA,UACL,QAAQ,SAAS,OAAO,QAAQ;AAC/B,gBAAI,kBAAkB,OAAO;AAC5B,qBAAO,IAAI,MAAM,OAAO,MAAM,OAAO,OAAO,OAAO,GAAG,OAAO,KAAK;AAAA,YACnE,WAAW,MAAM,QAAQ,MAAM,GAAG;AACjC,qBAAO,OAAO,IAAI,MAAM;AAAA,YACzB,OAAO;AACN,qBAAO,OAAO,QAAQ,MAAM,OAAO,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,WAAW,GAAG;AAAA,YAClF;AAAA,UACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAkBA,MAAM,SAAU,GAAG;AAClB,mBAAO,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,UACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA,OAAO,SAAU,KAAK;AACrB,gBAAI,CAAC,IAAI,MAAM,GAAG;AACjB,qBAAO,eAAe,KAAK,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AAAA,YACzD;AACA,mBAAO,IAAI,MAAM;AAAA,UAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAYA,OAAO,SAAS,UAAU,GAAG,SAAS;AACrC,sBAAU,WAAW,CAAC;AAEtB,gBAAI;AAAO,gBAAI;AACf,oBAAQ,EAAE,KAAK,KAAK,CAAC,GAAG;AAAA,cACvB,KAAK;AACJ,qBAAK,EAAE,KAAK,MAAM,CAAC;AACnB,oBAAI,QAAQ,EAAE,GAAG;AAChB,yBAAO,QAAQ,EAAE;AAAA,gBAClB;AACA;AAAA,gBAA4C,CAAC;AAC7C,wBAAQ,EAAE,IAAI;AAEd,yBAAS,OAAO,GAAG;AAClB,sBAAI,EAAE,eAAe,GAAG,GAAG;AAC1B,0BAAM,GAAG,IAAI,UAAU,EAAE,GAAG,GAAG,OAAO;AAAA,kBACvC;AAAA,gBACD;AAEA;AAAA;AAAA,kBAA2B;AAAA;AAAA,cAE5B,KAAK;AACJ,qBAAK,EAAE,KAAK,MAAM,CAAC;AACnB,oBAAI,QAAQ,EAAE,GAAG;AAChB,yBAAO,QAAQ,EAAE;AAAA,gBAClB;AACA,wBAAQ,CAAC;AACT,wBAAQ,EAAE,IAAI;AAEd;AAAA;AAAA,gBAAyC,EAAK,QAAQ,SAAU,GAAG,GAAG;AACrE,wBAAM,CAAC,IAAI,UAAU,GAAG,OAAO;AAAA,gBAChC,CAAC;AAED;AAAA;AAAA,kBAA2B;AAAA;AAAA,cAE5B;AACC,uBAAO;AAAA,YACT;AAAA,UACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAUA,aAAa,SAAU,SAAS;AAC/B,mBAAO,SAAS;AACf,kBAAI,IAAI,KAAK,KAAK,QAAQ,SAAS;AACnC,kBAAI,GAAG;AACN,uBAAO,EAAE,CAAC,EAAE,YAAY;AAAA,cACzB;AACA,wBAAU,QAAQ;AAAA,YACnB;AACA,mBAAO;AAAA,UACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASA,aAAa,SAAU,SAAS,UAAU;AAGzC,oBAAQ,YAAY,QAAQ,UAAU,QAAQ,OAAO,MAAM,IAAI,GAAG,EAAE;AAIpE,oBAAQ,UAAU,IAAI,cAAc,QAAQ;AAAA,UAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UASA,eAAe,WAAY;AAC1B,gBAAI,OAAO,aAAa,aAAa;AACpC,qBAAO;AAAA,YACR;AACA,gBAAI,mBAAmB,YAAY,IAAI,GAAwC;AAC9E;AAAA;AAAA,gBAA2B,SAAS;AAAA;AAAA,YACrC;AAMA,gBAAI;AACH,oBAAM,IAAI,MAAM;AAAA,YACjB,SAAS,KAAK;AAQb,kBAAI,OAAO,qCAAqC,KAAK,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC;AACxE,kBAAI,KAAK;AACR,oBAAI,UAAU,SAAS,qBAAqB,QAAQ;AACpD,yBAAS,KAAK,SAAS;AACtB,sBAAI,QAAQ,CAAC,EAAE,OAAO,KAAK;AAC1B,2BAAO,QAAQ,CAAC;AAAA,kBACjB;AAAA,gBACD;AAAA,cACD;AACA,qBAAO;AAAA,YACR;AAAA,UACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAqBA,UAAU,SAAU,SAAS,WAAW,mBAAmB;AAC1D,gBAAI,KAAK,QAAQ;AAEjB,mBAAO,SAAS;AACf,kBAAI,YAAY,QAAQ;AACxB,kBAAI,UAAU,SAAS,SAAS,GAAG;AAClC,uBAAO;AAAA,cACR;AACA,kBAAI,UAAU,SAAS,EAAE,GAAG;AAC3B,uBAAO;AAAA,cACR;AACA,wBAAU,QAAQ;AAAA,YACnB;AACA,mBAAO,CAAC,CAAC;AAAA,UACV;AAAA,QACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QASA,WAAW;AAAA;AAAA;AAAA;AAAA,UAIV,OAAO;AAAA,UACP,WAAW;AAAA,UACX,MAAM;AAAA,UACN,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UA8BL,QAAQ,SAAU,IAAI,OAAO;AAC5B,gBAAIC,QAAO,EAAE,KAAK,MAAM,EAAE,UAAU,EAAE,CAAC;AAEvC,qBAAS,OAAO,OAAO;AACtB,cAAAA,MAAK,GAAG,IAAI,MAAM,GAAG;AAAA,YACtB;AAEA,mBAAOA;AAAA,UACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UA6EA,cAAc,SAAU,QAAQ,QAAQ,QAAQ,MAAM;AACrD,mBAAO;AAAA,YAA4B,EAAE;AACrC,gBAAI,UAAU,KAAK,MAAM;AAEzB,gBAAI,MAAM,CAAC;AAEX,qBAAS,SAAS,SAAS;AAC1B,kBAAI,QAAQ,eAAe,KAAK,GAAG;AAElC,oBAAI,SAAS,QAAQ;AACpB,2BAAS,YAAY,QAAQ;AAC5B,wBAAI,OAAO,eAAe,QAAQ,GAAG;AACpC,0BAAI,QAAQ,IAAI,OAAO,QAAQ;AAAA,oBAChC;AAAA,kBACD;AAAA,gBACD;AAGA,oBAAI,CAAC,OAAO,eAAe,KAAK,GAAG;AAClC,sBAAI,KAAK,IAAI,QAAQ,KAAK;AAAA,gBAC3B;AAAA,cACD;AAAA,YACD;AAEA,gBAAI,MAAM,KAAK,MAAM;AACrB,iBAAK,MAAM,IAAI;AAGf,cAAE,UAAU,IAAI,EAAE,WAAW,SAAU,KAAK,OAAO;AAClD,kBAAI,UAAU,OAAO,OAAO,QAAQ;AACnC,qBAAK,GAAG,IAAI;AAAA,cACb;AAAA,YACD,CAAC;AAED,mBAAO;AAAA,UACR;AAAA;AAAA,UAGA,KAAK,SAAS,IAAI,GAAG,UAAU,MAAM,SAAS;AAC7C,sBAAU,WAAW,CAAC;AAEtB,gBAAI,QAAQ,EAAE,KAAK;AAEnB,qBAAS,KAAK,GAAG;AAChB,kBAAI,EAAE,eAAe,CAAC,GAAG;AACxB,yBAAS,KAAK,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,CAAC;AAEnC,oBAAI,WAAW,EAAE,CAAC;AAClB,oBAAI,eAAe,EAAE,KAAK,KAAK,QAAQ;AAEvC,oBAAI,iBAAiB,YAAY,CAAC,QAAQ,MAAM,QAAQ,CAAC,GAAG;AAC3D,0BAAQ,MAAM,QAAQ,CAAC,IAAI;AAC3B,sBAAI,UAAU,UAAU,MAAM,OAAO;AAAA,gBACtC,WAAW,iBAAiB,WAAW,CAAC,QAAQ,MAAM,QAAQ,CAAC,GAAG;AACjE,0BAAQ,MAAM,QAAQ,CAAC,IAAI;AAC3B,sBAAI,UAAU,UAAU,GAAG,OAAO;AAAA,gBACnC;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,QAEA,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAcV,cAAc,SAAU,OAAO,UAAU;AACxC,YAAE,kBAAkB,UAAU,OAAO,QAAQ;AAAA,QAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAiBA,mBAAmB,SAAU,WAAW,OAAO,UAAU;AACxD,cAAI,MAAM;AAAA,YACT;AAAA,YACA;AAAA,YACA,UAAU;AAAA,UACX;AAEA,YAAE,MAAM,IAAI,uBAAuB,GAAG;AAEtC,cAAI,WAAW,MAAM,UAAU,MAAM,MAAM,IAAI,UAAU,iBAAiB,IAAI,QAAQ,CAAC;AAEvF,YAAE,MAAM,IAAI,iCAAiC,GAAG;AAEhD,mBAAS,IAAI,GAAG,SAAU,UAAU,IAAI,SAAS,GAAG,KAAK;AACxD,cAAE,iBAAiB,SAAS,UAAU,MAAM,IAAI,QAAQ;AAAA,UACzD;AAAA,QACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QA8BA,kBAAkB,SAAU,SAAS,OAAO,UAAU;AAErD,cAAI,WAAW,EAAE,KAAK,YAAY,OAAO;AACzC,cAAI,UAAU,EAAE,UAAU,QAAQ;AAGlC,YAAE,KAAK,YAAY,SAAS,QAAQ;AAGpC,cAAI,SAAS,QAAQ;AACrB,cAAI,UAAU,OAAO,SAAS,YAAY,MAAM,OAAO;AACtD,cAAE,KAAK,YAAY,QAAQ,QAAQ;AAAA,UACpC;AAEA,cAAI,OAAO,QAAQ;AAEnB,cAAI,MAAM;AAAA,YACT;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAEA,mBAAS,sBAAsB,iBAAiB;AAC/C,gBAAI,kBAAkB;AAEtB,cAAE,MAAM,IAAI,iBAAiB,GAAG;AAEhC,gBAAI,QAAQ,YAAY,IAAI;AAE5B,cAAE,MAAM,IAAI,mBAAmB,GAAG;AAClC,cAAE,MAAM,IAAI,YAAY,GAAG;AAC3B,wBAAY,SAAS,KAAK,IAAI,OAAO;AAAA,UACtC;AAEA,YAAE,MAAM,IAAI,uBAAuB,GAAG;AAGtC,mBAAS,IAAI,QAAQ;AACrB,cAAI,UAAU,OAAO,SAAS,YAAY,MAAM,SAAS,CAAC,OAAO,aAAa,UAAU,GAAG;AAC1F,mBAAO,aAAa,YAAY,GAAG;AAAA,UACpC;AAEA,cAAI,CAAC,IAAI,MAAM;AACd,cAAE,MAAM,IAAI,YAAY,GAAG;AAC3B,wBAAY,SAAS,KAAK,IAAI,OAAO;AACrC;AAAA,UACD;AAEA,YAAE,MAAM,IAAI,oBAAoB,GAAG;AAEnC,cAAI,CAAC,IAAI,SAAS;AACjB,kCAAsB,EAAE,KAAK,OAAO,IAAI,IAAI,CAAC;AAC7C;AAAA,UACD;AAEA,cAAI,SAASD,OAAM,QAAQ;AAC1B,gBAAI,SAAS,IAAI,OAAO,EAAE,QAAQ;AAElC,mBAAO,YAAY,SAAU,KAAK;AACjC,oCAAsB,IAAI,IAAI;AAAA,YAC/B;AAEA,mBAAO,YAAY,KAAK,UAAU;AAAA,cACjC,UAAU,IAAI;AAAA,cACd,MAAM,IAAI;AAAA,cACV,gBAAgB;AAAA,YACjB,CAAC,CAAC;AAAA,UACH,OAAO;AACN,kCAAsB,EAAE,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI,QAAQ,CAAC;AAAA,UACvE;AAAA,QACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAsBA,WAAW,SAAU,MAAM,SAAS,UAAU;AAC7C,cAAI,MAAM;AAAA,YACT,MAAM;AAAA,YACN;AAAA,YACA;AAAA,UACD;AACA,YAAE,MAAM,IAAI,mBAAmB,GAAG;AAClC,cAAI,CAAC,IAAI,SAAS;AACjB,kBAAM,IAAI,MAAM,mBAAmB,IAAI,WAAW,mBAAmB;AAAA,UACtE;AACA,cAAI,SAAS,EAAE,SAAS,IAAI,MAAM,IAAI,OAAO;AAC7C,YAAE,MAAM,IAAI,kBAAkB,GAAG;AACjC,iBAAO,MAAM,UAAU,EAAE,KAAK,OAAO,IAAI,MAAM,GAAG,IAAI,QAAQ;AAAA,QAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QA0BA,UAAU,SAAU,MAAM,SAAS;AAClC,cAAI,OAAO,QAAQ;AACnB,cAAI,MAAM;AACT,qBAAS,SAAS,MAAM;AACvB,sBAAQ,KAAK,IAAI,KAAK,KAAK;AAAA,YAC5B;AAEA,mBAAO,QAAQ;AAAA,UAChB;AAEA,cAAI,YAAY,IAAI,WAAW;AAC/B,mBAAS,WAAW,UAAU,MAAM,IAAI;AAExC,uBAAa,MAAM,WAAW,SAAS,UAAU,MAAM,CAAC;AAExD,iBAAO,QAAQ,SAAS;AAAA,QACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,OAAO;AAAA,UACN,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAcN,KAAK,SAAU,MAAM,UAAU;AAC9B,gBAAI,QAAQ,EAAE,MAAM;AAEpB,kBAAM,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;AAE9B,kBAAM,IAAI,EAAE,KAAK,QAAQ;AAAA,UAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAWA,KAAK,SAAU,MAAM,KAAK;AACzB,gBAAI,YAAY,EAAE,MAAM,IAAI,IAAI;AAEhC,gBAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AACpC;AAAA,YACD;AAEA,qBAAS,IAAI,GAAG,UAAW,WAAW,UAAU,GAAG,KAAK;AACvD,uBAAS,GAAG;AAAA,YACb;AAAA,UACD;AAAA,QACD;AAAA,QAEA;AAAA,MACD;AACA,MAAAA,OAAM,QAAQ;AAmBd,eAAS,MAAM,MAAM,SAAS,OAAO,YAAY;AAUhD,aAAK,OAAO;AASZ,aAAK,UAAU;AAQf,aAAK,QAAQ;AAEb,aAAK,UAAU,cAAc,IAAI,SAAS;AAAA,MAC3C;AA8BA,YAAM,YAAY,SAAS,UAAU,GAAG,UAAU;AACjD,YAAI,OAAO,KAAK,UAAU;AACzB,iBAAO;AAAA,QACR;AACA,YAAI,MAAM,QAAQ,CAAC,GAAG;AACrB,cAAI,IAAI;AACR,YAAE,QAAQ,SAAU,GAAG;AACtB,iBAAK,UAAU,GAAG,QAAQ;AAAA,UAC3B,CAAC;AACD,iBAAO;AAAA,QACR;AAEA,YAAI,MAAM;AAAA,UACT,MAAM,EAAE;AAAA,UACR,SAAS,UAAU,EAAE,SAAS,QAAQ;AAAA,UACtC,KAAK;AAAA,UACL,SAAS,CAAC,SAAS,EAAE,IAAI;AAAA,UACzB,YAAY,CAAC;AAAA,UACb;AAAA,QACD;AAEA,YAAI,UAAU,EAAE;AAChB,YAAI,SAAS;AACZ,cAAI,MAAM,QAAQ,OAAO,GAAG;AAC3B,kBAAM,UAAU,KAAK,MAAM,IAAI,SAAS,OAAO;AAAA,UAChD,OAAO;AACN,gBAAI,QAAQ,KAAK,OAAO;AAAA,UACzB;AAAA,QACD;AAEA,UAAE,MAAM,IAAI,QAAQ,GAAG;AAEvB,YAAI,aAAa;AACjB,iBAAS,QAAQ,IAAI,YAAY;AAChC,wBAAc,MAAM,OAAO,QAAQ,IAAI,WAAW,IAAI,KAAK,IAAI,QAAQ,MAAM,QAAQ,IAAI;AAAA,QAC1F;AAEA,eAAO,MAAM,IAAI,MAAM,aAAa,IAAI,QAAQ,KAAK,GAAG,IAAI,MAAM,aAAa,MAAM,IAAI,UAAU,OAAO,IAAI,MAAM;AAAA,MACrH;AASA,eAAS,aAAa,SAAS,KAAK,MAAM,YAAY;AACrD,gBAAQ,YAAY;AACpB,YAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,YAAI,SAAS,cAAc,MAAM,CAAC,GAAG;AAEpC,cAAI,mBAAmB,MAAM,CAAC,EAAE;AAChC,gBAAM,SAAS;AACf,gBAAM,CAAC,IAAI,MAAM,CAAC,EAAE,MAAM,gBAAgB;AAAA,QAC3C;AACA,eAAO;AAAA,MACR;AAgBA,eAAS,aAAa,MAAM,WAAW,SAAS,WAAW,UAAU,SAAS;AAC7E,iBAAS,SAAS,SAAS;AAC1B,cAAI,CAAC,QAAQ,eAAe,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG;AACtD;AAAA,UACD;AAEA,cAAI,WAAW,QAAQ,KAAK;AAC5B,qBAAW,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAEzD,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACzC,gBAAI,WAAW,QAAQ,SAAS,QAAQ,MAAM,GAAG;AAChD;AAAA,YACD;AAEA,gBAAI,aAAa,SAAS,CAAC;AAC3B,gBAAI,SAAS,WAAW;AACxB,gBAAI,aAAa,CAAC,CAAC,WAAW;AAC9B,gBAAI,SAAS,CAAC,CAAC,WAAW;AAC1B,gBAAI,QAAQ,WAAW;AAEvB,gBAAI,UAAU,CAAC,WAAW,QAAQ,QAAQ;AAEzC,kBAAI,QAAQ,WAAW,QAAQ,SAAS,EAAE,MAAM,WAAW,EAAE,CAAC;AAC9D,yBAAW,UAAU,OAAO,WAAW,QAAQ,QAAQ,QAAQ,GAAG;AAAA,YACnE;AAGA,gBAAI,UAAU,WAAW,WAAW;AAEpC,qBACK,cAAc,UAAU,MAAM,MAAM,UACxC,gBAAgB,UAAU,MAC1B,OAAO,YAAY,MAAM,QAAQ,cAAc,YAAY,MAC1D;AAED,kBAAI,WAAW,OAAO,QAAQ,OAAO;AACpC;AAAA,cACD;AAEA,kBAAI,MAAM,YAAY;AAEtB,kBAAI,UAAU,SAAS,KAAK,QAAQ;AAEnC;AAAA,cACD;AAEA,kBAAI,eAAe,OAAO;AACzB;AAAA,cACD;AAEA,kBAAI,cAAc;AAClB,kBAAI;AAEJ,kBAAI,QAAQ;AACX,wBAAQ,aAAa,SAAS,KAAK,MAAM,UAAU;AACnD,oBAAI,CAAC,SAAS,MAAM,SAAS,KAAK,QAAQ;AACzC;AAAA,gBACD;AAEA,oBAAI,OAAO,MAAM;AACjB,oBAAI,KAAK,MAAM,QAAQ,MAAM,CAAC,EAAE;AAChC,oBAAI,IAAI;AAGR,qBAAK,YAAY,MAAM;AACvB,uBAAO,QAAQ,GAAG;AACjB,gCAAc,YAAY;AAC1B,uBAAK,YAAY,MAAM;AAAA,gBACxB;AAEA,qBAAK,YAAY,MAAM;AACvB,sBAAM;AAGN,oBAAI,YAAY,iBAAiB,OAAO;AACvC;AAAA,gBACD;AAGA,yBACK,IAAI,aACR,MAAM,UAAU,SAAS,IAAI,MAAM,OAAO,EAAE,UAAU,WACtD,IAAI,EAAE,MACL;AACD;AACA,uBAAK,EAAE,MAAM;AAAA,gBACd;AACA;AAGA,sBAAM,KAAK,MAAM,KAAK,CAAC;AACvB,sBAAM,SAAS;AAAA,cAChB,OAAO;AACN,wBAAQ,aAAa,SAAS,GAAG,KAAK,UAAU;AAChD,oBAAI,CAAC,OAAO;AACX;AAAA,gBACD;AAAA,cACD;AAGA,kBAAI,OAAO,MAAM;AACjB,kBAAI,WAAW,MAAM,CAAC;AACtB,kBAAI,SAAS,IAAI,MAAM,GAAG,IAAI;AAC9B,kBAAI,QAAQ,IAAI,MAAM,OAAO,SAAS,MAAM;AAE5C,kBAAI,QAAQ,MAAM,IAAI;AACtB,kBAAI,WAAW,QAAQ,QAAQ,OAAO;AACrC,wBAAQ,QAAQ;AAAA,cACjB;AAEA,kBAAI,aAAa,YAAY;AAE7B,kBAAI,QAAQ;AACX,6BAAa,SAAS,WAAW,YAAY,MAAM;AACnD,uBAAO,OAAO;AAAA,cACf;AAEA,0BAAY,WAAW,YAAY,WAAW;AAE9C,kBAAI,UAAU,IAAI,MAAM,OAAO,SAAS,EAAE,SAAS,UAAU,MAAM,IAAI,UAAU,OAAO,QAAQ;AAChG,4BAAc,SAAS,WAAW,YAAY,OAAO;AAErD,kBAAI,OAAO;AACV,yBAAS,WAAW,aAAa,KAAK;AAAA,cACvC;AAEA,kBAAI,cAAc,GAAG;AAKpB,oBAAI,gBAAgB;AAAA,kBACnB,OAAO,QAAQ,MAAM;AAAA,kBACrB;AAAA,gBACD;AACA,6BAAa,MAAM,WAAW,SAAS,YAAY,MAAM,KAAK,aAAa;AAG3E,oBAAI,WAAW,cAAc,QAAQ,QAAQ,OAAO;AACnD,0BAAQ,QAAQ,cAAc;AAAA,gBAC/B;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAeA,eAAS,aAAa;AAErB,YAAI,OAAO,EAAE,OAAO,MAAM,MAAM,MAAM,MAAM,KAAK;AAEjD,YAAI,OAAO,EAAE,OAAO,MAAM,MAAM,MAAM,MAAM,KAAK;AACjD,aAAK,OAAO;AAGZ,aAAK,OAAO;AAEZ,aAAK,OAAO;AACZ,aAAK,SAAS;AAAA,MACf;AAWA,eAAS,SAAS,MAAM,MAAM,OAAO;AAEpC,YAAI,OAAO,KAAK;AAEhB,YAAI,UAAU,EAAE,OAAc,MAAM,MAAM,KAAW;AACrD,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK;AAEL,eAAO;AAAA,MACR;AASA,eAAS,YAAY,MAAM,MAAM,OAAO;AACvC,YAAI,OAAO,KAAK;AAChB,iBAAS,IAAI,GAAG,IAAI,SAAS,SAAS,KAAK,MAAM,KAAK;AACrD,iBAAO,KAAK;AAAA,QACb;AACA,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,UAAU;AAAA,MAChB;AAMA,eAAS,QAAQ,MAAM;AACtB,YAAI,QAAQ,CAAC;AACb,YAAI,OAAO,KAAK,KAAK;AACrB,eAAO,SAAS,KAAK,MAAM;AAC1B,gBAAM,KAAK,KAAK,KAAK;AACrB,iBAAO,KAAK;AAAA,QACb;AACA,eAAO;AAAA,MACR;AAGA,UAAI,CAACA,OAAM,UAAU;AACpB,YAAI,CAACA,OAAM,kBAAkB;AAE5B,iBAAO;AAAA,QACR;AAEA,YAAI,CAAC,EAAE,6BAA6B;AAEnC,UAAAA,OAAM,iBAAiB,WAAW,SAAU,KAAK;AAChD,gBAAI,UAAU,KAAK,MAAM,IAAI,IAAI;AACjC,gBAAIC,QAAO,QAAQ;AACnB,gBAAI,OAAO,QAAQ;AACnB,gBAAI,iBAAiB,QAAQ;AAE7B,YAAAD,OAAM,YAAY,EAAE,UAAU,MAAM,EAAE,UAAUC,KAAI,GAAGA,KAAI,CAAC;AAC5D,gBAAI,gBAAgB;AACnB,cAAAD,OAAM,MAAM;AAAA,YACb;AAAA,UACD,GAAG,KAAK;AAAA,QACT;AAEA,eAAO;AAAA,MACR;AAGA,UAAI,SAAS,EAAE,KAAK,cAAc;AAElC,UAAI,QAAQ;AACX,UAAE,WAAW,OAAO;AAEpB,YAAI,OAAO,aAAa,aAAa,GAAG;AACvC,YAAE,SAAS;AAAA,QACZ;AAAA,MACD;AAEA,eAAS,iCAAiC;AACzC,YAAI,CAAC,EAAE,QAAQ;AACd,YAAE,aAAa;AAAA,QAChB;AAAA,MACD;AAEA,UAAI,CAAC,EAAE,QAAQ;AAOd,YAAI,aAAa,SAAS;AAC1B,YAAI,eAAe,aAAa,eAAe,iBAAiB,UAAU,OAAO,OAAO;AACvF,mBAAS,iBAAiB,oBAAoB,8BAA8B;AAAA,QAC7E,OAAO;AACN,cAAI,OAAO,uBAAuB;AACjC,mBAAO,sBAAsB,8BAA8B;AAAA,UAC5D,OAAO;AACN,mBAAO,WAAW,gCAAgC,EAAE;AAAA,UACrD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IAER,EAAE,KAAK;AAEP,QAAI,OAAO,WAAW,eAAe,OAAO,SAAS;AACpD,aAAO,UAAU;AAAA,IAClB;AAGA,QAAI,OAAO,WAAW,aAAa;AAClC,aAAO,QAAQ;AAAA,IAChB;AAAA;AAAA;;;AC7rCA;AAAA;AAMA,QAAI,MACF,OAAO,eAAe,WAClB,aACA,OAAO,SAAS,WAChB,OACA,OAAO,WAAW,WAClB,SACA,OAAO,WAAW,WAClB,SACA,CAAC;AAEP,QAAI,UAAU,QAAQ;AAEtB,QAAI,QAAQ,EAAC,QAAQ,MAAM,6BAA6B,KAAI;AAI5D,QAAI,IAAI;AACR,QAAI,SAAS;AACb,QAAI,QAAQ;AACZ,QAAI,SAAS;AACb,QAAI,MAAM;AACV,QAAI,QAAQ;AACZ,QAAI,KAAK;AAET,YAAQ;AAER,QAAI,MAAM,CAAC,EAAE;AAGb,aAAS,YAAY;AAAA,IAAC;AAEtB,cAAU,YAAY;AAGtB,QAAI,UAAU,IAAI,UAAU;AAG5B,WAAO,UAAU;AAGjB,YAAQ,YAAY;AACpB,YAAQ,WAAW;AACnB,YAAQ,QAAQ;AAChB,YAAQ,aAAa;AACrB,YAAQ,gBAAgB;AAGxB,aAAS,MAAM;AACf,aAAS,GAAG;AACZ,aAAS,KAAK;AACd,aAAS,EAAE;AAEX,YAAQ,KAAK,SAAS;AACtB,YAAQ,MAAM,YAAY;AAE1B,aAAS,SAAS,SAAS;AACzB,UAAI,OAAO,YAAY,cAAc,CAAC,QAAQ,aAAa;AACzD,cAAM,IAAI,MAAM,6CAA6C,UAAU,GAAG;AAAA,MAC5E;AAGA,UAAI,QAAQ,UAAU,QAAQ,WAAW,MAAM,QAAW;AACxD,gBAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AAEA,aAAS,MAAM,MAAME,QAAO;AAC1B,UAAI,YAAY,QAAQ;AACxB,UAAI,MAAM;AACV,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAIA,QAAO;AACT,cAAM,CAAC;AACP,YAAI,IAAI,IAAIA;AAAA,MACd;AAEA,WAAK,OAAO,KAAK;AACf,eAAO,IAAI,GAAG;AACd,eAAO,OAAO,SAAS,WAAW,CAAC,IAAI,IAAI;AAC3C,iBAAS,KAAK;AACd,gBAAQ;AAER,eAAO,EAAE,QAAQ,QAAQ;AACvB,oBAAU,KAAK,KAAK,CAAC,IAAI,UAAU,GAAG;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAEA,aAAS,UAAU,OAAO,MAAM;AAC9B,UAAI,MAAM,MAAM;AAChB,UAAI;AAEJ,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI,MAAM,yCAAyC,QAAQ,GAAG;AAAA,MACtE;AAGA,UAAI,QAAQ,KAAK,KAAK,IAAI,MAAM,UAAU;AACxC,kBAAU;AACV,eAAO;AAAA,MACT,OAAO;AACL,YAAI,OAAO,SAAS,UAAU;AAC5B,gBAAM,IAAI,MAAM,wCAAwC,OAAO,GAAG;AAAA,QACpE;AAEA,YAAI,IAAI,KAAK,QAAQ,WAAW,IAAI,GAAG;AACrC,oBAAU,QAAQ,UAAU,IAAI;AAAA,QAClC,OAAO;AACL,gBAAM,IAAI,MAAM,wBAAwB,OAAO,qBAAqB;AAAA,QACtE;AAAA,MACF;AAEA,aAAO,IAAI,KAAK,MAAM,OAAO,SAAS,IAAI;AAAA,IAC5C;AAEA,aAAS,WAAW,UAAU;AAC5B,UAAI,OAAO,aAAa,UAAU;AAChC,cAAM,IAAI,MAAM,4CAA4C,WAAW,GAAG;AAAA,MAC5E;AAEA,aAAO,IAAI,KAAK,QAAQ,WAAW,QAAQ;AAAA,IAC7C;AAEA,aAAS,gBAAgB;AACvB,UAAI,YAAY,QAAQ;AACxB,UAAI,OAAO,CAAC;AACZ,UAAI;AAEJ,WAAK,YAAY,WAAW;AAC1B,YACE,IAAI,KAAK,WAAW,QAAQ,KAC5B,OAAO,UAAU,QAAQ,MAAM,UAC/B;AACA,eAAK,KAAK,QAAQ;AAAA,QACpB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,OAAO,UAAU,QAAQ;AAC1C,UAAI;AAEJ,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO,EAAC,MAAM,QAAQ,MAAY;AAAA,MACpC;AAEA,UAAI,QAAQ,KAAK,KAAK,KAAK,MAAM,SAAS;AACxC,eAAO,aAAa,OAAO,QAAQ;AAAA,MACrC;AAEA,YAAM;AAAA,QACJ,MAAM,MAAM;AAAA,QACZ,SAAS,QAAQ,MAAM,UAAU,MAAM,SAAS,UAAU,MAAM;AAAA,QAChE,KAAK;AAAA,QACL,SAAS,CAAC,SAAS,MAAM,IAAI;AAAA,QAC7B,YAAY,CAAC;AAAA,QACb;AAAA,QACA;AAAA,MACF;AAEA,UAAI,MAAM,OAAO;AACf,YAAI,UAAU,IAAI,QAAQ,OAAO,MAAM,KAAK;AAAA,MAC9C;AAEA,cAAQ,MAAM,IAAI,QAAQ,GAAG;AAE7B,aAAO;AAAA,QACL,IAAI,MAAM,MAAM,IAAI,QAAQ,KAAK,GAAG;AAAA,QACpC,WAAW,IAAI,UAAU;AAAA,QACzB,IAAI;AAAA,MACN;AAAA,IACF;AAEA,aAAS,aAAa,QAAQ,UAAU;AACtC,UAAI,SAAS,CAAC;AACd,UAAI,SAAS,OAAO;AACpB,UAAI,QAAQ;AACZ,UAAI;AAEJ,aAAO,EAAE,QAAQ,QAAQ;AACvB,gBAAQ,OAAO,KAAK;AAEpB,YAAI,UAAU,MAAM,UAAU,QAAQ,UAAU,QAAW;AACzD,iBAAO,KAAK,KAAK;AAAA,QACnB;AAAA,MACF;AAEA,cAAQ;AACR,eAAS,OAAO;AAEhB,aAAO,EAAE,QAAQ,QAAQ;AACvB,gBAAQ,OAAO,KAAK;AACpB,eAAO,KAAK,IAAI,QAAQ,MAAM,UAAU,OAAO,UAAU,MAAM;AAAA,MACjE;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,OAAO,QAAQ;AACtB,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,OAAO;AACzB,UAAI;AAEJ,WAAK,OAAO,OAAO;AACjB,cAAM,GAAG,IAAI,OAAO,MAAM,GAAG,CAAC;AAAA,MAChC;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,UAAU;AACjB,UAAI,UAAU,WAAW;AAEzB,UAAI,UAAU,UAAU,IAAI,QAAQ;AAEpC,aAAOC;AAEP,eAASA,WAAU;AAEjB,YAAI,SAAS;AACX,cAAI,QAAQ;AAAA,QACd,OAAO;AACL,iBAAO,IAAI;AAAA,QACb;AAEA,kBAAU;AACV,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA;AAAA;", "names": ["require_html", "require_html", "_self", "lang", "alias", "restore"]}