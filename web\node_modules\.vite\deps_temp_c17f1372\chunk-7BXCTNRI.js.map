{"version": 3, "sources": ["../../highlight.js/lib/languages/haxe.js"], "sourcesContent": ["/*\nLanguage: Haxe\nDescription: Haxe is an open source toolkit based on a modern, high level, strictly typed programming language.\nAuthor: <PERSON> <i<PERSON><PERSON>@gmail.com> (Based on the actionscript.js language file by <PERSON>)\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://haxe.org\n*/\n\nfunction haxe(hljs) {\n\n  const HAXE_BASIC_TYPES = 'Int Float String Bool Dynamic Void Array ';\n\n  return {\n    name: 'Haxe',\n    aliases: ['hx'],\n    keywords: {\n      keyword: 'break case cast catch continue default do dynamic else enum extern ' +\n               'for function here if import in inline never new override package private get set ' +\n               'public return static super switch this throw trace try typedef untyped using var while ' +\n               HAXE_BASIC_TYPES,\n      built_in:\n        'trace this',\n      literal:\n        'true false null _'\n    },\n    contains: [\n      {\n        className: 'string', // interpolate-able strings\n        begin: '\\'',\n        end: '\\'',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          {\n            className: 'subst', // interpolation\n            begin: '\\\\$\\\\{',\n            end: '\\\\}'\n          },\n          {\n            className: 'subst', // interpolation\n            begin: '\\\\$',\n            end: /\\W\\}/\n          }\n        ]\n      },\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta', // compiler meta\n        begin: '@:',\n        end: '$'\n      },\n      {\n        className: 'meta', // compiler conditionals\n        begin: '#',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'if else elseif end error'\n        }\n      },\n      {\n        className: 'type', // function types\n        begin: ':[ \\t]*',\n        end: '[^A-Za-z0-9_ \\t\\\\->]',\n        excludeBegin: true,\n        excludeEnd: true,\n        relevance: 0\n      },\n      {\n        className: 'type', // types\n        begin: ':[ \\t]*',\n        end: '\\\\W',\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'type', // instantiation\n        begin: 'new *',\n        end: '\\\\W',\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'class', // enums\n        beginKeywords: 'enum',\n        end: '\\\\{',\n        contains: [hljs.TITLE_MODE]\n      },\n      {\n        className: 'class', // abstracts\n        beginKeywords: 'abstract',\n        end: '[\\\\{$]',\n        contains: [\n          {\n            className: 'type',\n            begin: '\\\\(',\n            end: '\\\\)',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          {\n            className: 'type',\n            begin: 'from +',\n            end: '\\\\W',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          {\n            className: 'type',\n            begin: 'to +',\n            end: '\\\\W',\n            excludeBegin: true,\n            excludeEnd: true\n          },\n          hljs.TITLE_MODE\n        ],\n        keywords: {\n          keyword: 'abstract from to'\n        }\n      },\n      {\n        className: 'class', // classes\n        begin: '\\\\b(class|interface) +',\n        end: '[\\\\{$]',\n        excludeEnd: true,\n        keywords: 'class interface',\n        contains: [\n          {\n            className: 'keyword',\n            begin: '\\\\b(extends|implements) +',\n            keywords: 'extends implements',\n            contains: [\n              {\n                className: 'type',\n                begin: hljs.IDENT_RE,\n                relevance: 0\n              }\n            ]\n          },\n          hljs.TITLE_MODE\n        ]\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: '\\\\(',\n        excludeEnd: true,\n        illegal: '\\\\S',\n        contains: [hljs.TITLE_MODE]\n      }\n    ],\n    illegal: /<\\//\n  };\n}\n\nmodule.exports = haxe;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,KAAK,MAAM;AAElB,YAAM,mBAAmB;AAEzB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,IAAI;AAAA,QACd,UAAU;AAAA,UACR,SAAS,gPAGA;AAAA,UACT,UACE;AAAA,UACF,SACE;AAAA,QACJ;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,gBACE,WAAW;AAAA;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,cACA;AAAA,gBACE,WAAW;AAAA;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU,CAAC,KAAK,UAAU;AAAA,UAC5B;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,cACd;AAAA,cACA,KAAK;AAAA,YACP;AAAA,YACA,UAAU;AAAA,cACR,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,UAAU;AAAA,gBACV,UAAU;AAAA,kBACR;AAAA,oBACE,WAAW;AAAA,oBACX,OAAO,KAAK;AAAA,oBACZ,WAAW;AAAA,kBACb;AAAA,gBACF;AAAA,cACF;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,UAAU,CAAC,KAAK,UAAU;AAAA,UAC5B;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}