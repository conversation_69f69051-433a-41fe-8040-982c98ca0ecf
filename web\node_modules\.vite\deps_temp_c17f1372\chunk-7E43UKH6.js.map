{"version": 3, "sources": ["../../highlight.js/lib/languages/isbl.js"], "sourcesContent": ["/*\nLanguage: ISBL\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: built-in language DIRECTUM\nCategory: enterprise\n*/\n\nfunction isbl(hljs) {\n  // Определение идентификаторов\n  const UNDERSCORE_IDENT_RE = \"[A-Za-zА-Яа-яёЁ_!][A-Za-zА-Яа-яёЁ_0-9]*\";\n\n  // Определение имен функций\n  const FUNCTION_NAME_IDENT_RE = \"[A-Za-zА-Яа-яёЁ_][A-Za-zА-Яа-яёЁ_0-9]*\";\n\n  // keyword : ключевые слова\n  const KEYWORD =\n    \"and и else иначе endexcept endfinally endforeach конецвсе endif конецесли endwhile конецпока \" +\n    \"except exitfor finally foreach все if если in в not не or или try while пока \";\n\n  // SYSRES Constants\n  const sysres_constants =\n    \"SYSRES_CONST_ACCES_RIGHT_TYPE_EDIT \" +\n    \"SYSRES_CONST_ACCES_RIGHT_TYPE_FULL \" +\n    \"SYSRES_CONST_ACCES_RIGHT_TYPE_VIEW \" +\n    \"SYSRES_CONST_ACCESS_MODE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_NO_ACCESS_VIEW \" +\n    \"SYSRES_CONST_ACCESS_NO_ACCESS_VIEW_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_ADD_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_ADD_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_CHANGE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_CHANGE_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_DELETE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_DELETE_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_EXECUTE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_EXECUTE_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_NO_ACCESS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_NO_ACCESS_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_RATIFY_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_RATIFY_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_VIEW \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_VIEW_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_VIEW_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ACCESS_RIGHTS_VIEW_REQUISITE_YES_CODE \" +\n    \"SYSRES_CONST_ACCESS_TYPE_CHANGE \" +\n    \"SYSRES_CONST_ACCESS_TYPE_CHANGE_CODE \" +\n    \"SYSRES_CONST_ACCESS_TYPE_EXISTS \" +\n    \"SYSRES_CONST_ACCESS_TYPE_EXISTS_CODE \" +\n    \"SYSRES_CONST_ACCESS_TYPE_FULL \" +\n    \"SYSRES_CONST_ACCESS_TYPE_FULL_CODE \" +\n    \"SYSRES_CONST_ACCESS_TYPE_VIEW \" +\n    \"SYSRES_CONST_ACCESS_TYPE_VIEW_CODE \" +\n    \"SYSRES_CONST_ACTION_TYPE_ABORT \" +\n    \"SYSRES_CONST_ACTION_TYPE_ACCEPT \" +\n    \"SYSRES_CONST_ACTION_TYPE_ACCESS_RIGHTS \" +\n    \"SYSRES_CONST_ACTION_TYPE_ADD_ATTACHMENT \" +\n    \"SYSRES_CONST_ACTION_TYPE_CHANGE_CARD \" +\n    \"SYSRES_CONST_ACTION_TYPE_CHANGE_KIND \" +\n    \"SYSRES_CONST_ACTION_TYPE_CHANGE_STORAGE \" +\n    \"SYSRES_CONST_ACTION_TYPE_CONTINUE \" +\n    \"SYSRES_CONST_ACTION_TYPE_COPY \" +\n    \"SYSRES_CONST_ACTION_TYPE_CREATE \" +\n    \"SYSRES_CONST_ACTION_TYPE_CREATE_VERSION \" +\n    \"SYSRES_CONST_ACTION_TYPE_DELETE \" +\n    \"SYSRES_CONST_ACTION_TYPE_DELETE_ATTACHMENT \" +\n    \"SYSRES_CONST_ACTION_TYPE_DELETE_VERSION \" +\n    \"SYSRES_CONST_ACTION_TYPE_DISABLE_DELEGATE_ACCESS_RIGHTS \" +\n    \"SYSRES_CONST_ACTION_TYPE_ENABLE_DELEGATE_ACCESS_RIGHTS \" +\n    \"SYSRES_CONST_ACTION_TYPE_ENCRYPTION_BY_CERTIFICATE \" +\n    \"SYSRES_CONST_ACTION_TYPE_ENCRYPTION_BY_CERTIFICATE_AND_PASSWORD \" +\n    \"SYSRES_CONST_ACTION_TYPE_ENCRYPTION_BY_PASSWORD \" +\n    \"SYSRES_CONST_ACTION_TYPE_EXPORT_WITH_LOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_EXPORT_WITHOUT_LOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_IMPORT_WITH_UNLOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_IMPORT_WITHOUT_UNLOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_LIFE_CYCLE_STAGE \" +\n    \"SYSRES_CONST_ACTION_TYPE_LOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_LOCK_FOR_SERVER \" +\n    \"SYSRES_CONST_ACTION_TYPE_LOCK_MODIFY \" +\n    \"SYSRES_CONST_ACTION_TYPE_MARK_AS_READED \" +\n    \"SYSRES_CONST_ACTION_TYPE_MARK_AS_UNREADED \" +\n    \"SYSRES_CONST_ACTION_TYPE_MODIFY \" +\n    \"SYSRES_CONST_ACTION_TYPE_MODIFY_CARD \" +\n    \"SYSRES_CONST_ACTION_TYPE_MOVE_TO_ARCHIVE \" +\n    \"SYSRES_CONST_ACTION_TYPE_OFF_ENCRYPTION \" +\n    \"SYSRES_CONST_ACTION_TYPE_PASSWORD_CHANGE \" +\n    \"SYSRES_CONST_ACTION_TYPE_PERFORM \" +\n    \"SYSRES_CONST_ACTION_TYPE_RECOVER_FROM_LOCAL_COPY \" +\n    \"SYSRES_CONST_ACTION_TYPE_RESTART \" +\n    \"SYSRES_CONST_ACTION_TYPE_RESTORE_FROM_ARCHIVE \" +\n    \"SYSRES_CONST_ACTION_TYPE_REVISION \" +\n    \"SYSRES_CONST_ACTION_TYPE_SEND_BY_MAIL \" +\n    \"SYSRES_CONST_ACTION_TYPE_SIGN \" +\n    \"SYSRES_CONST_ACTION_TYPE_START \" +\n    \"SYSRES_CONST_ACTION_TYPE_UNLOCK \" +\n    \"SYSRES_CONST_ACTION_TYPE_UNLOCK_FROM_SERVER \" +\n    \"SYSRES_CONST_ACTION_TYPE_VERSION_STATE \" +\n    \"SYSRES_CONST_ACTION_TYPE_VERSION_VISIBILITY \" +\n    \"SYSRES_CONST_ACTION_TYPE_VIEW \" +\n    \"SYSRES_CONST_ACTION_TYPE_VIEW_SHADOW_COPY \" +\n    \"SYSRES_CONST_ACTION_TYPE_WORKFLOW_DESCRIPTION_MODIFY \" +\n    \"SYSRES_CONST_ACTION_TYPE_WRITE_HISTORY \" +\n    \"SYSRES_CONST_ACTIVE_VERSION_STATE_PICK_VALUE \" +\n    \"SYSRES_CONST_ADD_REFERENCE_MODE_NAME \" +\n    \"SYSRES_CONST_ADDITION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ADDITIONAL_PARAMS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ADITIONAL_JOB_END_DATE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_ADITIONAL_JOB_READ_REQUISITE_NAME \" +\n    \"SYSRES_CONST_ADITIONAL_JOB_START_DATE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_ADITIONAL_JOB_STATE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_ADDING_USER_TO_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_ADDING_USER_TO_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_COMP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_COMP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_USER_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_CREATION_USER_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_CREATION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_CREATION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_DELETION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DATABASE_USER_DELETION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_COMP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_COMP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_FROM_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_DELETION_USER_FROM_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_RESTRICTION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_FILTERER_RESTRICTION_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_PRIVILEGE_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_PRIVILEGE_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_RIGHTS_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_GRANTING_RIGHTS_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_MAIN_SERVER_CHANGED_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_MAIN_SERVER_CHANGED_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_PUBLIC_CHANGED_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_IS_PUBLIC_CHANGED_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_RESTRICTION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_FILTERER_RESTRICTION_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_PRIVILEGE_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_PRIVILEGE_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_RIGHTS_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_REMOVING_RIGHTS_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_CREATION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_CREATION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_DELETION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_SERVER_LOGIN_DELETION_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_CATEGORY_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_CATEGORY_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_COMP_TITLE_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_COMP_TITLE_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_FULL_NAME_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_FULL_NAME_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_PARENT_GROUP_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_PARENT_GROUP_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_AUTH_TYPE_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_AUTH_TYPE_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_LOGIN_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_LOGIN_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_STATUS_ACTION \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_UPDATING_USER_STATUS_ACTION_CODE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_USER_PASSWORD_CHANGE \" +\n    \"SYSRES_CONST_ADMINISTRATION_HISTORY_USER_PASSWORD_CHANGE_ACTION \" +\n    \"SYSRES_CONST_ALL_ACCEPT_CONDITION_RUS \" +\n    \"SYSRES_CONST_ALL_USERS_GROUP \" +\n    \"SYSRES_CONST_ALL_USERS_GROUP_NAME \" +\n    \"SYSRES_CONST_ALL_USERS_SERVER_GROUP_NAME \" +\n    \"SYSRES_CONST_ALLOWED_ACCESS_TYPE_CODE \" +\n    \"SYSRES_CONST_ALLOWED_ACCESS_TYPE_NAME \" +\n    \"SYSRES_CONST_APP_VIEWER_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_APPROVING_SIGNATURE_NAME \" +\n    \"SYSRES_CONST_APPROVING_SIGNATURE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ASSISTANT_SUBSTITUE_TYPE \" +\n    \"SYSRES_CONST_ASSISTANT_SUBSTITUE_TYPE_CODE \" +\n    \"SYSRES_CONST_ATTACH_TYPE_COMPONENT_TOKEN \" +\n    \"SYSRES_CONST_ATTACH_TYPE_DOC \" +\n    \"SYSRES_CONST_ATTACH_TYPE_EDOC \" +\n    \"SYSRES_CONST_ATTACH_TYPE_FOLDER \" +\n    \"SYSRES_CONST_ATTACH_TYPE_JOB \" +\n    \"SYSRES_CONST_ATTACH_TYPE_REFERENCE \" +\n    \"SYSRES_CONST_ATTACH_TYPE_TASK \" +\n    \"SYSRES_CONST_AUTH_ENCODED_PASSWORD \" +\n    \"SYSRES_CONST_AUTH_ENCODED_PASSWORD_CODE \" +\n    \"SYSRES_CONST_AUTH_NOVELL \" +\n    \"SYSRES_CONST_AUTH_PASSWORD \" +\n    \"SYSRES_CONST_AUTH_PASSWORD_CODE \" +\n    \"SYSRES_CONST_AUTH_WINDOWS \" +\n    \"SYSRES_CONST_AUTHENTICATING_SIGNATURE_NAME \" +\n    \"SYSRES_CONST_AUTHENTICATING_SIGNATURE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_AUTO_ENUM_METHOD_FLAG \" +\n    \"SYSRES_CONST_AUTO_NUMERATION_CODE \" +\n    \"SYSRES_CONST_AUTO_STRONG_ENUM_METHOD_FLAG \" +\n    \"SYSRES_CONST_AUTOTEXT_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_TEXT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_ALL \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_ALL_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_SIGN \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_SIGN_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_WORK \" +\n    \"SYSRES_CONST_AUTOTEXT_USAGE_WORK_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USE_ANYWHERE_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USE_ON_SIGNING_CODE \" +\n    \"SYSRES_CONST_AUTOTEXT_USE_ON_WORK_CODE \" +\n    \"SYSRES_CONST_BEGIN_DATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_BLACK_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_BLUE_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_BTN_PART \" +\n    \"SYSRES_CONST_CALCULATED_ROLE_TYPE_CODE \" +\n    \"SYSRES_CONST_CALL_TYPE_VARIABLE_BUTTON_VALUE \" +\n    \"SYSRES_CONST_CALL_TYPE_VARIABLE_PROGRAM_VALUE \" +\n    \"SYSRES_CONST_CANCEL_MESSAGE_FUNCTION_RESULT \" +\n    \"SYSRES_CONST_CARD_PART \" +\n    \"SYSRES_CONST_CARD_REFERENCE_MODE_NAME \" +\n    \"SYSRES_CONST_CERTIFICATE_TYPE_REQUISITE_ENCRYPT_VALUE \" +\n    \"SYSRES_CONST_CERTIFICATE_TYPE_REQUISITE_SIGN_AND_ENCRYPT_VALUE \" +\n    \"SYSRES_CONST_CERTIFICATE_TYPE_REQUISITE_SIGN_VALUE \" +\n    \"SYSRES_CONST_CHECK_PARAM_VALUE_DATE_PARAM_TYPE \" +\n    \"SYSRES_CONST_CHECK_PARAM_VALUE_FLOAT_PARAM_TYPE \" +\n    \"SYSRES_CONST_CHECK_PARAM_VALUE_INTEGER_PARAM_TYPE \" +\n    \"SYSRES_CONST_CHECK_PARAM_VALUE_PICK_PARAM_TYPE \" +\n    \"SYSRES_CONST_CHECK_PARAM_VALUE_REEFRENCE_PARAM_TYPE \" +\n    \"SYSRES_CONST_CLOSED_RECORD_FLAG_VALUE_FEMININE \" +\n    \"SYSRES_CONST_CLOSED_RECORD_FLAG_VALUE_MASCULINE \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_ADMIN \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_DEVELOPER \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_DOCS \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_EDOC_CARDS \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_EXTERNAL_EXECUTABLE \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_OTHER \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_REFERENCE \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_REPORT \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_SCRIPT \" +\n    \"SYSRES_CONST_CODE_COMPONENT_TYPE_URL \" +\n    \"SYSRES_CONST_CODE_REQUISITE_ACCESS \" +\n    \"SYSRES_CONST_CODE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_CODE_REQUISITE_COMPONENT \" +\n    \"SYSRES_CONST_CODE_REQUISITE_DESCRIPTION \" +\n    \"SYSRES_CONST_CODE_REQUISITE_EXCLUDE_COMPONENT \" +\n    \"SYSRES_CONST_CODE_REQUISITE_RECORD \" +\n    \"SYSRES_CONST_COMMENT_REQ_CODE \" +\n    \"SYSRES_CONST_COMMON_SETTINGS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_COMP_CODE_GRD \" +\n    \"SYSRES_CONST_COMPONENT_GROUP_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_ADMIN_COMPONENTS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_DEVELOPER_COMPONENTS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_DOCS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_EDOC_CARDS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_EDOCS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_EXTERNAL_EXECUTABLE \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_OTHER \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_REFERENCE_TYPES \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_REFERENCES \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_REPORTS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_SCRIPTS \" +\n    \"SYSRES_CONST_COMPONENT_TYPE_URL \" +\n    \"SYSRES_CONST_COMPONENTS_REMOTE_SERVERS_VIEW_CODE \" +\n    \"SYSRES_CONST_CONDITION_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_CONST_FIRM_STATUS_COMMON \" +\n    \"SYSRES_CONST_CONST_FIRM_STATUS_INDIVIDUAL \" +\n    \"SYSRES_CONST_CONST_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_CONST_POSITIVE_VALUE \" +\n    \"SYSRES_CONST_CONST_SERVER_STATUS_DONT_REPLICATE \" +\n    \"SYSRES_CONST_CONST_SERVER_STATUS_REPLICATE \" +\n    \"SYSRES_CONST_CONTENTS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DATA_TYPE_BOOLEAN \" +\n    \"SYSRES_CONST_DATA_TYPE_DATE \" +\n    \"SYSRES_CONST_DATA_TYPE_FLOAT \" +\n    \"SYSRES_CONST_DATA_TYPE_INTEGER \" +\n    \"SYSRES_CONST_DATA_TYPE_PICK \" +\n    \"SYSRES_CONST_DATA_TYPE_REFERENCE \" +\n    \"SYSRES_CONST_DATA_TYPE_STRING \" +\n    \"SYSRES_CONST_DATA_TYPE_TEXT \" +\n    \"SYSRES_CONST_DATA_TYPE_VARIANT \" +\n    \"SYSRES_CONST_DATE_CLOSE_REQ_CODE \" +\n    \"SYSRES_CONST_DATE_FORMAT_DATE_ONLY_CHAR \" +\n    \"SYSRES_CONST_DATE_OPEN_REQ_CODE \" +\n    \"SYSRES_CONST_DATE_REQUISITE \" +\n    \"SYSRES_CONST_DATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DATE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_DATE_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_DATE_TYPE_CHAR \" +\n    \"SYSRES_CONST_DATETIME_FORMAT_VALUE \" +\n    \"SYSRES_CONST_DEA_ACCESS_RIGHTS_ACTION_CODE \" +\n    \"SYSRES_CONST_DESCRIPTION_LOCALIZE_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DESCRIPTION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DET1_PART \" +\n    \"SYSRES_CONST_DET2_PART \" +\n    \"SYSRES_CONST_DET3_PART \" +\n    \"SYSRES_CONST_DET4_PART \" +\n    \"SYSRES_CONST_DET5_PART \" +\n    \"SYSRES_CONST_DET6_PART \" +\n    \"SYSRES_CONST_DETAIL_DATASET_KEY_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DETAIL_PICK_REQUISITE_CODE \" +\n    \"SYSRES_CONST_DETAIL_REQ_CODE \" +\n    \"SYSRES_CONST_DO_NOT_USE_ACCESS_TYPE_CODE \" +\n    \"SYSRES_CONST_DO_NOT_USE_ACCESS_TYPE_NAME \" +\n    \"SYSRES_CONST_DO_NOT_USE_ON_VIEW_ACCESS_TYPE_CODE \" +\n    \"SYSRES_CONST_DO_NOT_USE_ON_VIEW_ACCESS_TYPE_NAME \" +\n    \"SYSRES_CONST_DOCUMENT_STORAGES_CODE \" +\n    \"SYSRES_CONST_DOCUMENT_TEMPLATES_TYPE_NAME \" +\n    \"SYSRES_CONST_DOUBLE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITOR_CLOSE_FILE_OBSERV_TYPE_CODE \" +\n    \"SYSRES_CONST_EDITOR_CLOSE_PROCESS_OBSERV_TYPE_CODE \" +\n    \"SYSRES_CONST_EDITOR_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_APPLICATION_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_CREATE_SEVERAL_PROCESSES_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_EXTENSION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_OBSERVER_BY_PROCESS_TYPE \" +\n    \"SYSRES_CONST_EDITORS_REFERENCE_CODE \" +\n    \"SYSRES_CONST_EDITORS_REPLACE_SPEC_CHARS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_USE_PLUGINS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDITORS_VIEW_DOCUMENT_OPENED_TO_EDIT_CODE \" +\n    \"SYSRES_CONST_EDOC_CARD_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_CARD_TYPES_LINK_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_CERTIFICATE_AND_PASSWORD_ENCODE_CODE \" +\n    \"SYSRES_CONST_EDOC_CERTIFICATE_ENCODE_CODE \" +\n    \"SYSRES_CONST_EDOC_DATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_KIND_REFERENCE_CODE \" +\n    \"SYSRES_CONST_EDOC_KINDS_BY_TEMPLATE_ACTION_CODE \" +\n    \"SYSRES_CONST_EDOC_MANAGE_ACCESS_CODE \" +\n    \"SYSRES_CONST_EDOC_NONE_ENCODE_CODE \" +\n    \"SYSRES_CONST_EDOC_NUMBER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_PASSWORD_ENCODE_CODE \" +\n    \"SYSRES_CONST_EDOC_READONLY_ACCESS_CODE \" +\n    \"SYSRES_CONST_EDOC_SHELL_LIFE_TYPE_VIEW_VALUE \" +\n    \"SYSRES_CONST_EDOC_SIZE_RESTRICTION_PRIORITY_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_CHECK_ACCESS_RIGHTS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_COMPUTER_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_DATABASE_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_EDIT_IN_STORAGE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_LOCAL_PATH_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_STORAGE_SHARED_SOURCE_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_TEMPLATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EDOC_TYPES_REFERENCE_CODE \" +\n    \"SYSRES_CONST_EDOC_VERSION_ACTIVE_STAGE_CODE \" +\n    \"SYSRES_CONST_EDOC_VERSION_DESIGN_STAGE_CODE \" +\n    \"SYSRES_CONST_EDOC_VERSION_OBSOLETE_STAGE_CODE \" +\n    \"SYSRES_CONST_EDOC_WRITE_ACCES_CODE \" +\n    \"SYSRES_CONST_EDOCUMENT_CARD_REQUISITES_REFERENCE_CODE_SELECTED_REQUISITE \" +\n    \"SYSRES_CONST_ENCODE_CERTIFICATE_TYPE_CODE \" +\n    \"SYSRES_CONST_END_DATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_ENUMERATION_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_EXECUTE_ACCESS_RIGHTS_TYPE_CODE \" +\n    \"SYSRES_CONST_EXECUTIVE_FILE_STORAGE_TYPE \" +\n    \"SYSRES_CONST_EXIST_CONST \" +\n    \"SYSRES_CONST_EXIST_VALUE \" +\n    \"SYSRES_CONST_EXPORT_LOCK_TYPE_ASK \" +\n    \"SYSRES_CONST_EXPORT_LOCK_TYPE_WITH_LOCK \" +\n    \"SYSRES_CONST_EXPORT_LOCK_TYPE_WITHOUT_LOCK \" +\n    \"SYSRES_CONST_EXPORT_VERSION_TYPE_ASK \" +\n    \"SYSRES_CONST_EXPORT_VERSION_TYPE_LAST \" +\n    \"SYSRES_CONST_EXPORT_VERSION_TYPE_LAST_ACTIVE \" +\n    \"SYSRES_CONST_EXTENSION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_FILTER_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_FILTER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_FILTER_TYPE_COMMON_CODE \" +\n    \"SYSRES_CONST_FILTER_TYPE_COMMON_NAME \" +\n    \"SYSRES_CONST_FILTER_TYPE_USER_CODE \" +\n    \"SYSRES_CONST_FILTER_TYPE_USER_NAME \" +\n    \"SYSRES_CONST_FILTER_VALUE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_FLOAT_NUMBER_FORMAT_CHAR \" +\n    \"SYSRES_CONST_FLOAT_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_FOLDER_AUTHOR_VALUE \" +\n    \"SYSRES_CONST_FOLDER_KIND_ANY_OBJECTS \" +\n    \"SYSRES_CONST_FOLDER_KIND_COMPONENTS \" +\n    \"SYSRES_CONST_FOLDER_KIND_EDOCS \" +\n    \"SYSRES_CONST_FOLDER_KIND_JOBS \" +\n    \"SYSRES_CONST_FOLDER_KIND_TASKS \" +\n    \"SYSRES_CONST_FOLDER_TYPE_COMMON \" +\n    \"SYSRES_CONST_FOLDER_TYPE_COMPONENT \" +\n    \"SYSRES_CONST_FOLDER_TYPE_FAVORITES \" +\n    \"SYSRES_CONST_FOLDER_TYPE_INBOX \" +\n    \"SYSRES_CONST_FOLDER_TYPE_OUTBOX \" +\n    \"SYSRES_CONST_FOLDER_TYPE_QUICK_LAUNCH \" +\n    \"SYSRES_CONST_FOLDER_TYPE_SEARCH \" +\n    \"SYSRES_CONST_FOLDER_TYPE_SHORTCUTS \" +\n    \"SYSRES_CONST_FOLDER_TYPE_USER \" +\n    \"SYSRES_CONST_FROM_DICTIONARY_ENUM_METHOD_FLAG \" +\n    \"SYSRES_CONST_FULL_SUBSTITUTE_TYPE \" +\n    \"SYSRES_CONST_FULL_SUBSTITUTE_TYPE_CODE \" +\n    \"SYSRES_CONST_FUNCTION_CANCEL_RESULT \" +\n    \"SYSRES_CONST_FUNCTION_CATEGORY_SYSTEM \" +\n    \"SYSRES_CONST_FUNCTION_CATEGORY_USER \" +\n    \"SYSRES_CONST_FUNCTION_FAILURE_RESULT \" +\n    \"SYSRES_CONST_FUNCTION_SAVE_RESULT \" +\n    \"SYSRES_CONST_GENERATED_REQUISITE \" +\n    \"SYSRES_CONST_GREEN_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_GROUP_ACCOUNT_TYPE_VALUE_CODE \" +\n    \"SYSRES_CONST_GROUP_CATEGORY_NORMAL_CODE \" +\n    \"SYSRES_CONST_GROUP_CATEGORY_NORMAL_NAME \" +\n    \"SYSRES_CONST_GROUP_CATEGORY_SERVICE_CODE \" +\n    \"SYSRES_CONST_GROUP_CATEGORY_SERVICE_NAME \" +\n    \"SYSRES_CONST_GROUP_COMMON_CATEGORY_FIELD_VALUE \" +\n    \"SYSRES_CONST_GROUP_FULL_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUP_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUP_RIGHTS_T_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUP_SERVER_CODES_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUP_SERVER_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUP_SERVICE_CATEGORY_FIELD_VALUE \" +\n    \"SYSRES_CONST_GROUP_USER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_GROUPS_REFERENCE_CODE \" +\n    \"SYSRES_CONST_GROUPS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_HIDDEN_MODE_NAME \" +\n    \"SYSRES_CONST_HIGH_LVL_REQUISITE_CODE \" +\n    \"SYSRES_CONST_HISTORY_ACTION_CREATE_CODE \" +\n    \"SYSRES_CONST_HISTORY_ACTION_DELETE_CODE \" +\n    \"SYSRES_CONST_HISTORY_ACTION_EDIT_CODE \" +\n    \"SYSRES_CONST_HOUR_CHAR \" +\n    \"SYSRES_CONST_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_IDSPS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_IMAGE_MODE_COLOR \" +\n    \"SYSRES_CONST_IMAGE_MODE_GREYSCALE \" +\n    \"SYSRES_CONST_IMAGE_MODE_MONOCHROME \" +\n    \"SYSRES_CONST_IMPORTANCE_HIGH \" +\n    \"SYSRES_CONST_IMPORTANCE_LOW \" +\n    \"SYSRES_CONST_IMPORTANCE_NORMAL \" +\n    \"SYSRES_CONST_IN_DESIGN_VERSION_STATE_PICK_VALUE \" +\n    \"SYSRES_CONST_INCOMING_WORK_RULE_TYPE_CODE \" +\n    \"SYSRES_CONST_INT_REQUISITE \" +\n    \"SYSRES_CONST_INT_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_INTEGER_NUMBER_FORMAT_CHAR \" +\n    \"SYSRES_CONST_INTEGER_TYPE_CHAR \" +\n    \"SYSRES_CONST_IS_GENERATED_REQUISITE_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_IS_PUBLIC_ROLE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_IS_REMOTE_USER_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_IS_REMOTE_USER_POSITIVE_VALUE \" +\n    \"SYSRES_CONST_IS_STORED_REQUISITE_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_IS_STORED_REQUISITE_STORED_VALUE \" +\n    \"SYSRES_CONST_ITALIC_LIFE_CYCLE_STAGE_DRAW_STYLE \" +\n    \"SYSRES_CONST_JOB_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_JOB_KIND_CONTROL_JOB \" +\n    \"SYSRES_CONST_JOB_KIND_JOB \" +\n    \"SYSRES_CONST_JOB_KIND_NOTICE \" +\n    \"SYSRES_CONST_JOB_STATE_ABORTED \" +\n    \"SYSRES_CONST_JOB_STATE_COMPLETE \" +\n    \"SYSRES_CONST_JOB_STATE_WORKING \" +\n    \"SYSRES_CONST_KIND_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KIND_REQUISITE_NAME \" +\n    \"SYSRES_CONST_KINDS_CREATE_SHADOW_COPIES_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_DEFAULT_EDOC_LIFE_STAGE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_ALL_TEPLATES_ALLOWED_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_ALLOW_LIFE_CYCLE_STAGE_CHANGING_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_ALLOW_MULTIPLE_ACTIVE_VERSIONS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_SHARE_ACCES_RIGHTS_BY_DEFAULT_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_TEMPLATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_EDOC_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_KINDS_SIGNERS_REQUISITES_CODE \" +\n    \"SYSRES_CONST_KOD_INPUT_TYPE \" +\n    \"SYSRES_CONST_LAST_UPDATE_DATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_LIFE_CYCLE_START_STAGE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_LILAC_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_COMPONENT \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_DOCUMENT \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_EDOC \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_FOLDER \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_JOB \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_REFERENCE \" +\n    \"SYSRES_CONST_LINK_OBJECT_KIND_TASK \" +\n    \"SYSRES_CONST_LINK_REF_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_LIST_REFERENCE_MODE_NAME \" +\n    \"SYSRES_CONST_LOCALIZATION_DICTIONARY_MAIN_VIEW_CODE \" +\n    \"SYSRES_CONST_MAIN_VIEW_CODE \" +\n    \"SYSRES_CONST_MANUAL_ENUM_METHOD_FLAG \" +\n    \"SYSRES_CONST_MASTER_COMP_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_MASTER_TABLE_REC_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_MAXIMIZED_MODE_NAME \" +\n    \"SYSRES_CONST_ME_VALUE \" +\n    \"SYSRES_CONST_MESSAGE_ATTENTION_CAPTION \" +\n    \"SYSRES_CONST_MESSAGE_CONFIRMATION_CAPTION \" +\n    \"SYSRES_CONST_MESSAGE_ERROR_CAPTION \" +\n    \"SYSRES_CONST_MESSAGE_INFORMATION_CAPTION \" +\n    \"SYSRES_CONST_MINIMIZED_MODE_NAME \" +\n    \"SYSRES_CONST_MINUTE_CHAR \" +\n    \"SYSRES_CONST_MODULE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_MONITORING_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_MONTH_FORMAT_VALUE \" +\n    \"SYSRES_CONST_NAME_LOCALIZE_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NAME_SINGULAR_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NAMEAN_INPUT_TYPE \" +\n    \"SYSRES_CONST_NEGATIVE_PICK_VALUE \" +\n    \"SYSRES_CONST_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_NO \" +\n    \"SYSRES_CONST_NO_PICK_VALUE \" +\n    \"SYSRES_CONST_NO_SIGNATURE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NO_VALUE \" +\n    \"SYSRES_CONST_NONE_ACCESS_RIGHTS_TYPE_CODE \" +\n    \"SYSRES_CONST_NONOPERATING_RECORD_FLAG_VALUE \" +\n    \"SYSRES_CONST_NONOPERATING_RECORD_FLAG_VALUE_MASCULINE \" +\n    \"SYSRES_CONST_NORMAL_ACCESS_RIGHTS_TYPE_CODE \" +\n    \"SYSRES_CONST_NORMAL_LIFE_CYCLE_STAGE_DRAW_STYLE \" +\n    \"SYSRES_CONST_NORMAL_MODE_NAME \" +\n    \"SYSRES_CONST_NOT_ALLOWED_ACCESS_TYPE_CODE \" +\n    \"SYSRES_CONST_NOT_ALLOWED_ACCESS_TYPE_NAME \" +\n    \"SYSRES_CONST_NOTE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NOTICE_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_NUM_REQUISITE \" +\n    \"SYSRES_CONST_NUM_STR_REQUISITE_CODE \" +\n    \"SYSRES_CONST_NUMERATION_AUTO_NOT_STRONG \" +\n    \"SYSRES_CONST_NUMERATION_AUTO_STRONG \" +\n    \"SYSRES_CONST_NUMERATION_FROM_DICTONARY \" +\n    \"SYSRES_CONST_NUMERATION_MANUAL \" +\n    \"SYSRES_CONST_NUMERIC_TYPE_CHAR \" +\n    \"SYSRES_CONST_NUMREQ_REQUISITE_CODE \" +\n    \"SYSRES_CONST_OBSOLETE_VERSION_STATE_PICK_VALUE \" +\n    \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE \" +\n    \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE_CODE \" +\n    \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE_FEMININE \" +\n    \"SYSRES_CONST_OPERATING_RECORD_FLAG_VALUE_MASCULINE \" +\n    \"SYSRES_CONST_OPTIONAL_FORM_COMP_REQCODE_PREFIX \" +\n    \"SYSRES_CONST_ORANGE_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_ORIGINALREF_REQUISITE_CODE \" +\n    \"SYSRES_CONST_OURFIRM_REF_CODE \" +\n    \"SYSRES_CONST_OURFIRM_REQUISITE_CODE \" +\n    \"SYSRES_CONST_OURFIRM_VAR \" +\n    \"SYSRES_CONST_OUTGOING_WORK_RULE_TYPE_CODE \" +\n    \"SYSRES_CONST_PICK_NEGATIVE_RESULT \" +\n    \"SYSRES_CONST_PICK_POSITIVE_RESULT \" +\n    \"SYSRES_CONST_PICK_REQUISITE \" +\n    \"SYSRES_CONST_PICK_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_PICK_TYPE_CHAR \" +\n    \"SYSRES_CONST_PLAN_STATUS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_PLATFORM_VERSION_COMMENT \" +\n    \"SYSRES_CONST_PLUGINS_SETTINGS_DESCRIPTION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_POSITIVE_PICK_VALUE \" +\n    \"SYSRES_CONST_POWER_TO_CREATE_ACTION_CODE \" +\n    \"SYSRES_CONST_POWER_TO_SIGN_ACTION_CODE \" +\n    \"SYSRES_CONST_PRIORITY_REQUISITE_CODE \" +\n    \"SYSRES_CONST_QUALIFIED_TASK_TYPE \" +\n    \"SYSRES_CONST_QUALIFIED_TASK_TYPE_CODE \" +\n    \"SYSRES_CONST_RECSTAT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_RED_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_REF_ID_T_REF_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REF_REQUISITE \" +\n    \"SYSRES_CONST_REF_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_REF_REQUISITES_REFERENCE_CODE_SELECTED_REQUISITE \" +\n    \"SYSRES_CONST_REFERENCE_RECORD_HISTORY_CREATE_ACTION_CODE \" +\n    \"SYSRES_CONST_REFERENCE_RECORD_HISTORY_DELETE_ACTION_CODE \" +\n    \"SYSRES_CONST_REFERENCE_RECORD_HISTORY_MODIFY_ACTION_CODE \" +\n    \"SYSRES_CONST_REFERENCE_TYPE_CHAR \" +\n    \"SYSRES_CONST_REFERENCE_TYPE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_REFERENCES_ADD_PARAMS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REFERENCES_DISPLAY_REQUISITE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REMOTE_SERVER_STATUS_WORKING \" +\n    \"SYSRES_CONST_REMOTE_SERVER_TYPE_MAIN \" +\n    \"SYSRES_CONST_REMOTE_SERVER_TYPE_SECONDARY \" +\n    \"SYSRES_CONST_REMOTE_USER_FLAG_VALUE_CODE \" +\n    \"SYSRES_CONST_REPORT_APP_EDITOR_INTERNAL \" +\n    \"SYSRES_CONST_REPORT_BASE_REPORT_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REPORT_BASE_REPORT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REPORT_SCRIPT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REPORT_TEMPLATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REPORT_VIEWER_CODE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REQ_ALLOW_COMPONENT_DEFAULT_VALUE \" +\n    \"SYSRES_CONST_REQ_ALLOW_RECORD_DEFAULT_VALUE \" +\n    \"SYSRES_CONST_REQ_ALLOW_SERVER_COMPONENT_DEFAULT_VALUE \" +\n    \"SYSRES_CONST_REQ_MODE_AVAILABLE_CODE \" +\n    \"SYSRES_CONST_REQ_MODE_EDIT_CODE \" +\n    \"SYSRES_CONST_REQ_MODE_HIDDEN_CODE \" +\n    \"SYSRES_CONST_REQ_MODE_NOT_AVAILABLE_CODE \" +\n    \"SYSRES_CONST_REQ_MODE_VIEW_CODE \" +\n    \"SYSRES_CONST_REQ_NUMBER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REQ_SECTION_VALUE \" +\n    \"SYSRES_CONST_REQ_TYPE_VALUE \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_BY_UNIT \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_DATE_FULL \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_DATE_TIME \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_LEFT \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_RIGHT \" +\n    \"SYSRES_CONST_REQUISITE_FORMAT_WITHOUT_UNIT \" +\n    \"SYSRES_CONST_REQUISITE_NUMBER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_ACTIONS \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_BUTTON \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_BUTTONS \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_CARD \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE10 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE11 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE12 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE13 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE14 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE15 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE16 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE17 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE18 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE19 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE2 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE20 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE21 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE22 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE23 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE24 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE3 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE4 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE5 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE6 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE7 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE8 \" +\n    \"SYSRES_CONST_REQUISITE_SECTION_TABLE9 \" +\n    \"SYSRES_CONST_REQUISITES_PSEUDOREFERENCE_REQUISITE_NUMBER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_RIGHT_ALIGNMENT_CODE \" +\n    \"SYSRES_CONST_ROLES_REFERENCE_CODE \" +\n    \"SYSRES_CONST_ROUTE_STEP_AFTER_RUS \" +\n    \"SYSRES_CONST_ROUTE_STEP_AND_CONDITION_RUS \" +\n    \"SYSRES_CONST_ROUTE_STEP_OR_CONDITION_RUS \" +\n    \"SYSRES_CONST_ROUTE_TYPE_COMPLEX \" +\n    \"SYSRES_CONST_ROUTE_TYPE_PARALLEL \" +\n    \"SYSRES_CONST_ROUTE_TYPE_SERIAL \" +\n    \"SYSRES_CONST_SBDATASETDESC_NEGATIVE_VALUE \" +\n    \"SYSRES_CONST_SBDATASETDESC_POSITIVE_VALUE \" +\n    \"SYSRES_CONST_SBVIEWSDESC_POSITIVE_VALUE \" +\n    \"SYSRES_CONST_SCRIPT_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_SEARCH_BY_TEXT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_SEARCHES_COMPONENT_CONTENT \" +\n    \"SYSRES_CONST_SEARCHES_CRITERIA_ACTION_NAME \" +\n    \"SYSRES_CONST_SEARCHES_EDOC_CONTENT \" +\n    \"SYSRES_CONST_SEARCHES_FOLDER_CONTENT \" +\n    \"SYSRES_CONST_SEARCHES_JOB_CONTENT \" +\n    \"SYSRES_CONST_SEARCHES_REFERENCE_CODE \" +\n    \"SYSRES_CONST_SEARCHES_TASK_CONTENT \" +\n    \"SYSRES_CONST_SECOND_CHAR \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_ACTIONS_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_CARD_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_1_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_2_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_3_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_4_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_5_VALUE \" +\n    \"SYSRES_CONST_SECTION_REQUISITE_DETAIL_6_VALUE \" +\n    \"SYSRES_CONST_SELECT_REFERENCE_MODE_NAME \" +\n    \"SYSRES_CONST_SELECT_TYPE_SELECTABLE \" +\n    \"SYSRES_CONST_SELECT_TYPE_SELECTABLE_ONLY_CHILD \" +\n    \"SYSRES_CONST_SELECT_TYPE_SELECTABLE_WITH_CHILD \" +\n    \"SYSRES_CONST_SELECT_TYPE_UNSLECTABLE \" +\n    \"SYSRES_CONST_SERVER_TYPE_MAIN \" +\n    \"SYSRES_CONST_SERVICE_USER_CATEGORY_FIELD_VALUE \" +\n    \"SYSRES_CONST_SETTINGS_USER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_SIGNATURE_AND_ENCODE_CERTIFICATE_TYPE_CODE \" +\n    \"SYSRES_CONST_SIGNATURE_CERTIFICATE_TYPE_CODE \" +\n    \"SYSRES_CONST_SINGULAR_TITLE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_SQL_SERVER_AUTHENTIFICATION_FLAG_VALUE_CODE \" +\n    \"SYSRES_CONST_SQL_SERVER_ENCODE_AUTHENTIFICATION_FLAG_VALUE_CODE \" +\n    \"SYSRES_CONST_STANDART_ROUTE_REFERENCE_CODE \" +\n    \"SYSRES_CONST_STANDART_ROUTE_REFERENCE_COMMENT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_STANDART_ROUTES_GROUPS_REFERENCE_CODE \" +\n    \"SYSRES_CONST_STATE_REQ_NAME \" +\n    \"SYSRES_CONST_STATE_REQUISITE_ACTIVE_VALUE \" +\n    \"SYSRES_CONST_STATE_REQUISITE_CLOSED_VALUE \" +\n    \"SYSRES_CONST_STATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_STATIC_ROLE_TYPE_CODE \" +\n    \"SYSRES_CONST_STATUS_PLAN_DEFAULT_VALUE \" +\n    \"SYSRES_CONST_STATUS_VALUE_AUTOCLEANING \" +\n    \"SYSRES_CONST_STATUS_VALUE_BLUE_SQUARE \" +\n    \"SYSRES_CONST_STATUS_VALUE_COMPLETE \" +\n    \"SYSRES_CONST_STATUS_VALUE_GREEN_SQUARE \" +\n    \"SYSRES_CONST_STATUS_VALUE_ORANGE_SQUARE \" +\n    \"SYSRES_CONST_STATUS_VALUE_PURPLE_SQUARE \" +\n    \"SYSRES_CONST_STATUS_VALUE_RED_SQUARE \" +\n    \"SYSRES_CONST_STATUS_VALUE_SUSPEND \" +\n    \"SYSRES_CONST_STATUS_VALUE_YELLOW_SQUARE \" +\n    \"SYSRES_CONST_STDROUTE_SHOW_TO_USERS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_STORAGE_TYPE_FILE \" +\n    \"SYSRES_CONST_STORAGE_TYPE_SQL_SERVER \" +\n    \"SYSRES_CONST_STR_REQUISITE \" +\n    \"SYSRES_CONST_STRIKEOUT_LIFE_CYCLE_STAGE_DRAW_STYLE \" +\n    \"SYSRES_CONST_STRING_FORMAT_LEFT_ALIGN_CHAR \" +\n    \"SYSRES_CONST_STRING_FORMAT_RIGHT_ALIGN_CHAR \" +\n    \"SYSRES_CONST_STRING_REQUISITE_CODE \" +\n    \"SYSRES_CONST_STRING_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_STRING_TYPE_CHAR \" +\n    \"SYSRES_CONST_SUBSTITUTES_PSEUDOREFERENCE_CODE \" +\n    \"SYSRES_CONST_SUBTASK_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_SYSTEM_SETTING_CURRENT_USER_PARAM_VALUE \" +\n    \"SYSRES_CONST_SYSTEM_SETTING_EMPTY_VALUE_PARAM_VALUE \" +\n    \"SYSRES_CONST_SYSTEM_VERSION_COMMENT \" +\n    \"SYSRES_CONST_TASK_ACCESS_TYPE_ALL \" +\n    \"SYSRES_CONST_TASK_ACCESS_TYPE_ALL_MEMBERS \" +\n    \"SYSRES_CONST_TASK_ACCESS_TYPE_MANUAL \" +\n    \"SYSRES_CONST_TASK_ENCODE_TYPE_CERTIFICATION \" +\n    \"SYSRES_CONST_TASK_ENCODE_TYPE_CERTIFICATION_AND_PASSWORD \" +\n    \"SYSRES_CONST_TASK_ENCODE_TYPE_NONE \" +\n    \"SYSRES_CONST_TASK_ENCODE_TYPE_PASSWORD \" +\n    \"SYSRES_CONST_TASK_ROUTE_ALL_CONDITION \" +\n    \"SYSRES_CONST_TASK_ROUTE_AND_CONDITION \" +\n    \"SYSRES_CONST_TASK_ROUTE_OR_CONDITION \" +\n    \"SYSRES_CONST_TASK_STATE_ABORTED \" +\n    \"SYSRES_CONST_TASK_STATE_COMPLETE \" +\n    \"SYSRES_CONST_TASK_STATE_CONTINUED \" +\n    \"SYSRES_CONST_TASK_STATE_CONTROL \" +\n    \"SYSRES_CONST_TASK_STATE_INIT \" +\n    \"SYSRES_CONST_TASK_STATE_WORKING \" +\n    \"SYSRES_CONST_TASK_TITLE \" +\n    \"SYSRES_CONST_TASK_TYPES_GROUPS_REFERENCE_CODE \" +\n    \"SYSRES_CONST_TASK_TYPES_REFERENCE_CODE \" +\n    \"SYSRES_CONST_TEMPLATES_REFERENCE_CODE \" +\n    \"SYSRES_CONST_TEST_DATE_REQUISITE_NAME \" +\n    \"SYSRES_CONST_TEST_DEV_DATABASE_NAME \" +\n    \"SYSRES_CONST_TEST_DEV_SYSTEM_CODE \" +\n    \"SYSRES_CONST_TEST_EDMS_DATABASE_NAME \" +\n    \"SYSRES_CONST_TEST_EDMS_MAIN_CODE \" +\n    \"SYSRES_CONST_TEST_EDMS_MAIN_DB_NAME \" +\n    \"SYSRES_CONST_TEST_EDMS_SECOND_CODE \" +\n    \"SYSRES_CONST_TEST_EDMS_SECOND_DB_NAME \" +\n    \"SYSRES_CONST_TEST_EDMS_SYSTEM_CODE \" +\n    \"SYSRES_CONST_TEST_NUMERIC_REQUISITE_NAME \" +\n    \"SYSRES_CONST_TEXT_REQUISITE \" +\n    \"SYSRES_CONST_TEXT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_TEXT_REQUISITE_TYPE \" +\n    \"SYSRES_CONST_TEXT_TYPE_CHAR \" +\n    \"SYSRES_CONST_TYPE_CODE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_UNDEFINED_LIFE_CYCLE_STAGE_FONT_COLOR \" +\n    \"SYSRES_CONST_UNITS_SECTION_ID_REQUISITE_CODE \" +\n    \"SYSRES_CONST_UNITS_SECTION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_UNOPERATING_RECORD_FLAG_VALUE_CODE \" +\n    \"SYSRES_CONST_UNSTORED_DATA_REQUISITE_CODE \" +\n    \"SYSRES_CONST_UNSTORED_DATA_REQUISITE_NAME \" +\n    \"SYSRES_CONST_USE_ACCESS_TYPE_CODE \" +\n    \"SYSRES_CONST_USE_ACCESS_TYPE_NAME \" +\n    \"SYSRES_CONST_USER_ACCOUNT_TYPE_VALUE_CODE \" +\n    \"SYSRES_CONST_USER_ADDITIONAL_INFORMATION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_AND_GROUP_ID_FROM_PSEUDOREFERENCE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_CATEGORY_NORMAL \" +\n    \"SYSRES_CONST_USER_CERTIFICATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_CERTIFICATE_STATE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_CERTIFICATE_SUBJECT_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_CERTIFICATE_THUMBPRINT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_COMMON_CATEGORY \" +\n    \"SYSRES_CONST_USER_COMMON_CATEGORY_CODE \" +\n    \"SYSRES_CONST_USER_FULL_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_GROUP_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_LOGIN_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_REMOTE_CONTROLLER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_REMOTE_SYSTEM_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_RIGHTS_T_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_SERVER_NAME_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USER_SERVICE_CATEGORY \" +\n    \"SYSRES_CONST_USER_SERVICE_CATEGORY_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_ADMINISTRATOR_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_ADMINISTRATOR_NAME \" +\n    \"SYSRES_CONST_USER_STATUS_DEVELOPER_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_DEVELOPER_NAME \" +\n    \"SYSRES_CONST_USER_STATUS_DISABLED_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_DISABLED_NAME \" +\n    \"SYSRES_CONST_USER_STATUS_SYSTEM_DEVELOPER_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_USER_CODE \" +\n    \"SYSRES_CONST_USER_STATUS_USER_NAME \" +\n    \"SYSRES_CONST_USER_STATUS_USER_NAME_DEPRECATED \" +\n    \"SYSRES_CONST_USER_TYPE_FIELD_VALUE_USER \" +\n    \"SYSRES_CONST_USER_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_CONTROLLER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_IS_MAIN_SERVER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_REFERENCE_CODE \" +\n    \"SYSRES_CONST_USERS_REGISTRATION_CERTIFICATES_ACTION_NAME \" +\n    \"SYSRES_CONST_USERS_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_SYSTEM_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_USER_ACCESS_RIGHTS_TYPR_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_USER_AUTHENTICATION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_USER_COMPONENT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_USER_GROUP_REQUISITE_CODE \" +\n    \"SYSRES_CONST_USERS_VIEW_CERTIFICATES_ACTION_NAME \" +\n    \"SYSRES_CONST_VIEW_DEFAULT_CODE \" +\n    \"SYSRES_CONST_VIEW_DEFAULT_NAME \" +\n    \"SYSRES_CONST_VIEWER_REQUISITE_CODE \" +\n    \"SYSRES_CONST_WAITING_BLOCK_DESCRIPTION \" +\n    \"SYSRES_CONST_WIZARD_FORM_LABEL_TEST_STRING  \" +\n    \"SYSRES_CONST_WIZARD_QUERY_PARAM_HEIGHT_ETALON_STRING \" +\n    \"SYSRES_CONST_WIZARD_REFERENCE_COMMENT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_WORK_RULES_DESCRIPTION_REQUISITE_CODE \" +\n    \"SYSRES_CONST_WORK_TIME_CALENDAR_REFERENCE_CODE \" +\n    \"SYSRES_CONST_WORK_WORKFLOW_HARD_ROUTE_TYPE_VALUE \" +\n    \"SYSRES_CONST_WORK_WORKFLOW_HARD_ROUTE_TYPE_VALUE_CODE \" +\n    \"SYSRES_CONST_WORK_WORKFLOW_HARD_ROUTE_TYPE_VALUE_CODE_RUS \" +\n    \"SYSRES_CONST_WORK_WORKFLOW_SOFT_ROUTE_TYPE_VALUE_CODE_RUS \" +\n    \"SYSRES_CONST_WORKFLOW_ROUTE_TYPR_HARD \" +\n    \"SYSRES_CONST_WORKFLOW_ROUTE_TYPR_SOFT \" +\n    \"SYSRES_CONST_XML_ENCODING \" +\n    \"SYSRES_CONST_XREC_STAT_REQUISITE_CODE \" +\n    \"SYSRES_CONST_XRECID_FIELD_NAME \" +\n    \"SYSRES_CONST_YES \" +\n    \"SYSRES_CONST_YES_NO_2_REQUISITE_CODE \" +\n    \"SYSRES_CONST_YES_NO_REQUISITE_CODE \" +\n    \"SYSRES_CONST_YES_NO_T_REF_TYPE_REQUISITE_CODE \" +\n    \"SYSRES_CONST_YES_PICK_VALUE \" +\n    \"SYSRES_CONST_YES_VALUE \";\n\n  // Base constant\n  const base_constants = \"CR FALSE nil NO_VALUE NULL TAB TRUE YES_VALUE \";\n\n  // Base group name\n  const base_group_name_constants =\n    \"ADMINISTRATORS_GROUP_NAME CUSTOMIZERS_GROUP_NAME DEVELOPERS_GROUP_NAME SERVICE_USERS_GROUP_NAME \";\n\n  // Decision block properties\n  const decision_block_properties_constants =\n    \"DECISION_BLOCK_FIRST_OPERAND_PROPERTY DECISION_BLOCK_NAME_PROPERTY DECISION_BLOCK_OPERATION_PROPERTY \" +\n    \"DECISION_BLOCK_RESULT_TYPE_PROPERTY DECISION_BLOCK_SECOND_OPERAND_PROPERTY \";\n\n  // File extension\n  const file_extension_constants =\n    \"ANY_FILE_EXTENTION COMPRESSED_DOCUMENT_EXTENSION EXTENDED_DOCUMENT_EXTENSION \" +\n    \"SHORT_COMPRESSED_DOCUMENT_EXTENSION SHORT_EXTENDED_DOCUMENT_EXTENSION \";\n\n  // Job block properties\n  const job_block_properties_constants =\n    \"JOB_BLOCK_ABORT_DEADLINE_PROPERTY \" +\n    \"JOB_BLOCK_AFTER_FINISH_EVENT \" +\n    \"JOB_BLOCK_AFTER_QUERY_PARAMETERS_EVENT \" +\n    \"JOB_BLOCK_ATTACHMENT_PROPERTY \" +\n    \"JOB_BLOCK_ATTACHMENTS_RIGHTS_GROUP_PROPERTY \" +\n    \"JOB_BLOCK_ATTACHMENTS_RIGHTS_TYPE_PROPERTY \" +\n    \"JOB_BLOCK_BEFORE_QUERY_PARAMETERS_EVENT \" +\n    \"JOB_BLOCK_BEFORE_START_EVENT \" +\n    \"JOB_BLOCK_CREATED_JOBS_PROPERTY \" +\n    \"JOB_BLOCK_DEADLINE_PROPERTY \" +\n    \"JOB_BLOCK_EXECUTION_RESULTS_PROPERTY \" +\n    \"JOB_BLOCK_IS_PARALLEL_PROPERTY \" +\n    \"JOB_BLOCK_IS_RELATIVE_ABORT_DEADLINE_PROPERTY \" +\n    \"JOB_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" +\n    \"JOB_BLOCK_JOB_TEXT_PROPERTY \" +\n    \"JOB_BLOCK_NAME_PROPERTY \" +\n    \"JOB_BLOCK_NEED_SIGN_ON_PERFORM_PROPERTY \" +\n    \"JOB_BLOCK_PERFORMER_PROPERTY \" +\n    \"JOB_BLOCK_RELATIVE_ABORT_DEADLINE_TYPE_PROPERTY \" +\n    \"JOB_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" +\n    \"JOB_BLOCK_SUBJECT_PROPERTY \";\n\n  // Language code\n  const language_code_constants = \"ENGLISH_LANGUAGE_CODE RUSSIAN_LANGUAGE_CODE \";\n\n  // Launching external applications\n  const launching_external_applications_constants =\n    \"smHidden smMaximized smMinimized smNormal wmNo wmYes \";\n\n  // Link kind\n  const link_kind_constants =\n    \"COMPONENT_TOKEN_LINK_KIND \" +\n    \"DOCUMENT_LINK_KIND \" +\n    \"EDOCUMENT_LINK_KIND \" +\n    \"FOLDER_LINK_KIND \" +\n    \"JOB_LINK_KIND \" +\n    \"REFERENCE_LINK_KIND \" +\n    \"TASK_LINK_KIND \";\n\n  // Lock type\n  const lock_type_constants =\n    \"COMPONENT_TOKEN_LOCK_TYPE EDOCUMENT_VERSION_LOCK_TYPE \";\n\n  // Monitor block properties\n  const monitor_block_properties_constants =\n    \"MONITOR_BLOCK_AFTER_FINISH_EVENT \" +\n    \"MONITOR_BLOCK_BEFORE_START_EVENT \" +\n    \"MONITOR_BLOCK_DEADLINE_PROPERTY \" +\n    \"MONITOR_BLOCK_INTERVAL_PROPERTY \" +\n    \"MONITOR_BLOCK_INTERVAL_TYPE_PROPERTY \" +\n    \"MONITOR_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" +\n    \"MONITOR_BLOCK_NAME_PROPERTY \" +\n    \"MONITOR_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" +\n    \"MONITOR_BLOCK_SEARCH_SCRIPT_PROPERTY \";\n\n  // Notice block properties\n  const notice_block_properties_constants =\n    \"NOTICE_BLOCK_AFTER_FINISH_EVENT \" +\n    \"NOTICE_BLOCK_ATTACHMENT_PROPERTY \" +\n    \"NOTICE_BLOCK_ATTACHMENTS_RIGHTS_GROUP_PROPERTY \" +\n    \"NOTICE_BLOCK_ATTACHMENTS_RIGHTS_TYPE_PROPERTY \" +\n    \"NOTICE_BLOCK_BEFORE_START_EVENT \" +\n    \"NOTICE_BLOCK_CREATED_NOTICES_PROPERTY \" +\n    \"NOTICE_BLOCK_DEADLINE_PROPERTY \" +\n    \"NOTICE_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" +\n    \"NOTICE_BLOCK_NAME_PROPERTY \" +\n    \"NOTICE_BLOCK_NOTICE_TEXT_PROPERTY \" +\n    \"NOTICE_BLOCK_PERFORMER_PROPERTY \" +\n    \"NOTICE_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" +\n    \"NOTICE_BLOCK_SUBJECT_PROPERTY \";\n\n  // Object events\n  const object_events_constants =\n    \"dseAfterCancel \" +\n    \"dseAfterClose \" +\n    \"dseAfterDelete \" +\n    \"dseAfterDeleteOutOfTransaction \" +\n    \"dseAfterInsert \" +\n    \"dseAfterOpen \" +\n    \"dseAfterScroll \" +\n    \"dseAfterUpdate \" +\n    \"dseAfterUpdateOutOfTransaction \" +\n    \"dseBeforeCancel \" +\n    \"dseBeforeClose \" +\n    \"dseBeforeDelete \" +\n    \"dseBeforeDetailUpdate \" +\n    \"dseBeforeInsert \" +\n    \"dseBeforeOpen \" +\n    \"dseBeforeUpdate \" +\n    \"dseOnAnyRequisiteChange \" +\n    \"dseOnCloseRecord \" +\n    \"dseOnDeleteError \" +\n    \"dseOnOpenRecord \" +\n    \"dseOnPrepareUpdate \" +\n    \"dseOnUpdateError \" +\n    \"dseOnUpdateRatifiedRecord \" +\n    \"dseOnValidDelete \" +\n    \"dseOnValidUpdate \" +\n    \"reOnChange \" +\n    \"reOnChangeValues \" +\n    \"SELECTION_BEGIN_ROUTE_EVENT \" +\n    \"SELECTION_END_ROUTE_EVENT \";\n\n  // Object params\n  const object_params_constants =\n    \"CURRENT_PERIOD_IS_REQUIRED \" +\n    \"PREVIOUS_CARD_TYPE_NAME \" +\n    \"SHOW_RECORD_PROPERTIES_FORM \";\n\n  // Other\n  const other_constants =\n    \"ACCESS_RIGHTS_SETTING_DIALOG_CODE \" +\n    \"ADMINISTRATOR_USER_CODE \" +\n    \"ANALYTIC_REPORT_TYPE \" +\n    \"asrtHideLocal \" +\n    \"asrtHideRemote \" +\n    \"CALCULATED_ROLE_TYPE_CODE \" +\n    \"COMPONENTS_REFERENCE_DEVELOPER_VIEW_CODE \" +\n    \"DCTS_TEST_PROTOCOLS_FOLDER_PATH \" +\n    \"E_EDOC_VERSION_ALREADY_APPROVINGLY_SIGNED \" +\n    \"E_EDOC_VERSION_ALREADY_APPROVINGLY_SIGNED_BY_USER \" +\n    \"E_EDOC_VERSION_ALREDY_SIGNED \" +\n    \"E_EDOC_VERSION_ALREDY_SIGNED_BY_USER \" +\n    \"EDOC_TYPES_CODE_REQUISITE_FIELD_NAME \" +\n    \"EDOCUMENTS_ALIAS_NAME \" +\n    \"FILES_FOLDER_PATH \" +\n    \"FILTER_OPERANDS_DELIMITER \" +\n    \"FILTER_OPERATIONS_DELIMITER \" +\n    \"FORMCARD_NAME \" +\n    \"FORMLIST_NAME \" +\n    \"GET_EXTENDED_DOCUMENT_EXTENSION_CREATION_MODE \" +\n    \"GET_EXTENDED_DOCUMENT_EXTENSION_IMPORT_MODE \" +\n    \"INTEGRATED_REPORT_TYPE \" +\n    \"IS_BUILDER_APPLICATION_ROLE \" +\n    \"IS_BUILDER_APPLICATION_ROLE2 \" +\n    \"IS_BUILDER_USERS \" +\n    \"ISBSYSDEV \" +\n    \"LOG_FOLDER_PATH \" +\n    \"mbCancel \" +\n    \"mbNo \" +\n    \"mbNoToAll \" +\n    \"mbOK \" +\n    \"mbYes \" +\n    \"mbYesToAll \" +\n    \"MEMORY_DATASET_DESRIPTIONS_FILENAME \" +\n    \"mrNo \" +\n    \"mrNoToAll \" +\n    \"mrYes \" +\n    \"mrYesToAll \" +\n    \"MULTIPLE_SELECT_DIALOG_CODE \" +\n    \"NONOPERATING_RECORD_FLAG_FEMININE \" +\n    \"NONOPERATING_RECORD_FLAG_MASCULINE \" +\n    \"OPERATING_RECORD_FLAG_FEMININE \" +\n    \"OPERATING_RECORD_FLAG_MASCULINE \" +\n    \"PROFILING_SETTINGS_COMMON_SETTINGS_CODE_VALUE \" +\n    \"PROGRAM_INITIATED_LOOKUP_ACTION \" +\n    \"ratDelete \" +\n    \"ratEdit \" +\n    \"ratInsert \" +\n    \"REPORT_TYPE \" +\n    \"REQUIRED_PICK_VALUES_VARIABLE \" +\n    \"rmCard \" +\n    \"rmList \" +\n    \"SBRTE_PROGID_DEV \" +\n    \"SBRTE_PROGID_RELEASE \" +\n    \"STATIC_ROLE_TYPE_CODE \" +\n    \"SUPPRESS_EMPTY_TEMPLATE_CREATION \" +\n    \"SYSTEM_USER_CODE \" +\n    \"UPDATE_DIALOG_DATASET \" +\n    \"USED_IN_OBJECT_HINT_PARAM \" +\n    \"USER_INITIATED_LOOKUP_ACTION \" +\n    \"USER_NAME_FORMAT \" +\n    \"USER_SELECTION_RESTRICTIONS \" +\n    \"WORKFLOW_TEST_PROTOCOLS_FOLDER_PATH \" +\n    \"ELS_SUBTYPE_CONTROL_NAME \" +\n    \"ELS_FOLDER_KIND_CONTROL_NAME \" +\n    \"REPEAT_PROCESS_CURRENT_OBJECT_EXCEPTION_NAME \";\n\n  // Privileges\n  const privileges_constants =\n    \"PRIVILEGE_COMPONENT_FULL_ACCESS \" +\n    \"PRIVILEGE_DEVELOPMENT_EXPORT \" +\n    \"PRIVILEGE_DEVELOPMENT_IMPORT \" +\n    \"PRIVILEGE_DOCUMENT_DELETE \" +\n    \"PRIVILEGE_ESD \" +\n    \"PRIVILEGE_FOLDER_DELETE \" +\n    \"PRIVILEGE_MANAGE_ACCESS_RIGHTS \" +\n    \"PRIVILEGE_MANAGE_REPLICATION \" +\n    \"PRIVILEGE_MANAGE_SESSION_SERVER \" +\n    \"PRIVILEGE_OBJECT_FULL_ACCESS \" +\n    \"PRIVILEGE_OBJECT_VIEW \" +\n    \"PRIVILEGE_RESERVE_LICENSE \" +\n    \"PRIVILEGE_SYSTEM_CUSTOMIZE \" +\n    \"PRIVILEGE_SYSTEM_DEVELOP \" +\n    \"PRIVILEGE_SYSTEM_INSTALL \" +\n    \"PRIVILEGE_TASK_DELETE \" +\n    \"PRIVILEGE_USER_PLUGIN_SETTINGS_CUSTOMIZE \" +\n    \"PRIVILEGES_PSEUDOREFERENCE_CODE \";\n\n  // Pseudoreference code\n  const pseudoreference_code_constants =\n    \"ACCESS_TYPES_PSEUDOREFERENCE_CODE \" +\n    \"ALL_AVAILABLE_COMPONENTS_PSEUDOREFERENCE_CODE \" +\n    \"ALL_AVAILABLE_PRIVILEGES_PSEUDOREFERENCE_CODE \" +\n    \"ALL_REPLICATE_COMPONENTS_PSEUDOREFERENCE_CODE \" +\n    \"AVAILABLE_DEVELOPERS_COMPONENTS_PSEUDOREFERENCE_CODE \" +\n    \"COMPONENTS_PSEUDOREFERENCE_CODE \" +\n    \"FILTRATER_SETTINGS_CONFLICTS_PSEUDOREFERENCE_CODE \" +\n    \"GROUPS_PSEUDOREFERENCE_CODE \" +\n    \"RECEIVE_PROTOCOL_PSEUDOREFERENCE_CODE \" +\n    \"REFERENCE_REQUISITE_PSEUDOREFERENCE_CODE \" +\n    \"REFERENCE_REQUISITES_PSEUDOREFERENCE_CODE \" +\n    \"REFTYPES_PSEUDOREFERENCE_CODE \" +\n    \"REPLICATION_SEANCES_DIARY_PSEUDOREFERENCE_CODE \" +\n    \"SEND_PROTOCOL_PSEUDOREFERENCE_CODE \" +\n    \"SUBSTITUTES_PSEUDOREFERENCE_CODE \" +\n    \"SYSTEM_SETTINGS_PSEUDOREFERENCE_CODE \" +\n    \"UNITS_PSEUDOREFERENCE_CODE \" +\n    \"USERS_PSEUDOREFERENCE_CODE \" +\n    \"VIEWERS_PSEUDOREFERENCE_CODE \";\n\n  // Requisite ISBCertificateType values\n  const requisite_ISBCertificateType_values_constants =\n    \"CERTIFICATE_TYPE_ENCRYPT \" +\n    \"CERTIFICATE_TYPE_SIGN \" +\n    \"CERTIFICATE_TYPE_SIGN_AND_ENCRYPT \";\n\n  // Requisite ISBEDocStorageType values\n  const requisite_ISBEDocStorageType_values_constants =\n    \"STORAGE_TYPE_FILE \" +\n    \"STORAGE_TYPE_NAS_CIFS \" +\n    \"STORAGE_TYPE_SAPERION \" +\n    \"STORAGE_TYPE_SQL_SERVER \";\n\n  // Requisite CompType2 values\n  const requisite_compType2_values_constants =\n    \"COMPTYPE2_REQUISITE_DOCUMENTS_VALUE \" +\n    \"COMPTYPE2_REQUISITE_TASKS_VALUE \" +\n    \"COMPTYPE2_REQUISITE_FOLDERS_VALUE \" +\n    \"COMPTYPE2_REQUISITE_REFERENCES_VALUE \";\n\n  // Requisite name\n  const requisite_name_constants =\n    \"SYSREQ_CODE \" +\n    \"SYSREQ_COMPTYPE2 \" +\n    \"SYSREQ_CONST_AVAILABLE_FOR_WEB \" +\n    \"SYSREQ_CONST_COMMON_CODE \" +\n    \"SYSREQ_CONST_COMMON_VALUE \" +\n    \"SYSREQ_CONST_FIRM_CODE \" +\n    \"SYSREQ_CONST_FIRM_STATUS \" +\n    \"SYSREQ_CONST_FIRM_VALUE \" +\n    \"SYSREQ_CONST_SERVER_STATUS \" +\n    \"SYSREQ_CONTENTS \" +\n    \"SYSREQ_DATE_OPEN \" +\n    \"SYSREQ_DATE_CLOSE \" +\n    \"SYSREQ_DESCRIPTION \" +\n    \"SYSREQ_DESCRIPTION_LOCALIZE_ID \" +\n    \"SYSREQ_DOUBLE \" +\n    \"SYSREQ_EDOC_ACCESS_TYPE \" +\n    \"SYSREQ_EDOC_AUTHOR \" +\n    \"SYSREQ_EDOC_CREATED \" +\n    \"SYSREQ_EDOC_DELEGATE_RIGHTS_REQUISITE_CODE \" +\n    \"SYSREQ_EDOC_EDITOR \" +\n    \"SYSREQ_EDOC_ENCODE_TYPE \" +\n    \"SYSREQ_EDOC_ENCRYPTION_PLUGIN_NAME \" +\n    \"SYSREQ_EDOC_ENCRYPTION_PLUGIN_VERSION \" +\n    \"SYSREQ_EDOC_EXPORT_DATE \" +\n    \"SYSREQ_EDOC_EXPORTER \" +\n    \"SYSREQ_EDOC_KIND \" +\n    \"SYSREQ_EDOC_LIFE_STAGE_NAME \" +\n    \"SYSREQ_EDOC_LOCKED_FOR_SERVER_CODE \" +\n    \"SYSREQ_EDOC_MODIFIED \" +\n    \"SYSREQ_EDOC_NAME \" +\n    \"SYSREQ_EDOC_NOTE \" +\n    \"SYSREQ_EDOC_QUALIFIED_ID \" +\n    \"SYSREQ_EDOC_SESSION_KEY \" +\n    \"SYSREQ_EDOC_SESSION_KEY_ENCRYPTION_PLUGIN_NAME \" +\n    \"SYSREQ_EDOC_SESSION_KEY_ENCRYPTION_PLUGIN_VERSION \" +\n    \"SYSREQ_EDOC_SIGNATURE_TYPE \" +\n    \"SYSREQ_EDOC_SIGNED \" +\n    \"SYSREQ_EDOC_STORAGE \" +\n    \"SYSREQ_EDOC_STORAGES_ARCHIVE_STORAGE \" +\n    \"SYSREQ_EDOC_STORAGES_CHECK_RIGHTS \" +\n    \"SYSREQ_EDOC_STORAGES_COMPUTER_NAME \" +\n    \"SYSREQ_EDOC_STORAGES_EDIT_IN_STORAGE \" +\n    \"SYSREQ_EDOC_STORAGES_EXECUTIVE_STORAGE \" +\n    \"SYSREQ_EDOC_STORAGES_FUNCTION \" +\n    \"SYSREQ_EDOC_STORAGES_INITIALIZED \" +\n    \"SYSREQ_EDOC_STORAGES_LOCAL_PATH \" +\n    \"SYSREQ_EDOC_STORAGES_SAPERION_DATABASE_NAME \" +\n    \"SYSREQ_EDOC_STORAGES_SEARCH_BY_TEXT \" +\n    \"SYSREQ_EDOC_STORAGES_SERVER_NAME \" +\n    \"SYSREQ_EDOC_STORAGES_SHARED_SOURCE_NAME \" +\n    \"SYSREQ_EDOC_STORAGES_TYPE \" +\n    \"SYSREQ_EDOC_TEXT_MODIFIED \" +\n    \"SYSREQ_EDOC_TYPE_ACT_CODE \" +\n    \"SYSREQ_EDOC_TYPE_ACT_DESCRIPTION \" +\n    \"SYSREQ_EDOC_TYPE_ACT_DESCRIPTION_LOCALIZE_ID \" +\n    \"SYSREQ_EDOC_TYPE_ACT_ON_EXECUTE \" +\n    \"SYSREQ_EDOC_TYPE_ACT_ON_EXECUTE_EXISTS \" +\n    \"SYSREQ_EDOC_TYPE_ACT_SECTION \" +\n    \"SYSREQ_EDOC_TYPE_ADD_PARAMS \" +\n    \"SYSREQ_EDOC_TYPE_COMMENT \" +\n    \"SYSREQ_EDOC_TYPE_EVENT_TEXT \" +\n    \"SYSREQ_EDOC_TYPE_NAME_IN_SINGULAR \" +\n    \"SYSREQ_EDOC_TYPE_NAME_IN_SINGULAR_LOCALIZE_ID \" +\n    \"SYSREQ_EDOC_TYPE_NAME_LOCALIZE_ID \" +\n    \"SYSREQ_EDOC_TYPE_NUMERATION_METHOD \" +\n    \"SYSREQ_EDOC_TYPE_PSEUDO_REQUISITE_CODE \" +\n    \"SYSREQ_EDOC_TYPE_REQ_CODE \" +\n    \"SYSREQ_EDOC_TYPE_REQ_DESCRIPTION \" +\n    \"SYSREQ_EDOC_TYPE_REQ_DESCRIPTION_LOCALIZE_ID \" +\n    \"SYSREQ_EDOC_TYPE_REQ_IS_LEADING \" +\n    \"SYSREQ_EDOC_TYPE_REQ_IS_REQUIRED \" +\n    \"SYSREQ_EDOC_TYPE_REQ_NUMBER \" +\n    \"SYSREQ_EDOC_TYPE_REQ_ON_CHANGE \" +\n    \"SYSREQ_EDOC_TYPE_REQ_ON_CHANGE_EXISTS \" +\n    \"SYSREQ_EDOC_TYPE_REQ_ON_SELECT \" +\n    \"SYSREQ_EDOC_TYPE_REQ_ON_SELECT_KIND \" +\n    \"SYSREQ_EDOC_TYPE_REQ_SECTION \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_CARD \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_CODE \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_COMMENT \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_IS_MAIN \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_NAME \" +\n    \"SYSREQ_EDOC_TYPE_VIEW_NAME_LOCALIZE_ID \" +\n    \"SYSREQ_EDOC_VERSION_AUTHOR \" +\n    \"SYSREQ_EDOC_VERSION_CRC \" +\n    \"SYSREQ_EDOC_VERSION_DATA \" +\n    \"SYSREQ_EDOC_VERSION_EDITOR \" +\n    \"SYSREQ_EDOC_VERSION_EXPORT_DATE \" +\n    \"SYSREQ_EDOC_VERSION_EXPORTER \" +\n    \"SYSREQ_EDOC_VERSION_HIDDEN \" +\n    \"SYSREQ_EDOC_VERSION_LIFE_STAGE \" +\n    \"SYSREQ_EDOC_VERSION_MODIFIED \" +\n    \"SYSREQ_EDOC_VERSION_NOTE \" +\n    \"SYSREQ_EDOC_VERSION_SIGNATURE_TYPE \" +\n    \"SYSREQ_EDOC_VERSION_SIGNED \" +\n    \"SYSREQ_EDOC_VERSION_SIZE \" +\n    \"SYSREQ_EDOC_VERSION_SOURCE \" +\n    \"SYSREQ_EDOC_VERSION_TEXT_MODIFIED \" +\n    \"SYSREQ_EDOCKIND_DEFAULT_VERSION_STATE_CODE \" +\n    \"SYSREQ_FOLDER_KIND \" +\n    \"SYSREQ_FUNC_CATEGORY \" +\n    \"SYSREQ_FUNC_COMMENT \" +\n    \"SYSREQ_FUNC_GROUP \" +\n    \"SYSREQ_FUNC_GROUP_COMMENT \" +\n    \"SYSREQ_FUNC_GROUP_NUMBER \" +\n    \"SYSREQ_FUNC_HELP \" +\n    \"SYSREQ_FUNC_PARAM_DEF_VALUE \" +\n    \"SYSREQ_FUNC_PARAM_IDENT \" +\n    \"SYSREQ_FUNC_PARAM_NUMBER \" +\n    \"SYSREQ_FUNC_PARAM_TYPE \" +\n    \"SYSREQ_FUNC_TEXT \" +\n    \"SYSREQ_GROUP_CATEGORY \" +\n    \"SYSREQ_ID \" +\n    \"SYSREQ_LAST_UPDATE \" +\n    \"SYSREQ_LEADER_REFERENCE \" +\n    \"SYSREQ_LINE_NUMBER \" +\n    \"SYSREQ_MAIN_RECORD_ID \" +\n    \"SYSREQ_NAME \" +\n    \"SYSREQ_NAME_LOCALIZE_ID \" +\n    \"SYSREQ_NOTE \" +\n    \"SYSREQ_ORIGINAL_RECORD \" +\n    \"SYSREQ_OUR_FIRM \" +\n    \"SYSREQ_PROFILING_SETTINGS_BATCH_LOGING \" +\n    \"SYSREQ_PROFILING_SETTINGS_BATCH_SIZE \" +\n    \"SYSREQ_PROFILING_SETTINGS_PROFILING_ENABLED \" +\n    \"SYSREQ_PROFILING_SETTINGS_SQL_PROFILING_ENABLED \" +\n    \"SYSREQ_PROFILING_SETTINGS_START_LOGGED \" +\n    \"SYSREQ_RECORD_STATUS \" +\n    \"SYSREQ_REF_REQ_FIELD_NAME \" +\n    \"SYSREQ_REF_REQ_FORMAT \" +\n    \"SYSREQ_REF_REQ_GENERATED \" +\n    \"SYSREQ_REF_REQ_LENGTH \" +\n    \"SYSREQ_REF_REQ_PRECISION \" +\n    \"SYSREQ_REF_REQ_REFERENCE \" +\n    \"SYSREQ_REF_REQ_SECTION \" +\n    \"SYSREQ_REF_REQ_STORED \" +\n    \"SYSREQ_REF_REQ_TOKENS \" +\n    \"SYSREQ_REF_REQ_TYPE \" +\n    \"SYSREQ_REF_REQ_VIEW \" +\n    \"SYSREQ_REF_TYPE_ACT_CODE \" +\n    \"SYSREQ_REF_TYPE_ACT_DESCRIPTION \" +\n    \"SYSREQ_REF_TYPE_ACT_DESCRIPTION_LOCALIZE_ID \" +\n    \"SYSREQ_REF_TYPE_ACT_ON_EXECUTE \" +\n    \"SYSREQ_REF_TYPE_ACT_ON_EXECUTE_EXISTS \" +\n    \"SYSREQ_REF_TYPE_ACT_SECTION \" +\n    \"SYSREQ_REF_TYPE_ADD_PARAMS \" +\n    \"SYSREQ_REF_TYPE_COMMENT \" +\n    \"SYSREQ_REF_TYPE_COMMON_SETTINGS \" +\n    \"SYSREQ_REF_TYPE_DISPLAY_REQUISITE_NAME \" +\n    \"SYSREQ_REF_TYPE_EVENT_TEXT \" +\n    \"SYSREQ_REF_TYPE_MAIN_LEADING_REF \" +\n    \"SYSREQ_REF_TYPE_NAME_IN_SINGULAR \" +\n    \"SYSREQ_REF_TYPE_NAME_IN_SINGULAR_LOCALIZE_ID \" +\n    \"SYSREQ_REF_TYPE_NAME_LOCALIZE_ID \" +\n    \"SYSREQ_REF_TYPE_NUMERATION_METHOD \" +\n    \"SYSREQ_REF_TYPE_REQ_CODE \" +\n    \"SYSREQ_REF_TYPE_REQ_DESCRIPTION \" +\n    \"SYSREQ_REF_TYPE_REQ_DESCRIPTION_LOCALIZE_ID \" +\n    \"SYSREQ_REF_TYPE_REQ_IS_CONTROL \" +\n    \"SYSREQ_REF_TYPE_REQ_IS_FILTER \" +\n    \"SYSREQ_REF_TYPE_REQ_IS_LEADING \" +\n    \"SYSREQ_REF_TYPE_REQ_IS_REQUIRED \" +\n    \"SYSREQ_REF_TYPE_REQ_NUMBER \" +\n    \"SYSREQ_REF_TYPE_REQ_ON_CHANGE \" +\n    \"SYSREQ_REF_TYPE_REQ_ON_CHANGE_EXISTS \" +\n    \"SYSREQ_REF_TYPE_REQ_ON_SELECT \" +\n    \"SYSREQ_REF_TYPE_REQ_ON_SELECT_KIND \" +\n    \"SYSREQ_REF_TYPE_REQ_SECTION \" +\n    \"SYSREQ_REF_TYPE_VIEW_CARD \" +\n    \"SYSREQ_REF_TYPE_VIEW_CODE \" +\n    \"SYSREQ_REF_TYPE_VIEW_COMMENT \" +\n    \"SYSREQ_REF_TYPE_VIEW_IS_MAIN \" +\n    \"SYSREQ_REF_TYPE_VIEW_NAME \" +\n    \"SYSREQ_REF_TYPE_VIEW_NAME_LOCALIZE_ID \" +\n    \"SYSREQ_REFERENCE_TYPE_ID \" +\n    \"SYSREQ_STATE \" +\n    \"SYSREQ_STATЕ \" +\n    \"SYSREQ_SYSTEM_SETTINGS_VALUE \" +\n    \"SYSREQ_TYPE \" +\n    \"SYSREQ_UNIT \" +\n    \"SYSREQ_UNIT_ID \" +\n    \"SYSREQ_USER_GROUPS_GROUP_FULL_NAME \" +\n    \"SYSREQ_USER_GROUPS_GROUP_NAME \" +\n    \"SYSREQ_USER_GROUPS_GROUP_SERVER_NAME \" +\n    \"SYSREQ_USERS_ACCESS_RIGHTS \" +\n    \"SYSREQ_USERS_AUTHENTICATION \" +\n    \"SYSREQ_USERS_CATEGORY \" +\n    \"SYSREQ_USERS_COMPONENT \" +\n    \"SYSREQ_USERS_COMPONENT_USER_IS_PUBLIC \" +\n    \"SYSREQ_USERS_DOMAIN \" +\n    \"SYSREQ_USERS_FULL_USER_NAME \" +\n    \"SYSREQ_USERS_GROUP \" +\n    \"SYSREQ_USERS_IS_MAIN_SERVER \" +\n    \"SYSREQ_USERS_LOGIN \" +\n    \"SYSREQ_USERS_REFERENCE_USER_IS_PUBLIC \" +\n    \"SYSREQ_USERS_STATUS \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_INFO \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_PLUGIN_NAME \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_PLUGIN_VERSION \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_STATE \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_SUBJECT_NAME \" +\n    \"SYSREQ_USERS_USER_CERTIFICATE_THUMBPRINT \" +\n    \"SYSREQ_USERS_USER_DEFAULT_CERTIFICATE \" +\n    \"SYSREQ_USERS_USER_DESCRIPTION \" +\n    \"SYSREQ_USERS_USER_GLOBAL_NAME \" +\n    \"SYSREQ_USERS_USER_LOGIN \" +\n    \"SYSREQ_USERS_USER_MAIN_SERVER \" +\n    \"SYSREQ_USERS_USER_TYPE \" +\n    \"SYSREQ_WORK_RULES_FOLDER_ID \";\n\n  // Result\n  const result_constants = \"RESULT_VAR_NAME RESULT_VAR_NAME_ENG \";\n\n  // Rule identification\n  const rule_identification_constants =\n    \"AUTO_NUMERATION_RULE_ID \" +\n    \"CANT_CHANGE_ID_REQUISITE_RULE_ID \" +\n    \"CANT_CHANGE_OURFIRM_REQUISITE_RULE_ID \" +\n    \"CHECK_CHANGING_REFERENCE_RECORD_USE_RULE_ID \" +\n    \"CHECK_CODE_REQUISITE_RULE_ID \" +\n    \"CHECK_DELETING_REFERENCE_RECORD_USE_RULE_ID \" +\n    \"CHECK_FILTRATER_CHANGES_RULE_ID \" +\n    \"CHECK_RECORD_INTERVAL_RULE_ID \" +\n    \"CHECK_REFERENCE_INTERVAL_RULE_ID \" +\n    \"CHECK_REQUIRED_DATA_FULLNESS_RULE_ID \" +\n    \"CHECK_REQUIRED_REQUISITES_FULLNESS_RULE_ID \" +\n    \"MAKE_RECORD_UNRATIFIED_RULE_ID \" +\n    \"RESTORE_AUTO_NUMERATION_RULE_ID \" +\n    \"SET_FIRM_CONTEXT_FROM_RECORD_RULE_ID \" +\n    \"SET_FIRST_RECORD_IN_LIST_FORM_RULE_ID \" +\n    \"SET_IDSPS_VALUE_RULE_ID \" +\n    \"SET_NEXT_CODE_VALUE_RULE_ID \" +\n    \"SET_OURFIRM_BOUNDS_RULE_ID \" +\n    \"SET_OURFIRM_REQUISITE_RULE_ID \";\n\n  // Script block properties\n  const script_block_properties_constants =\n    \"SCRIPT_BLOCK_AFTER_FINISH_EVENT \" +\n    \"SCRIPT_BLOCK_BEFORE_START_EVENT \" +\n    \"SCRIPT_BLOCK_EXECUTION_RESULTS_PROPERTY \" +\n    \"SCRIPT_BLOCK_NAME_PROPERTY \" +\n    \"SCRIPT_BLOCK_SCRIPT_PROPERTY \";\n\n  // Subtask block properties\n  const subtask_block_properties_constants =\n    \"SUBTASK_BLOCK_ABORT_DEADLINE_PROPERTY \" +\n    \"SUBTASK_BLOCK_AFTER_FINISH_EVENT \" +\n    \"SUBTASK_BLOCK_ASSIGN_PARAMS_EVENT \" +\n    \"SUBTASK_BLOCK_ATTACHMENTS_PROPERTY \" +\n    \"SUBTASK_BLOCK_ATTACHMENTS_RIGHTS_GROUP_PROPERTY \" +\n    \"SUBTASK_BLOCK_ATTACHMENTS_RIGHTS_TYPE_PROPERTY \" +\n    \"SUBTASK_BLOCK_BEFORE_START_EVENT \" +\n    \"SUBTASK_BLOCK_CREATED_TASK_PROPERTY \" +\n    \"SUBTASK_BLOCK_CREATION_EVENT \" +\n    \"SUBTASK_BLOCK_DEADLINE_PROPERTY \" +\n    \"SUBTASK_BLOCK_IMPORTANCE_PROPERTY \" +\n    \"SUBTASK_BLOCK_INITIATOR_PROPERTY \" +\n    \"SUBTASK_BLOCK_IS_RELATIVE_ABORT_DEADLINE_PROPERTY \" +\n    \"SUBTASK_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" +\n    \"SUBTASK_BLOCK_JOBS_TYPE_PROPERTY \" +\n    \"SUBTASK_BLOCK_NAME_PROPERTY \" +\n    \"SUBTASK_BLOCK_PARALLEL_ROUTE_PROPERTY \" +\n    \"SUBTASK_BLOCK_PERFORMERS_PROPERTY \" +\n    \"SUBTASK_BLOCK_RELATIVE_ABORT_DEADLINE_TYPE_PROPERTY \" +\n    \"SUBTASK_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \" +\n    \"SUBTASK_BLOCK_REQUIRE_SIGN_PROPERTY \" +\n    \"SUBTASK_BLOCK_STANDARD_ROUTE_PROPERTY \" +\n    \"SUBTASK_BLOCK_START_EVENT \" +\n    \"SUBTASK_BLOCK_STEP_CONTROL_PROPERTY \" +\n    \"SUBTASK_BLOCK_SUBJECT_PROPERTY \" +\n    \"SUBTASK_BLOCK_TASK_CONTROL_PROPERTY \" +\n    \"SUBTASK_BLOCK_TEXT_PROPERTY \" +\n    \"SUBTASK_BLOCK_UNLOCK_ATTACHMENTS_ON_STOP_PROPERTY \" +\n    \"SUBTASK_BLOCK_USE_STANDARD_ROUTE_PROPERTY \" +\n    \"SUBTASK_BLOCK_WAIT_FOR_TASK_COMPLETE_PROPERTY \";\n\n  // System component\n  const system_component_constants =\n    \"SYSCOMP_CONTROL_JOBS \" +\n    \"SYSCOMP_FOLDERS \" +\n    \"SYSCOMP_JOBS \" +\n    \"SYSCOMP_NOTICES \" +\n    \"SYSCOMP_TASKS \";\n\n  // System dialogs\n  const system_dialogs_constants =\n    \"SYSDLG_CREATE_EDOCUMENT \" +\n    \"SYSDLG_CREATE_EDOCUMENT_VERSION \" +\n    \"SYSDLG_CURRENT_PERIOD \" +\n    \"SYSDLG_EDIT_FUNCTION_HELP \" +\n    \"SYSDLG_EDOCUMENT_KINDS_FOR_TEMPLATE \" +\n    \"SYSDLG_EXPORT_MULTIPLE_EDOCUMENTS \" +\n    \"SYSDLG_EXPORT_SINGLE_EDOCUMENT \" +\n    \"SYSDLG_IMPORT_EDOCUMENT \" +\n    \"SYSDLG_MULTIPLE_SELECT \" +\n    \"SYSDLG_SETUP_ACCESS_RIGHTS \" +\n    \"SYSDLG_SETUP_DEFAULT_RIGHTS \" +\n    \"SYSDLG_SETUP_FILTER_CONDITION \" +\n    \"SYSDLG_SETUP_SIGN_RIGHTS \" +\n    \"SYSDLG_SETUP_TASK_OBSERVERS \" +\n    \"SYSDLG_SETUP_TASK_ROUTE \" +\n    \"SYSDLG_SETUP_USERS_LIST \" +\n    \"SYSDLG_SIGN_EDOCUMENT \" +\n    \"SYSDLG_SIGN_MULTIPLE_EDOCUMENTS \";\n\n  // System reference names\n  const system_reference_names_constants =\n    \"SYSREF_ACCESS_RIGHTS_TYPES \" +\n    \"SYSREF_ADMINISTRATION_HISTORY \" +\n    \"SYSREF_ALL_AVAILABLE_COMPONENTS \" +\n    \"SYSREF_ALL_AVAILABLE_PRIVILEGES \" +\n    \"SYSREF_ALL_REPLICATING_COMPONENTS \" +\n    \"SYSREF_AVAILABLE_DEVELOPERS_COMPONENTS \" +\n    \"SYSREF_CALENDAR_EVENTS \" +\n    \"SYSREF_COMPONENT_TOKEN_HISTORY \" +\n    \"SYSREF_COMPONENT_TOKENS \" +\n    \"SYSREF_COMPONENTS \" +\n    \"SYSREF_CONSTANTS \" +\n    \"SYSREF_DATA_RECEIVE_PROTOCOL \" +\n    \"SYSREF_DATA_SEND_PROTOCOL \" +\n    \"SYSREF_DIALOGS \" +\n    \"SYSREF_DIALOGS_REQUISITES \" +\n    \"SYSREF_EDITORS \" +\n    \"SYSREF_EDOC_CARDS \" +\n    \"SYSREF_EDOC_TYPES \" +\n    \"SYSREF_EDOCUMENT_CARD_REQUISITES \" +\n    \"SYSREF_EDOCUMENT_CARD_TYPES \" +\n    \"SYSREF_EDOCUMENT_CARD_TYPES_REFERENCE \" +\n    \"SYSREF_EDOCUMENT_CARDS \" +\n    \"SYSREF_EDOCUMENT_HISTORY \" +\n    \"SYSREF_EDOCUMENT_KINDS \" +\n    \"SYSREF_EDOCUMENT_REQUISITES \" +\n    \"SYSREF_EDOCUMENT_SIGNATURES \" +\n    \"SYSREF_EDOCUMENT_TEMPLATES \" +\n    \"SYSREF_EDOCUMENT_TEXT_STORAGES \" +\n    \"SYSREF_EDOCUMENT_VIEWS \" +\n    \"SYSREF_FILTERER_SETUP_CONFLICTS \" +\n    \"SYSREF_FILTRATER_SETTING_CONFLICTS \" +\n    \"SYSREF_FOLDER_HISTORY \" +\n    \"SYSREF_FOLDERS \" +\n    \"SYSREF_FUNCTION_GROUPS \" +\n    \"SYSREF_FUNCTION_PARAMS \" +\n    \"SYSREF_FUNCTIONS \" +\n    \"SYSREF_JOB_HISTORY \" +\n    \"SYSREF_LINKS \" +\n    \"SYSREF_LOCALIZATION_DICTIONARY \" +\n    \"SYSREF_LOCALIZATION_LANGUAGES \" +\n    \"SYSREF_MODULES \" +\n    \"SYSREF_PRIVILEGES \" +\n    \"SYSREF_RECORD_HISTORY \" +\n    \"SYSREF_REFERENCE_REQUISITES \" +\n    \"SYSREF_REFERENCE_TYPE_VIEWS \" +\n    \"SYSREF_REFERENCE_TYPES \" +\n    \"SYSREF_REFERENCES \" +\n    \"SYSREF_REFERENCES_REQUISITES \" +\n    \"SYSREF_REMOTE_SERVERS \" +\n    \"SYSREF_REPLICATION_SESSIONS_LOG \" +\n    \"SYSREF_REPLICATION_SESSIONS_PROTOCOL \" +\n    \"SYSREF_REPORTS \" +\n    \"SYSREF_ROLES \" +\n    \"SYSREF_ROUTE_BLOCK_GROUPS \" +\n    \"SYSREF_ROUTE_BLOCKS \" +\n    \"SYSREF_SCRIPTS \" +\n    \"SYSREF_SEARCHES \" +\n    \"SYSREF_SERVER_EVENTS \" +\n    \"SYSREF_SERVER_EVENTS_HISTORY \" +\n    \"SYSREF_STANDARD_ROUTE_GROUPS \" +\n    \"SYSREF_STANDARD_ROUTES \" +\n    \"SYSREF_STATUSES \" +\n    \"SYSREF_SYSTEM_SETTINGS \" +\n    \"SYSREF_TASK_HISTORY \" +\n    \"SYSREF_TASK_KIND_GROUPS \" +\n    \"SYSREF_TASK_KINDS \" +\n    \"SYSREF_TASK_RIGHTS \" +\n    \"SYSREF_TASK_SIGNATURES \" +\n    \"SYSREF_TASKS \" +\n    \"SYSREF_UNITS \" +\n    \"SYSREF_USER_GROUPS \" +\n    \"SYSREF_USER_GROUPS_REFERENCE \" +\n    \"SYSREF_USER_SUBSTITUTION \" +\n    \"SYSREF_USERS \" +\n    \"SYSREF_USERS_REFERENCE \" +\n    \"SYSREF_VIEWERS \" +\n    \"SYSREF_WORKING_TIME_CALENDARS \";\n\n  // Table name\n  const table_name_constants =\n    \"ACCESS_RIGHTS_TABLE_NAME \" +\n    \"EDMS_ACCESS_TABLE_NAME \" +\n    \"EDOC_TYPES_TABLE_NAME \";\n\n  // Test\n  const test_constants =\n    \"TEST_DEV_DB_NAME \" +\n    \"TEST_DEV_SYSTEM_CODE \" +\n    \"TEST_EDMS_DB_NAME \" +\n    \"TEST_EDMS_MAIN_CODE \" +\n    \"TEST_EDMS_MAIN_DB_NAME \" +\n    \"TEST_EDMS_SECOND_CODE \" +\n    \"TEST_EDMS_SECOND_DB_NAME \" +\n    \"TEST_EDMS_SYSTEM_CODE \" +\n    \"TEST_ISB5_MAIN_CODE \" +\n    \"TEST_ISB5_SECOND_CODE \" +\n    \"TEST_SQL_SERVER_2005_NAME \" +\n    \"TEST_SQL_SERVER_NAME \";\n\n  // Using the dialog windows\n  const using_the_dialog_windows_constants =\n    \"ATTENTION_CAPTION \" +\n    \"cbsCommandLinks \" +\n    \"cbsDefault \" +\n    \"CONFIRMATION_CAPTION \" +\n    \"ERROR_CAPTION \" +\n    \"INFORMATION_CAPTION \" +\n    \"mrCancel \" +\n    \"mrOk \";\n\n  // Using the document\n  const using_the_document_constants =\n    \"EDOC_VERSION_ACTIVE_STAGE_CODE \" +\n    \"EDOC_VERSION_DESIGN_STAGE_CODE \" +\n    \"EDOC_VERSION_OBSOLETE_STAGE_CODE \";\n\n  // Using the EA and encryption\n  const using_the_EA_and_encryption_constants =\n    \"cpDataEnciphermentEnabled \" +\n    \"cpDigitalSignatureEnabled \" +\n    \"cpID \" +\n    \"cpIssuer \" +\n    \"cpPluginVersion \" +\n    \"cpSerial \" +\n    \"cpSubjectName \" +\n    \"cpSubjSimpleName \" +\n    \"cpValidFromDate \" +\n    \"cpValidToDate \";\n\n  // Using the ISBL-editor\n  const using_the_ISBL_editor_constants =\n    \"ISBL_SYNTAX \" + \"NO_SYNTAX \" + \"XML_SYNTAX \";\n\n  // Wait block properties\n  const wait_block_properties_constants =\n    \"WAIT_BLOCK_AFTER_FINISH_EVENT \" +\n    \"WAIT_BLOCK_BEFORE_START_EVENT \" +\n    \"WAIT_BLOCK_DEADLINE_PROPERTY \" +\n    \"WAIT_BLOCK_IS_RELATIVE_DEADLINE_PROPERTY \" +\n    \"WAIT_BLOCK_NAME_PROPERTY \" +\n    \"WAIT_BLOCK_RELATIVE_DEADLINE_TYPE_PROPERTY \";\n\n  // SYSRES Common\n  const sysres_common_constants =\n    \"SYSRES_COMMON \" +\n    \"SYSRES_CONST \" +\n    \"SYSRES_MBFUNC \" +\n    \"SYSRES_SBDATA \" +\n    \"SYSRES_SBGUI \" +\n    \"SYSRES_SBINTF \" +\n    \"SYSRES_SBREFDSC \" +\n    \"SYSRES_SQLERRORS \" +\n    \"SYSRES_SYSCOMP \";\n\n  // Константы ==> built_in\n  const CONSTANTS =\n    sysres_constants +\n    base_constants +\n    base_group_name_constants +\n    decision_block_properties_constants +\n    file_extension_constants +\n    job_block_properties_constants +\n    language_code_constants +\n    launching_external_applications_constants +\n    link_kind_constants +\n    lock_type_constants +\n    monitor_block_properties_constants +\n    notice_block_properties_constants +\n    object_events_constants +\n    object_params_constants +\n    other_constants +\n    privileges_constants +\n    pseudoreference_code_constants +\n    requisite_ISBCertificateType_values_constants +\n    requisite_ISBEDocStorageType_values_constants +\n    requisite_compType2_values_constants +\n    requisite_name_constants +\n    result_constants +\n    rule_identification_constants +\n    script_block_properties_constants +\n    subtask_block_properties_constants +\n    system_component_constants +\n    system_dialogs_constants +\n    system_reference_names_constants +\n    table_name_constants +\n    test_constants +\n    using_the_dialog_windows_constants +\n    using_the_document_constants +\n    using_the_EA_and_encryption_constants +\n    using_the_ISBL_editor_constants +\n    wait_block_properties_constants +\n    sysres_common_constants;\n\n  // enum TAccountType\n  const TAccountType = \"atUser atGroup atRole \";\n\n  // enum TActionEnabledMode\n  const TActionEnabledMode =\n    \"aemEnabledAlways \" +\n    \"aemDisabledAlways \" +\n    \"aemEnabledOnBrowse \" +\n    \"aemEnabledOnEdit \" +\n    \"aemDisabledOnBrowseEmpty \";\n\n  // enum TAddPosition\n  const TAddPosition = \"apBegin apEnd \";\n\n  // enum TAlignment\n  const TAlignment = \"alLeft alRight \";\n\n  // enum TAreaShowMode\n  const TAreaShowMode =\n    \"asmNever \" +\n    \"asmNoButCustomize \" +\n    \"asmAsLastTime \" +\n    \"asmYesButCustomize \" +\n    \"asmAlways \";\n\n  // enum TCertificateInvalidationReason\n  const TCertificateInvalidationReason = \"cirCommon cirRevoked \";\n\n  // enum TCertificateType\n  const TCertificateType = \"ctSignature ctEncode ctSignatureEncode \";\n\n  // enum TCheckListBoxItemState\n  const TCheckListBoxItemState = \"clbUnchecked clbChecked clbGrayed \";\n\n  // enum TCloseOnEsc\n  const TCloseOnEsc = \"ceISB ceAlways ceNever \";\n\n  // enum TCompType\n  const TCompType =\n    \"ctDocument \" +\n    \"ctReference \" +\n    \"ctScript \" +\n    \"ctUnknown \" +\n    \"ctReport \" +\n    \"ctDialog \" +\n    \"ctFunction \" +\n    \"ctFolder \" +\n    \"ctEDocument \" +\n    \"ctTask \" +\n    \"ctJob \" +\n    \"ctNotice \" +\n    \"ctControlJob \";\n\n  // enum TConditionFormat\n  const TConditionFormat = \"cfInternal cfDisplay \";\n\n  // enum TConnectionIntent\n  const TConnectionIntent = \"ciUnspecified ciWrite ciRead \";\n\n  // enum TContentKind\n  const TContentKind =\n    \"ckFolder \" +\n    \"ckEDocument \" +\n    \"ckTask \" +\n    \"ckJob \" +\n    \"ckComponentToken \" +\n    \"ckAny \" +\n    \"ckReference \" +\n    \"ckScript \" +\n    \"ckReport \" +\n    \"ckDialog \";\n\n  // enum TControlType\n  const TControlType =\n    \"ctISBLEditor \" +\n    \"ctBevel \" +\n    \"ctButton \" +\n    \"ctCheckListBox \" +\n    \"ctComboBox \" +\n    \"ctComboEdit \" +\n    \"ctGrid \" +\n    \"ctDBCheckBox \" +\n    \"ctDBComboBox \" +\n    \"ctDBEdit \" +\n    \"ctDBEllipsis \" +\n    \"ctDBMemo \" +\n    \"ctDBNavigator \" +\n    \"ctDBRadioGroup \" +\n    \"ctDBStatusLabel \" +\n    \"ctEdit \" +\n    \"ctGroupBox \" +\n    \"ctInplaceHint \" +\n    \"ctMemo \" +\n    \"ctPanel \" +\n    \"ctListBox \" +\n    \"ctRadioButton \" +\n    \"ctRichEdit \" +\n    \"ctTabSheet \" +\n    \"ctWebBrowser \" +\n    \"ctImage \" +\n    \"ctHyperLink \" +\n    \"ctLabel \" +\n    \"ctDBMultiEllipsis \" +\n    \"ctRibbon \" +\n    \"ctRichView \" +\n    \"ctInnerPanel \" +\n    \"ctPanelGroup \" +\n    \"ctBitButton \";\n\n  // enum TCriterionContentType\n  const TCriterionContentType =\n    \"cctDate \" +\n    \"cctInteger \" +\n    \"cctNumeric \" +\n    \"cctPick \" +\n    \"cctReference \" +\n    \"cctString \" +\n    \"cctText \";\n\n  // enum TCultureType\n  const TCultureType = \"cltInternal cltPrimary cltGUI \";\n\n  // enum TDataSetEventType\n  const TDataSetEventType =\n    \"dseBeforeOpen \" +\n    \"dseAfterOpen \" +\n    \"dseBeforeClose \" +\n    \"dseAfterClose \" +\n    \"dseOnValidDelete \" +\n    \"dseBeforeDelete \" +\n    \"dseAfterDelete \" +\n    \"dseAfterDeleteOutOfTransaction \" +\n    \"dseOnDeleteError \" +\n    \"dseBeforeInsert \" +\n    \"dseAfterInsert \" +\n    \"dseOnValidUpdate \" +\n    \"dseBeforeUpdate \" +\n    \"dseOnUpdateRatifiedRecord \" +\n    \"dseAfterUpdate \" +\n    \"dseAfterUpdateOutOfTransaction \" +\n    \"dseOnUpdateError \" +\n    \"dseAfterScroll \" +\n    \"dseOnOpenRecord \" +\n    \"dseOnCloseRecord \" +\n    \"dseBeforeCancel \" +\n    \"dseAfterCancel \" +\n    \"dseOnUpdateDeadlockError \" +\n    \"dseBeforeDetailUpdate \" +\n    \"dseOnPrepareUpdate \" +\n    \"dseOnAnyRequisiteChange \";\n\n  // enum TDataSetState\n  const TDataSetState = \"dssEdit dssInsert dssBrowse dssInActive \";\n\n  // enum TDateFormatType\n  const TDateFormatType = \"dftDate dftShortDate dftDateTime dftTimeStamp \";\n\n  // enum TDateOffsetType\n  const TDateOffsetType = \"dotDays dotHours dotMinutes dotSeconds \";\n\n  // enum TDateTimeKind\n  const TDateTimeKind = \"dtkndLocal dtkndUTC \";\n\n  // enum TDeaAccessRights\n  const TDeaAccessRights = \"arNone arView arEdit arFull \";\n\n  // enum TDocumentDefaultAction\n  const TDocumentDefaultAction = \"ddaView ddaEdit \";\n\n  // enum TEditMode\n  const TEditMode =\n    \"emLock \" +\n    \"emEdit \" +\n    \"emSign \" +\n    \"emExportWithLock \" +\n    \"emImportWithUnlock \" +\n    \"emChangeVersionNote \" +\n    \"emOpenForModify \" +\n    \"emChangeLifeStage \" +\n    \"emDelete \" +\n    \"emCreateVersion \" +\n    \"emImport \" +\n    \"emUnlockExportedWithLock \" +\n    \"emStart \" +\n    \"emAbort \" +\n    \"emReInit \" +\n    \"emMarkAsReaded \" +\n    \"emMarkAsUnreaded \" +\n    \"emPerform \" +\n    \"emAccept \" +\n    \"emResume \" +\n    \"emChangeRights \" +\n    \"emEditRoute \" +\n    \"emEditObserver \" +\n    \"emRecoveryFromLocalCopy \" +\n    \"emChangeWorkAccessType \" +\n    \"emChangeEncodeTypeToCertificate \" +\n    \"emChangeEncodeTypeToPassword \" +\n    \"emChangeEncodeTypeToNone \" +\n    \"emChangeEncodeTypeToCertificatePassword \" +\n    \"emChangeStandardRoute \" +\n    \"emGetText \" +\n    \"emOpenForView \" +\n    \"emMoveToStorage \" +\n    \"emCreateObject \" +\n    \"emChangeVersionHidden \" +\n    \"emDeleteVersion \" +\n    \"emChangeLifeCycleStage \" +\n    \"emApprovingSign \" +\n    \"emExport \" +\n    \"emContinue \" +\n    \"emLockFromEdit \" +\n    \"emUnLockForEdit \" +\n    \"emLockForServer \" +\n    \"emUnlockFromServer \" +\n    \"emDelegateAccessRights \" +\n    \"emReEncode \";\n\n  // enum TEditorCloseObservType\n  const TEditorCloseObservType = \"ecotFile ecotProcess \";\n\n  // enum TEdmsApplicationAction\n  const TEdmsApplicationAction = \"eaGet eaCopy eaCreate eaCreateStandardRoute \";\n\n  // enum TEDocumentLockType\n  const TEDocumentLockType = \"edltAll edltNothing edltQuery \";\n\n  // enum TEDocumentStepShowMode\n  const TEDocumentStepShowMode = \"essmText essmCard \";\n\n  // enum TEDocumentStepVersionType\n  const TEDocumentStepVersionType = \"esvtLast esvtLastActive esvtSpecified \";\n\n  // enum TEDocumentStorageFunction\n  const TEDocumentStorageFunction = \"edsfExecutive edsfArchive \";\n\n  // enum TEDocumentStorageType\n  const TEDocumentStorageType = \"edstSQLServer edstFile \";\n\n  // enum TEDocumentVersionSourceType\n  const TEDocumentVersionSourceType =\n    \"edvstNone edvstEDocumentVersionCopy edvstFile edvstTemplate edvstScannedFile \";\n\n  // enum TEDocumentVersionState\n  const TEDocumentVersionState = \"vsDefault vsDesign vsActive vsObsolete \";\n\n  // enum TEncodeType\n  const TEncodeType = \"etNone etCertificate etPassword etCertificatePassword \";\n\n  // enum TExceptionCategory\n  const TExceptionCategory = \"ecException ecWarning ecInformation \";\n\n  // enum TExportedSignaturesType\n  const TExportedSignaturesType = \"estAll estApprovingOnly \";\n\n  // enum TExportedVersionType\n  const TExportedVersionType = \"evtLast evtLastActive evtQuery \";\n\n  // enum TFieldDataType\n  const TFieldDataType =\n    \"fdtString \" +\n    \"fdtNumeric \" +\n    \"fdtInteger \" +\n    \"fdtDate \" +\n    \"fdtText \" +\n    \"fdtUnknown \" +\n    \"fdtWideString \" +\n    \"fdtLargeInteger \";\n\n  // enum TFolderType\n  const TFolderType =\n    \"ftInbox \" +\n    \"ftOutbox \" +\n    \"ftFavorites \" +\n    \"ftCommonFolder \" +\n    \"ftUserFolder \" +\n    \"ftComponents \" +\n    \"ftQuickLaunch \" +\n    \"ftShortcuts \" +\n    \"ftSearch \";\n\n  // enum TGridRowHeight\n  const TGridRowHeight = \"grhAuto \" + \"grhX1 \" + \"grhX2 \" + \"grhX3 \";\n\n  // enum THyperlinkType\n  const THyperlinkType = \"hltText \" + \"hltRTF \" + \"hltHTML \";\n\n  // enum TImageFileFormat\n  const TImageFileFormat =\n    \"iffBMP \" +\n    \"iffJPEG \" +\n    \"iffMultiPageTIFF \" +\n    \"iffSinglePageTIFF \" +\n    \"iffTIFF \" +\n    \"iffPNG \";\n\n  // enum TImageMode\n  const TImageMode = \"im8bGrayscale \" + \"im24bRGB \" + \"im1bMonochrome \";\n\n  // enum TImageType\n  const TImageType = \"itBMP \" + \"itJPEG \" + \"itWMF \" + \"itPNG \";\n\n  // enum TInplaceHintKind\n  const TInplaceHintKind =\n    \"ikhInformation \" + \"ikhWarning \" + \"ikhError \" + \"ikhNoIcon \";\n\n  // enum TISBLContext\n  const TISBLContext =\n    \"icUnknown \" +\n    \"icScript \" +\n    \"icFunction \" +\n    \"icIntegratedReport \" +\n    \"icAnalyticReport \" +\n    \"icDataSetEventHandler \" +\n    \"icActionHandler \" +\n    \"icFormEventHandler \" +\n    \"icLookUpEventHandler \" +\n    \"icRequisiteChangeEventHandler \" +\n    \"icBeforeSearchEventHandler \" +\n    \"icRoleCalculation \" +\n    \"icSelectRouteEventHandler \" +\n    \"icBlockPropertyCalculation \" +\n    \"icBlockQueryParamsEventHandler \" +\n    \"icChangeSearchResultEventHandler \" +\n    \"icBlockEventHandler \" +\n    \"icSubTaskInitEventHandler \" +\n    \"icEDocDataSetEventHandler \" +\n    \"icEDocLookUpEventHandler \" +\n    \"icEDocActionHandler \" +\n    \"icEDocFormEventHandler \" +\n    \"icEDocRequisiteChangeEventHandler \" +\n    \"icStructuredConversionRule \" +\n    \"icStructuredConversionEventBefore \" +\n    \"icStructuredConversionEventAfter \" +\n    \"icWizardEventHandler \" +\n    \"icWizardFinishEventHandler \" +\n    \"icWizardStepEventHandler \" +\n    \"icWizardStepFinishEventHandler \" +\n    \"icWizardActionEnableEventHandler \" +\n    \"icWizardActionExecuteEventHandler \" +\n    \"icCreateJobsHandler \" +\n    \"icCreateNoticesHandler \" +\n    \"icBeforeLookUpEventHandler \" +\n    \"icAfterLookUpEventHandler \" +\n    \"icTaskAbortEventHandler \" +\n    \"icWorkflowBlockActionHandler \" +\n    \"icDialogDataSetEventHandler \" +\n    \"icDialogActionHandler \" +\n    \"icDialogLookUpEventHandler \" +\n    \"icDialogRequisiteChangeEventHandler \" +\n    \"icDialogFormEventHandler \" +\n    \"icDialogValidCloseEventHandler \" +\n    \"icBlockFormEventHandler \" +\n    \"icTaskFormEventHandler \" +\n    \"icReferenceMethod \" +\n    \"icEDocMethod \" +\n    \"icDialogMethod \" +\n    \"icProcessMessageHandler \";\n\n  // enum TItemShow\n  const TItemShow = \"isShow \" + \"isHide \" + \"isByUserSettings \";\n\n  // enum TJobKind\n  const TJobKind = \"jkJob \" + \"jkNotice \" + \"jkControlJob \";\n\n  // enum TJoinType\n  const TJoinType = \"jtInner \" + \"jtLeft \" + \"jtRight \" + \"jtFull \" + \"jtCross \";\n\n  // enum TLabelPos\n  const TLabelPos = \"lbpAbove \" + \"lbpBelow \" + \"lbpLeft \" + \"lbpRight \";\n\n  // enum TLicensingType\n  const TLicensingType = \"eltPerConnection \" + \"eltPerUser \";\n\n  // enum TLifeCycleStageFontColor\n  const TLifeCycleStageFontColor =\n    \"sfcUndefined \" +\n    \"sfcBlack \" +\n    \"sfcGreen \" +\n    \"sfcRed \" +\n    \"sfcBlue \" +\n    \"sfcOrange \" +\n    \"sfcLilac \";\n\n  // enum TLifeCycleStageFontStyle\n  const TLifeCycleStageFontStyle = \"sfsItalic \" + \"sfsStrikeout \" + \"sfsNormal \";\n\n  // enum TLockableDevelopmentComponentType\n  const TLockableDevelopmentComponentType =\n    \"ldctStandardRoute \" +\n    \"ldctWizard \" +\n    \"ldctScript \" +\n    \"ldctFunction \" +\n    \"ldctRouteBlock \" +\n    \"ldctIntegratedReport \" +\n    \"ldctAnalyticReport \" +\n    \"ldctReferenceType \" +\n    \"ldctEDocumentType \" +\n    \"ldctDialog \" +\n    \"ldctServerEvents \";\n\n  // enum TMaxRecordCountRestrictionType\n  const TMaxRecordCountRestrictionType =\n    \"mrcrtNone \" + \"mrcrtUser \" + \"mrcrtMaximal \" + \"mrcrtCustom \";\n\n  // enum TRangeValueType\n  const TRangeValueType =\n    \"vtEqual \" + \"vtGreaterOrEqual \" + \"vtLessOrEqual \" + \"vtRange \";\n\n  // enum TRelativeDate\n  const TRelativeDate =\n    \"rdYesterday \" +\n    \"rdToday \" +\n    \"rdTomorrow \" +\n    \"rdThisWeek \" +\n    \"rdThisMonth \" +\n    \"rdThisYear \" +\n    \"rdNextMonth \" +\n    \"rdNextWeek \" +\n    \"rdLastWeek \" +\n    \"rdLastMonth \";\n\n  // enum TReportDestination\n  const TReportDestination = \"rdWindow \" + \"rdFile \" + \"rdPrinter \";\n\n  // enum TReqDataType\n  const TReqDataType =\n    \"rdtString \" +\n    \"rdtNumeric \" +\n    \"rdtInteger \" +\n    \"rdtDate \" +\n    \"rdtReference \" +\n    \"rdtAccount \" +\n    \"rdtText \" +\n    \"rdtPick \" +\n    \"rdtUnknown \" +\n    \"rdtLargeInteger \" +\n    \"rdtDocument \";\n\n  // enum TRequisiteEventType\n  const TRequisiteEventType = \"reOnChange \" + \"reOnChangeValues \";\n\n  // enum TSBTimeType\n  const TSBTimeType = \"ttGlobal \" + \"ttLocal \" + \"ttUser \" + \"ttSystem \";\n\n  // enum TSearchShowMode\n  const TSearchShowMode =\n    \"ssmBrowse \" + \"ssmSelect \" + \"ssmMultiSelect \" + \"ssmBrowseModal \";\n\n  // enum TSelectMode\n  const TSelectMode = \"smSelect \" + \"smLike \" + \"smCard \";\n\n  // enum TSignatureType\n  const TSignatureType = \"stNone \" + \"stAuthenticating \" + \"stApproving \";\n\n  // enum TSignerContentType\n  const TSignerContentType = \"sctString \" + \"sctStream \";\n\n  // enum TStringsSortType\n  const TStringsSortType = \"sstAnsiSort \" + \"sstNaturalSort \";\n\n  // enum TStringValueType\n  const TStringValueType = \"svtEqual \" + \"svtContain \";\n\n  // enum TStructuredObjectAttributeType\n  const TStructuredObjectAttributeType =\n    \"soatString \" +\n    \"soatNumeric \" +\n    \"soatInteger \" +\n    \"soatDatetime \" +\n    \"soatReferenceRecord \" +\n    \"soatText \" +\n    \"soatPick \" +\n    \"soatBoolean \" +\n    \"soatEDocument \" +\n    \"soatAccount \" +\n    \"soatIntegerCollection \" +\n    \"soatNumericCollection \" +\n    \"soatStringCollection \" +\n    \"soatPickCollection \" +\n    \"soatDatetimeCollection \" +\n    \"soatBooleanCollection \" +\n    \"soatReferenceRecordCollection \" +\n    \"soatEDocumentCollection \" +\n    \"soatAccountCollection \" +\n    \"soatContents \" +\n    \"soatUnknown \";\n\n  // enum TTaskAbortReason\n  const TTaskAbortReason = \"tarAbortByUser \" + \"tarAbortByWorkflowException \";\n\n  // enum TTextValueType\n  const TTextValueType = \"tvtAllWords \" + \"tvtExactPhrase \" + \"tvtAnyWord \";\n\n  // enum TUserObjectStatus\n  const TUserObjectStatus =\n    \"usNone \" +\n    \"usCompleted \" +\n    \"usRedSquare \" +\n    \"usBlueSquare \" +\n    \"usYellowSquare \" +\n    \"usGreenSquare \" +\n    \"usOrangeSquare \" +\n    \"usPurpleSquare \" +\n    \"usFollowUp \";\n\n  // enum TUserType\n  const TUserType =\n    \"utUnknown \" +\n    \"utUser \" +\n    \"utDeveloper \" +\n    \"utAdministrator \" +\n    \"utSystemDeveloper \" +\n    \"utDisconnected \";\n\n  // enum TValuesBuildType\n  const TValuesBuildType =\n    \"btAnd \" + \"btDetailAnd \" + \"btOr \" + \"btNotOr \" + \"btOnly \";\n\n  // enum TViewMode\n  const TViewMode = \"vmView \" + \"vmSelect \" + \"vmNavigation \";\n\n  // enum TViewSelectionMode\n  const TViewSelectionMode =\n    \"vsmSingle \" + \"vsmMultiple \" + \"vsmMultipleCheck \" + \"vsmNoSelection \";\n\n  // enum TWizardActionType\n  const TWizardActionType =\n    \"wfatPrevious \" + \"wfatNext \" + \"wfatCancel \" + \"wfatFinish \";\n\n  // enum TWizardFormElementProperty\n  const TWizardFormElementProperty =\n    \"wfepUndefined \" +\n    \"wfepText3 \" +\n    \"wfepText6 \" +\n    \"wfepText9 \" +\n    \"wfepSpinEdit \" +\n    \"wfepDropDown \" +\n    \"wfepRadioGroup \" +\n    \"wfepFlag \" +\n    \"wfepText12 \" +\n    \"wfepText15 \" +\n    \"wfepText18 \" +\n    \"wfepText21 \" +\n    \"wfepText24 \" +\n    \"wfepText27 \" +\n    \"wfepText30 \" +\n    \"wfepRadioGroupColumn1 \" +\n    \"wfepRadioGroupColumn2 \" +\n    \"wfepRadioGroupColumn3 \";\n\n  // enum TWizardFormElementType\n  const TWizardFormElementType =\n    \"wfetQueryParameter \" + \"wfetText \" + \"wfetDelimiter \" + \"wfetLabel \";\n\n  // enum TWizardParamType\n  const TWizardParamType =\n    \"wptString \" +\n    \"wptInteger \" +\n    \"wptNumeric \" +\n    \"wptBoolean \" +\n    \"wptDateTime \" +\n    \"wptPick \" +\n    \"wptText \" +\n    \"wptUser \" +\n    \"wptUserList \" +\n    \"wptEDocumentInfo \" +\n    \"wptEDocumentInfoList \" +\n    \"wptReferenceRecordInfo \" +\n    \"wptReferenceRecordInfoList \" +\n    \"wptFolderInfo \" +\n    \"wptTaskInfo \" +\n    \"wptContents \" +\n    \"wptFileName \" +\n    \"wptDate \";\n\n  // enum TWizardStepResult\n  const TWizardStepResult =\n    \"wsrComplete \" +\n    \"wsrGoNext \" +\n    \"wsrGoPrevious \" +\n    \"wsrCustom \" +\n    \"wsrCancel \" +\n    \"wsrGoFinal \";\n\n  // enum TWizardStepType\n  const TWizardStepType =\n    \"wstForm \" +\n    \"wstEDocument \" +\n    \"wstTaskCard \" +\n    \"wstReferenceRecordCard \" +\n    \"wstFinal \";\n\n  // enum TWorkAccessType\n  const TWorkAccessType = \"waAll \" + \"waPerformers \" + \"waManual \";\n\n  // enum TWorkflowBlockType\n  const TWorkflowBlockType =\n    \"wsbStart \" +\n    \"wsbFinish \" +\n    \"wsbNotice \" +\n    \"wsbStep \" +\n    \"wsbDecision \" +\n    \"wsbWait \" +\n    \"wsbMonitor \" +\n    \"wsbScript \" +\n    \"wsbConnector \" +\n    \"wsbSubTask \" +\n    \"wsbLifeCycleStage \" +\n    \"wsbPause \";\n\n  // enum TWorkflowDataType\n  const TWorkflowDataType =\n    \"wdtInteger \" +\n    \"wdtFloat \" +\n    \"wdtString \" +\n    \"wdtPick \" +\n    \"wdtDateTime \" +\n    \"wdtBoolean \" +\n    \"wdtTask \" +\n    \"wdtJob \" +\n    \"wdtFolder \" +\n    \"wdtEDocument \" +\n    \"wdtReferenceRecord \" +\n    \"wdtUser \" +\n    \"wdtGroup \" +\n    \"wdtRole \" +\n    \"wdtIntegerCollection \" +\n    \"wdtFloatCollection \" +\n    \"wdtStringCollection \" +\n    \"wdtPickCollection \" +\n    \"wdtDateTimeCollection \" +\n    \"wdtBooleanCollection \" +\n    \"wdtTaskCollection \" +\n    \"wdtJobCollection \" +\n    \"wdtFolderCollection \" +\n    \"wdtEDocumentCollection \" +\n    \"wdtReferenceRecordCollection \" +\n    \"wdtUserCollection \" +\n    \"wdtGroupCollection \" +\n    \"wdtRoleCollection \" +\n    \"wdtContents \" +\n    \"wdtUserList \" +\n    \"wdtSearchDescription \" +\n    \"wdtDeadLine \" +\n    \"wdtPickSet \" +\n    \"wdtAccountCollection \";\n\n  // enum TWorkImportance\n  const TWorkImportance = \"wiLow \" + \"wiNormal \" + \"wiHigh \";\n\n  // enum TWorkRouteType\n  const TWorkRouteType = \"wrtSoft \" + \"wrtHard \";\n\n  // enum TWorkState\n  const TWorkState =\n    \"wsInit \" +\n    \"wsRunning \" +\n    \"wsDone \" +\n    \"wsControlled \" +\n    \"wsAborted \" +\n    \"wsContinued \";\n\n  // enum TWorkTextBuildingMode\n  const TWorkTextBuildingMode =\n    \"wtmFull \" + \"wtmFromCurrent \" + \"wtmOnlyCurrent \";\n\n  // Перечисления\n  const ENUMS =\n    TAccountType +\n    TActionEnabledMode +\n    TAddPosition +\n    TAlignment +\n    TAreaShowMode +\n    TCertificateInvalidationReason +\n    TCertificateType +\n    TCheckListBoxItemState +\n    TCloseOnEsc +\n    TCompType +\n    TConditionFormat +\n    TConnectionIntent +\n    TContentKind +\n    TControlType +\n    TCriterionContentType +\n    TCultureType +\n    TDataSetEventType +\n    TDataSetState +\n    TDateFormatType +\n    TDateOffsetType +\n    TDateTimeKind +\n    TDeaAccessRights +\n    TDocumentDefaultAction +\n    TEditMode +\n    TEditorCloseObservType +\n    TEdmsApplicationAction +\n    TEDocumentLockType +\n    TEDocumentStepShowMode +\n    TEDocumentStepVersionType +\n    TEDocumentStorageFunction +\n    TEDocumentStorageType +\n    TEDocumentVersionSourceType +\n    TEDocumentVersionState +\n    TEncodeType +\n    TExceptionCategory +\n    TExportedSignaturesType +\n    TExportedVersionType +\n    TFieldDataType +\n    TFolderType +\n    TGridRowHeight +\n    THyperlinkType +\n    TImageFileFormat +\n    TImageMode +\n    TImageType +\n    TInplaceHintKind +\n    TISBLContext +\n    TItemShow +\n    TJobKind +\n    TJoinType +\n    TLabelPos +\n    TLicensingType +\n    TLifeCycleStageFontColor +\n    TLifeCycleStageFontStyle +\n    TLockableDevelopmentComponentType +\n    TMaxRecordCountRestrictionType +\n    TRangeValueType +\n    TRelativeDate +\n    TReportDestination +\n    TReqDataType +\n    TRequisiteEventType +\n    TSBTimeType +\n    TSearchShowMode +\n    TSelectMode +\n    TSignatureType +\n    TSignerContentType +\n    TStringsSortType +\n    TStringValueType +\n    TStructuredObjectAttributeType +\n    TTaskAbortReason +\n    TTextValueType +\n    TUserObjectStatus +\n    TUserType +\n    TValuesBuildType +\n    TViewMode +\n    TViewSelectionMode +\n    TWizardActionType +\n    TWizardFormElementProperty +\n    TWizardFormElementType +\n    TWizardParamType +\n    TWizardStepResult +\n    TWizardStepType +\n    TWorkAccessType +\n    TWorkflowBlockType +\n    TWorkflowDataType +\n    TWorkImportance +\n    TWorkRouteType +\n    TWorkState +\n    TWorkTextBuildingMode;\n\n  // Системные функции ==> SYSFUNCTIONS\n  const system_functions =\n    \"AddSubString \" +\n    \"AdjustLineBreaks \" +\n    \"AmountInWords \" +\n    \"Analysis \" +\n    \"ArrayDimCount \" +\n    \"ArrayHighBound \" +\n    \"ArrayLowBound \" +\n    \"ArrayOf \" +\n    \"ArrayReDim \" +\n    \"Assert \" +\n    \"Assigned \" +\n    \"BeginOfMonth \" +\n    \"BeginOfPeriod \" +\n    \"BuildProfilingOperationAnalysis \" +\n    \"CallProcedure \" +\n    \"CanReadFile \" +\n    \"CArrayElement \" +\n    \"CDataSetRequisite \" +\n    \"ChangeDate \" +\n    \"ChangeReferenceDataset \" +\n    \"Char \" +\n    \"CharPos \" +\n    \"CheckParam \" +\n    \"CheckParamValue \" +\n    \"CompareStrings \" +\n    \"ConstantExists \" +\n    \"ControlState \" +\n    \"ConvertDateStr \" +\n    \"Copy \" +\n    \"CopyFile \" +\n    \"CreateArray \" +\n    \"CreateCachedReference \" +\n    \"CreateConnection \" +\n    \"CreateDialog \" +\n    \"CreateDualListDialog \" +\n    \"CreateEditor \" +\n    \"CreateException \" +\n    \"CreateFile \" +\n    \"CreateFolderDialog \" +\n    \"CreateInputDialog \" +\n    \"CreateLinkFile \" +\n    \"CreateList \" +\n    \"CreateLock \" +\n    \"CreateMemoryDataSet \" +\n    \"CreateObject \" +\n    \"CreateOpenDialog \" +\n    \"CreateProgress \" +\n    \"CreateQuery \" +\n    \"CreateReference \" +\n    \"CreateReport \" +\n    \"CreateSaveDialog \" +\n    \"CreateScript \" +\n    \"CreateSQLPivotFunction \" +\n    \"CreateStringList \" +\n    \"CreateTreeListSelectDialog \" +\n    \"CSelectSQL \" +\n    \"CSQL \" +\n    \"CSubString \" +\n    \"CurrentUserID \" +\n    \"CurrentUserName \" +\n    \"CurrentVersion \" +\n    \"DataSetLocateEx \" +\n    \"DateDiff \" +\n    \"DateTimeDiff \" +\n    \"DateToStr \" +\n    \"DayOfWeek \" +\n    \"DeleteFile \" +\n    \"DirectoryExists \" +\n    \"DisableCheckAccessRights \" +\n    \"DisableCheckFullShowingRestriction \" +\n    \"DisableMassTaskSendingRestrictions \" +\n    \"DropTable \" +\n    \"DupeString \" +\n    \"EditText \" +\n    \"EnableCheckAccessRights \" +\n    \"EnableCheckFullShowingRestriction \" +\n    \"EnableMassTaskSendingRestrictions \" +\n    \"EndOfMonth \" +\n    \"EndOfPeriod \" +\n    \"ExceptionExists \" +\n    \"ExceptionsOff \" +\n    \"ExceptionsOn \" +\n    \"Execute \" +\n    \"ExecuteProcess \" +\n    \"Exit \" +\n    \"ExpandEnvironmentVariables \" +\n    \"ExtractFileDrive \" +\n    \"ExtractFileExt \" +\n    \"ExtractFileName \" +\n    \"ExtractFilePath \" +\n    \"ExtractParams \" +\n    \"FileExists \" +\n    \"FileSize \" +\n    \"FindFile \" +\n    \"FindSubString \" +\n    \"FirmContext \" +\n    \"ForceDirectories \" +\n    \"Format \" +\n    \"FormatDate \" +\n    \"FormatNumeric \" +\n    \"FormatSQLDate \" +\n    \"FormatString \" +\n    \"FreeException \" +\n    \"GetComponent \" +\n    \"GetComponentLaunchParam \" +\n    \"GetConstant \" +\n    \"GetLastException \" +\n    \"GetReferenceRecord \" +\n    \"GetRefTypeByRefID \" +\n    \"GetTableID \" +\n    \"GetTempFolder \" +\n    \"IfThen \" +\n    \"In \" +\n    \"IndexOf \" +\n    \"InputDialog \" +\n    \"InputDialogEx \" +\n    \"InteractiveMode \" +\n    \"IsFileLocked \" +\n    \"IsGraphicFile \" +\n    \"IsNumeric \" +\n    \"Length \" +\n    \"LoadString \" +\n    \"LoadStringFmt \" +\n    \"LocalTimeToUTC \" +\n    \"LowerCase \" +\n    \"Max \" +\n    \"MessageBox \" +\n    \"MessageBoxEx \" +\n    \"MimeDecodeBinary \" +\n    \"MimeDecodeString \" +\n    \"MimeEncodeBinary \" +\n    \"MimeEncodeString \" +\n    \"Min \" +\n    \"MoneyInWords \" +\n    \"MoveFile \" +\n    \"NewID \" +\n    \"Now \" +\n    \"OpenFile \" +\n    \"Ord \" +\n    \"Precision \" +\n    \"Raise \" +\n    \"ReadCertificateFromFile \" +\n    \"ReadFile \" +\n    \"ReferenceCodeByID \" +\n    \"ReferenceNumber \" +\n    \"ReferenceRequisiteMode \" +\n    \"ReferenceRequisiteValue \" +\n    \"RegionDateSettings \" +\n    \"RegionNumberSettings \" +\n    \"RegionTimeSettings \" +\n    \"RegRead \" +\n    \"RegWrite \" +\n    \"RenameFile \" +\n    \"Replace \" +\n    \"Round \" +\n    \"SelectServerCode \" +\n    \"SelectSQL \" +\n    \"ServerDateTime \" +\n    \"SetConstant \" +\n    \"SetManagedFolderFieldsState \" +\n    \"ShowConstantsInputDialog \" +\n    \"ShowMessage \" +\n    \"Sleep \" +\n    \"Split \" +\n    \"SQL \" +\n    \"SQL2XLSTAB \" +\n    \"SQLProfilingSendReport \" +\n    \"StrToDate \" +\n    \"SubString \" +\n    \"SubStringCount \" +\n    \"SystemSetting \" +\n    \"Time \" +\n    \"TimeDiff \" +\n    \"Today \" +\n    \"Transliterate \" +\n    \"Trim \" +\n    \"UpperCase \" +\n    \"UserStatus \" +\n    \"UTCToLocalTime \" +\n    \"ValidateXML \" +\n    \"VarIsClear \" +\n    \"VarIsEmpty \" +\n    \"VarIsNull \" +\n    \"WorkTimeDiff \" +\n    \"WriteFile \" +\n    \"WriteFileEx \" +\n    \"WriteObjectHistory \" +\n    \"Анализ \" +\n    \"БазаДанных \" +\n    \"БлокЕсть \" +\n    \"БлокЕстьРасш \" +\n    \"БлокИнфо \" +\n    \"БлокСнять \" +\n    \"БлокСнятьРасш \" +\n    \"БлокУстановить \" +\n    \"Ввод \" +\n    \"ВводМеню \" +\n    \"ВедС \" +\n    \"ВедСпр \" +\n    \"ВерхняяГраницаМассива \" +\n    \"ВнешПрогр \" +\n    \"Восст \" +\n    \"ВременнаяПапка \" +\n    \"Время \" +\n    \"ВыборSQL \" +\n    \"ВыбратьЗапись \" +\n    \"ВыделитьСтр \" +\n    \"Вызвать \" +\n    \"Выполнить \" +\n    \"ВыпПрогр \" +\n    \"ГрафическийФайл \" +\n    \"ГруппаДополнительно \" +\n    \"ДатаВремяСерв \" +\n    \"ДеньНедели \" +\n    \"ДиалогДаНет \" +\n    \"ДлинаСтр \" +\n    \"ДобПодстр \" +\n    \"ЕПусто \" +\n    \"ЕслиТо \" +\n    \"ЕЧисло \" +\n    \"ЗамПодстр \" +\n    \"ЗаписьСправочника \" +\n    \"ЗначПоляСпр \" +\n    \"ИДТипСпр \" +\n    \"ИзвлечьДиск \" +\n    \"ИзвлечьИмяФайла \" +\n    \"ИзвлечьПуть \" +\n    \"ИзвлечьРасширение \" +\n    \"ИзмДат \" +\n    \"ИзменитьРазмерМассива \" +\n    \"ИзмеренийМассива \" +\n    \"ИмяОрг \" +\n    \"ИмяПоляСпр \" +\n    \"Индекс \" +\n    \"ИндикаторЗакрыть \" +\n    \"ИндикаторОткрыть \" +\n    \"ИндикаторШаг \" +\n    \"ИнтерактивныйРежим \" +\n    \"ИтогТблСпр \" +\n    \"КодВидВедСпр \" +\n    \"КодВидСпрПоИД \" +\n    \"КодПоAnalit \" +\n    \"КодСимвола \" +\n    \"КодСпр \" +\n    \"КолПодстр \" +\n    \"КолПроп \" +\n    \"КонМес \" +\n    \"Конст \" +\n    \"КонстЕсть \" +\n    \"КонстЗнач \" +\n    \"КонТран \" +\n    \"КопироватьФайл \" +\n    \"КопияСтр \" +\n    \"КПериод \" +\n    \"КСтрТблСпр \" +\n    \"Макс \" +\n    \"МаксСтрТблСпр \" +\n    \"Массив \" +\n    \"Меню \" +\n    \"МенюРасш \" +\n    \"Мин \" +\n    \"НаборДанныхНайтиРасш \" +\n    \"НаимВидСпр \" +\n    \"НаимПоAnalit \" +\n    \"НаимСпр \" +\n    \"НастроитьПереводыСтрок \" +\n    \"НачМес \" +\n    \"НачТран \" +\n    \"НижняяГраницаМассива \" +\n    \"НомерСпр \" +\n    \"НПериод \" +\n    \"Окно \" +\n    \"Окр \" +\n    \"Окружение \" +\n    \"ОтлИнфДобавить \" +\n    \"ОтлИнфУдалить \" +\n    \"Отчет \" +\n    \"ОтчетАнал \" +\n    \"ОтчетИнт \" +\n    \"ПапкаСуществует \" +\n    \"Пауза \" +\n    \"ПВыборSQL \" +\n    \"ПереименоватьФайл \" +\n    \"Переменные \" +\n    \"ПереместитьФайл \" +\n    \"Подстр \" +\n    \"ПоискПодстр \" +\n    \"ПоискСтр \" +\n    \"ПолучитьИДТаблицы \" +\n    \"ПользовательДополнительно \" +\n    \"ПользовательИД \" +\n    \"ПользовательИмя \" +\n    \"ПользовательСтатус \" +\n    \"Прервать \" +\n    \"ПроверитьПараметр \" +\n    \"ПроверитьПараметрЗнач \" +\n    \"ПроверитьУсловие \" +\n    \"РазбСтр \" +\n    \"РазнВремя \" +\n    \"РазнДат \" +\n    \"РазнДатаВремя \" +\n    \"РазнРабВремя \" +\n    \"РегУстВрем \" +\n    \"РегУстДат \" +\n    \"РегУстЧсл \" +\n    \"РедТекст \" +\n    \"РеестрЗапись \" +\n    \"РеестрСписокИменПарам \" +\n    \"РеестрЧтение \" +\n    \"РеквСпр \" +\n    \"РеквСпрПр \" +\n    \"Сегодня \" +\n    \"Сейчас \" +\n    \"Сервер \" +\n    \"СерверПроцессИД \" +\n    \"СертификатФайлСчитать \" +\n    \"СжПроб \" +\n    \"Символ \" +\n    \"СистемаДиректумКод \" +\n    \"СистемаИнформация \" +\n    \"СистемаКод \" +\n    \"Содержит \" +\n    \"СоединениеЗакрыть \" +\n    \"СоединениеОткрыть \" +\n    \"СоздатьДиалог \" +\n    \"СоздатьДиалогВыбораИзДвухСписков \" +\n    \"СоздатьДиалогВыбораПапки \" +\n    \"СоздатьДиалогОткрытияФайла \" +\n    \"СоздатьДиалогСохраненияФайла \" +\n    \"СоздатьЗапрос \" +\n    \"СоздатьИндикатор \" +\n    \"СоздатьИсключение \" +\n    \"СоздатьКэшированныйСправочник \" +\n    \"СоздатьМассив \" +\n    \"СоздатьНаборДанных \" +\n    \"СоздатьОбъект \" +\n    \"СоздатьОтчет \" +\n    \"СоздатьПапку \" +\n    \"СоздатьРедактор \" +\n    \"СоздатьСоединение \" +\n    \"СоздатьСписок \" +\n    \"СоздатьСписокСтрок \" +\n    \"СоздатьСправочник \" +\n    \"СоздатьСценарий \" +\n    \"СоздСпр \" +\n    \"СостСпр \" +\n    \"Сохр \" +\n    \"СохрСпр \" +\n    \"СписокСистем \" +\n    \"Спр \" +\n    \"Справочник \" +\n    \"СпрБлокЕсть \" +\n    \"СпрБлокСнять \" +\n    \"СпрБлокСнятьРасш \" +\n    \"СпрБлокУстановить \" +\n    \"СпрИзмНабДан \" +\n    \"СпрКод \" +\n    \"СпрНомер \" +\n    \"СпрОбновить \" +\n    \"СпрОткрыть \" +\n    \"СпрОтменить \" +\n    \"СпрПарам \" +\n    \"СпрПолеЗнач \" +\n    \"СпрПолеИмя \" +\n    \"СпрРекв \" +\n    \"СпрРеквВведЗн \" +\n    \"СпрРеквНовые \" +\n    \"СпрРеквПр \" +\n    \"СпрРеквПредЗн \" +\n    \"СпрРеквРежим \" +\n    \"СпрРеквТипТекст \" +\n    \"СпрСоздать \" +\n    \"СпрСост \" +\n    \"СпрСохранить \" +\n    \"СпрТблИтог \" +\n    \"СпрТблСтр \" +\n    \"СпрТблСтрКол \" +\n    \"СпрТблСтрМакс \" +\n    \"СпрТблСтрМин \" +\n    \"СпрТблСтрПред \" +\n    \"СпрТблСтрСлед \" +\n    \"СпрТблСтрСозд \" +\n    \"СпрТблСтрУд \" +\n    \"СпрТекПредст \" +\n    \"СпрУдалить \" +\n    \"СравнитьСтр \" +\n    \"СтрВерхРегистр \" +\n    \"СтрНижнРегистр \" +\n    \"СтрТблСпр \" +\n    \"СумПроп \" +\n    \"Сценарий \" +\n    \"СценарийПарам \" +\n    \"ТекВерсия \" +\n    \"ТекОрг \" +\n    \"Точн \" +\n    \"Тран \" +\n    \"Транслитерация \" +\n    \"УдалитьТаблицу \" +\n    \"УдалитьФайл \" +\n    \"УдСпр \" +\n    \"УдСтрТблСпр \" +\n    \"Уст \" +\n    \"УстановкиКонстант \" +\n    \"ФайлАтрибутСчитать \" +\n    \"ФайлАтрибутУстановить \" +\n    \"ФайлВремя \" +\n    \"ФайлВремяУстановить \" +\n    \"ФайлВыбрать \" +\n    \"ФайлЗанят \" +\n    \"ФайлЗаписать \" +\n    \"ФайлИскать \" +\n    \"ФайлКопировать \" +\n    \"ФайлМожноЧитать \" +\n    \"ФайлОткрыть \" +\n    \"ФайлПереименовать \" +\n    \"ФайлПерекодировать \" +\n    \"ФайлПереместить \" +\n    \"ФайлПросмотреть \" +\n    \"ФайлРазмер \" +\n    \"ФайлСоздать \" +\n    \"ФайлСсылкаСоздать \" +\n    \"ФайлСуществует \" +\n    \"ФайлСчитать \" +\n    \"ФайлУдалить \" +\n    \"ФмтSQLДат \" +\n    \"ФмтДат \" +\n    \"ФмтСтр \" +\n    \"ФмтЧсл \" +\n    \"Формат \" +\n    \"ЦМассивЭлемент \" +\n    \"ЦНаборДанныхРеквизит \" +\n    \"ЦПодстр \";\n\n  // Предопределенные переменные ==> built_in\n  const predefined_variables =\n    \"AltState \" +\n    \"Application \" +\n    \"CallType \" +\n    \"ComponentTokens \" +\n    \"CreatedJobs \" +\n    \"CreatedNotices \" +\n    \"ControlState \" +\n    \"DialogResult \" +\n    \"Dialogs \" +\n    \"EDocuments \" +\n    \"EDocumentVersionSource \" +\n    \"Folders \" +\n    \"GlobalIDs \" +\n    \"Job \" +\n    \"Jobs \" +\n    \"InputValue \" +\n    \"LookUpReference \" +\n    \"LookUpRequisiteNames \" +\n    \"LookUpSearch \" +\n    \"Object \" +\n    \"ParentComponent \" +\n    \"Processes \" +\n    \"References \" +\n    \"Requisite \" +\n    \"ReportName \" +\n    \"Reports \" +\n    \"Result \" +\n    \"Scripts \" +\n    \"Searches \" +\n    \"SelectedAttachments \" +\n    \"SelectedItems \" +\n    \"SelectMode \" +\n    \"Sender \" +\n    \"ServerEvents \" +\n    \"ServiceFactory \" +\n    \"ShiftState \" +\n    \"SubTask \" +\n    \"SystemDialogs \" +\n    \"Tasks \" +\n    \"Wizard \" +\n    \"Wizards \" +\n    \"Work \" +\n    \"ВызовСпособ \" +\n    \"ИмяОтчета \" +\n    \"РеквЗнач \";\n\n  // Интерфейсы ==> type\n  const interfaces =\n    \"IApplication \" +\n    \"IAccessRights \" +\n    \"IAccountRepository \" +\n    \"IAccountSelectionRestrictions \" +\n    \"IAction \" +\n    \"IActionList \" +\n    \"IAdministrationHistoryDescription \" +\n    \"IAnchors \" +\n    \"IApplication \" +\n    \"IArchiveInfo \" +\n    \"IAttachment \" +\n    \"IAttachmentList \" +\n    \"ICheckListBox \" +\n    \"ICheckPointedList \" +\n    \"IColumn \" +\n    \"IComponent \" +\n    \"IComponentDescription \" +\n    \"IComponentToken \" +\n    \"IComponentTokenFactory \" +\n    \"IComponentTokenInfo \" +\n    \"ICompRecordInfo \" +\n    \"IConnection \" +\n    \"IContents \" +\n    \"IControl \" +\n    \"IControlJob \" +\n    \"IControlJobInfo \" +\n    \"IControlList \" +\n    \"ICrypto \" +\n    \"ICrypto2 \" +\n    \"ICustomJob \" +\n    \"ICustomJobInfo \" +\n    \"ICustomListBox \" +\n    \"ICustomObjectWizardStep \" +\n    \"ICustomWork \" +\n    \"ICustomWorkInfo \" +\n    \"IDataSet \" +\n    \"IDataSetAccessInfo \" +\n    \"IDataSigner \" +\n    \"IDateCriterion \" +\n    \"IDateRequisite \" +\n    \"IDateRequisiteDescription \" +\n    \"IDateValue \" +\n    \"IDeaAccessRights \" +\n    \"IDeaObjectInfo \" +\n    \"IDevelopmentComponentLock \" +\n    \"IDialog \" +\n    \"IDialogFactory \" +\n    \"IDialogPickRequisiteItems \" +\n    \"IDialogsFactory \" +\n    \"IDICSFactory \" +\n    \"IDocRequisite \" +\n    \"IDocumentInfo \" +\n    \"IDualListDialog \" +\n    \"IECertificate \" +\n    \"IECertificateInfo \" +\n    \"IECertificates \" +\n    \"IEditControl \" +\n    \"IEditorForm \" +\n    \"IEdmsExplorer \" +\n    \"IEdmsObject \" +\n    \"IEdmsObjectDescription \" +\n    \"IEdmsObjectFactory \" +\n    \"IEdmsObjectInfo \" +\n    \"IEDocument \" +\n    \"IEDocumentAccessRights \" +\n    \"IEDocumentDescription \" +\n    \"IEDocumentEditor \" +\n    \"IEDocumentFactory \" +\n    \"IEDocumentInfo \" +\n    \"IEDocumentStorage \" +\n    \"IEDocumentVersion \" +\n    \"IEDocumentVersionListDialog \" +\n    \"IEDocumentVersionSource \" +\n    \"IEDocumentWizardStep \" +\n    \"IEDocVerSignature \" +\n    \"IEDocVersionState \" +\n    \"IEnabledMode \" +\n    \"IEncodeProvider \" +\n    \"IEncrypter \" +\n    \"IEvent \" +\n    \"IEventList \" +\n    \"IException \" +\n    \"IExternalEvents \" +\n    \"IExternalHandler \" +\n    \"IFactory \" +\n    \"IField \" +\n    \"IFileDialog \" +\n    \"IFolder \" +\n    \"IFolderDescription \" +\n    \"IFolderDialog \" +\n    \"IFolderFactory \" +\n    \"IFolderInfo \" +\n    \"IForEach \" +\n    \"IForm \" +\n    \"IFormTitle \" +\n    \"IFormWizardStep \" +\n    \"IGlobalIDFactory \" +\n    \"IGlobalIDInfo \" +\n    \"IGrid \" +\n    \"IHasher \" +\n    \"IHistoryDescription \" +\n    \"IHyperLinkControl \" +\n    \"IImageButton \" +\n    \"IImageControl \" +\n    \"IInnerPanel \" +\n    \"IInplaceHint \" +\n    \"IIntegerCriterion \" +\n    \"IIntegerList \" +\n    \"IIntegerRequisite \" +\n    \"IIntegerValue \" +\n    \"IISBLEditorForm \" +\n    \"IJob \" +\n    \"IJobDescription \" +\n    \"IJobFactory \" +\n    \"IJobForm \" +\n    \"IJobInfo \" +\n    \"ILabelControl \" +\n    \"ILargeIntegerCriterion \" +\n    \"ILargeIntegerRequisite \" +\n    \"ILargeIntegerValue \" +\n    \"ILicenseInfo \" +\n    \"ILifeCycleStage \" +\n    \"IList \" +\n    \"IListBox \" +\n    \"ILocalIDInfo \" +\n    \"ILocalization \" +\n    \"ILock \" +\n    \"IMemoryDataSet \" +\n    \"IMessagingFactory \" +\n    \"IMetadataRepository \" +\n    \"INotice \" +\n    \"INoticeInfo \" +\n    \"INumericCriterion \" +\n    \"INumericRequisite \" +\n    \"INumericValue \" +\n    \"IObject \" +\n    \"IObjectDescription \" +\n    \"IObjectImporter \" +\n    \"IObjectInfo \" +\n    \"IObserver \" +\n    \"IPanelGroup \" +\n    \"IPickCriterion \" +\n    \"IPickProperty \" +\n    \"IPickRequisite \" +\n    \"IPickRequisiteDescription \" +\n    \"IPickRequisiteItem \" +\n    \"IPickRequisiteItems \" +\n    \"IPickValue \" +\n    \"IPrivilege \" +\n    \"IPrivilegeList \" +\n    \"IProcess \" +\n    \"IProcessFactory \" +\n    \"IProcessMessage \" +\n    \"IProgress \" +\n    \"IProperty \" +\n    \"IPropertyChangeEvent \" +\n    \"IQuery \" +\n    \"IReference \" +\n    \"IReferenceCriterion \" +\n    \"IReferenceEnabledMode \" +\n    \"IReferenceFactory \" +\n    \"IReferenceHistoryDescription \" +\n    \"IReferenceInfo \" +\n    \"IReferenceRecordCardWizardStep \" +\n    \"IReferenceRequisiteDescription \" +\n    \"IReferencesFactory \" +\n    \"IReferenceValue \" +\n    \"IRefRequisite \" +\n    \"IReport \" +\n    \"IReportFactory \" +\n    \"IRequisite \" +\n    \"IRequisiteDescription \" +\n    \"IRequisiteDescriptionList \" +\n    \"IRequisiteFactory \" +\n    \"IRichEdit \" +\n    \"IRouteStep \" +\n    \"IRule \" +\n    \"IRuleList \" +\n    \"ISchemeBlock \" +\n    \"IScript \" +\n    \"IScriptFactory \" +\n    \"ISearchCriteria \" +\n    \"ISearchCriterion \" +\n    \"ISearchDescription \" +\n    \"ISearchFactory \" +\n    \"ISearchFolderInfo \" +\n    \"ISearchForObjectDescription \" +\n    \"ISearchResultRestrictions \" +\n    \"ISecuredContext \" +\n    \"ISelectDialog \" +\n    \"IServerEvent \" +\n    \"IServerEventFactory \" +\n    \"IServiceDialog \" +\n    \"IServiceFactory \" +\n    \"ISignature \" +\n    \"ISignProvider \" +\n    \"ISignProvider2 \" +\n    \"ISignProvider3 \" +\n    \"ISimpleCriterion \" +\n    \"IStringCriterion \" +\n    \"IStringList \" +\n    \"IStringRequisite \" +\n    \"IStringRequisiteDescription \" +\n    \"IStringValue \" +\n    \"ISystemDialogsFactory \" +\n    \"ISystemInfo \" +\n    \"ITabSheet \" +\n    \"ITask \" +\n    \"ITaskAbortReasonInfo \" +\n    \"ITaskCardWizardStep \" +\n    \"ITaskDescription \" +\n    \"ITaskFactory \" +\n    \"ITaskInfo \" +\n    \"ITaskRoute \" +\n    \"ITextCriterion \" +\n    \"ITextRequisite \" +\n    \"ITextValue \" +\n    \"ITreeListSelectDialog \" +\n    \"IUser \" +\n    \"IUserList \" +\n    \"IValue \" +\n    \"IView \" +\n    \"IWebBrowserControl \" +\n    \"IWizard \" +\n    \"IWizardAction \" +\n    \"IWizardFactory \" +\n    \"IWizardFormElement \" +\n    \"IWizardParam \" +\n    \"IWizardPickParam \" +\n    \"IWizardReferenceParam \" +\n    \"IWizardStep \" +\n    \"IWorkAccessRights \" +\n    \"IWorkDescription \" +\n    \"IWorkflowAskableParam \" +\n    \"IWorkflowAskableParams \" +\n    \"IWorkflowBlock \" +\n    \"IWorkflowBlockResult \" +\n    \"IWorkflowEnabledMode \" +\n    \"IWorkflowParam \" +\n    \"IWorkflowPickParam \" +\n    \"IWorkflowReferenceParam \" +\n    \"IWorkState \" +\n    \"IWorkTreeCustomNode \" +\n    \"IWorkTreeJobNode \" +\n    \"IWorkTreeTaskNode \" +\n    \"IXMLEditorForm \" +\n    \"SBCrypto \";\n\n  // built_in : встроенные или библиотечные объекты (константы, перечисления)\n  const BUILTIN = CONSTANTS + ENUMS;\n\n  // class: встроенные наборы значений, системные объекты, фабрики\n  const CLASS = predefined_variables;\n\n  // literal : примитивные типы\n  const LITERAL = \"null true false nil \";\n\n  // number : числа\n  const NUMBERS = {\n    className: \"number\",\n    begin: hljs.NUMBER_RE,\n    relevance: 0\n  };\n\n  // string : строки\n  const STRINGS = {\n    className: \"string\",\n    variants: [\n      {\n        begin: '\"',\n        end: '\"'\n      },\n      {\n        begin: \"'\",\n        end: \"'\"\n      }\n    ]\n  };\n\n  // Токены\n  const DOCTAGS = {\n    className: \"doctag\",\n    begin: \"\\\\b(?:TODO|DONE|BEGIN|END|STUB|CHG|FIXME|NOTE|BUG|XXX)\\\\b\",\n    relevance: 0\n  };\n\n  // Однострочный комментарий\n  const ISBL_LINE_COMMENT_MODE = {\n    className: \"comment\",\n    begin: \"//\",\n    end: \"$\",\n    relevance: 0,\n    contains: [\n      hljs.PHRASAL_WORDS_MODE,\n      DOCTAGS\n    ]\n  };\n\n  // Многострочный комментарий\n  const ISBL_BLOCK_COMMENT_MODE = {\n    className: \"comment\",\n    begin: \"/\\\\*\",\n    end: \"\\\\*/\",\n    relevance: 0,\n    contains: [\n      hljs.PHRASAL_WORDS_MODE,\n      DOCTAGS\n    ]\n  };\n\n  // comment : комментарии\n  const COMMENTS = {\n    variants: [\n      ISBL_LINE_COMMENT_MODE,\n      ISBL_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  // keywords : ключевые слова\n  const KEYWORDS = {\n    $pattern: UNDERSCORE_IDENT_RE,\n    keyword: KEYWORD,\n    built_in: BUILTIN,\n    class: CLASS,\n    literal: LITERAL\n  };\n\n  // methods : методы\n  const METHODS = {\n    begin: \"\\\\.\\\\s*\" + hljs.UNDERSCORE_IDENT_RE,\n    keywords: KEYWORDS,\n    relevance: 0\n  };\n\n  // type : встроенные типы\n  const TYPES = {\n    className: \"type\",\n    begin: \":[ \\\\t]*(\" + interfaces.trim().replace(/\\s/g, \"|\") + \")\",\n    end: \"[ \\\\t]*=\",\n    excludeEnd: true\n  };\n\n  // variables : переменные\n  const VARIABLES = {\n    className: \"variable\",\n    keywords: KEYWORDS,\n    begin: UNDERSCORE_IDENT_RE,\n    relevance: 0,\n    contains: [\n      TYPES,\n      METHODS\n    ]\n  };\n\n  // Имена функций\n  const FUNCTION_TITLE = FUNCTION_NAME_IDENT_RE + \"\\\\(\";\n\n  const TITLE_MODE = {\n    className: \"title\",\n    keywords: {\n      $pattern: UNDERSCORE_IDENT_RE,\n      built_in: system_functions\n    },\n    begin: FUNCTION_TITLE,\n    end: \"\\\\(\",\n    returnBegin: true,\n    excludeEnd: true\n  };\n\n  // function : функции\n  const FUNCTIONS = {\n    className: \"function\",\n    begin: FUNCTION_TITLE,\n    end: \"\\\\)$\",\n    returnBegin: true,\n    keywords: KEYWORDS,\n    illegal: \"[\\\\[\\\\]\\\\|\\\\$\\\\?%,~#@]\",\n    contains: [\n      TITLE_MODE,\n      METHODS,\n      VARIABLES,\n      STRINGS,\n      NUMBERS,\n      COMMENTS\n    ]\n  };\n\n  return {\n    name: 'ISBL',\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    illegal: \"\\\\$|\\\\?|%|,|;$|~|#|@|</\",\n    contains: [\n      FUNCTIONS,\n      TYPES,\n      METHODS,\n      VARIABLES,\n      STRINGS,\n      NUMBERS,\n      COMMENTS\n    ]\n  };\n}\n\nmodule.exports = isbl;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,KAAK,MAAM;AAElB,YAAM,sBAAsB;AAG5B,YAAM,yBAAyB;AAG/B,YAAM,UACJ;AAIF,YAAM,mBACJ;AAwwBF,YAAM,iBAAiB;AAGvB,YAAM,4BACJ;AAGF,YAAM,sCACJ;AAIF,YAAM,2BACJ;AAIF,YAAM,iCACJ;AAuBF,YAAM,0BAA0B;AAGhC,YAAM,4CACJ;AAGF,YAAM,sBACJ;AASF,YAAM,sBACJ;AAGF,YAAM,qCACJ;AAWF,YAAM,oCACJ;AAeF,YAAM,0BACJ;AA+BF,YAAM,0BACJ;AAKF,YAAM,kBACJ;AAoEF,YAAM,uBACJ;AAoBF,YAAM,iCACJ;AAqBF,YAAM,gDACJ;AAKF,YAAM,gDACJ;AAMF,YAAM,uCACJ;AAMF,YAAM,2BACJ;AAoNF,YAAM,mBAAmB;AAGzB,YAAM,gCACJ;AAqBF,YAAM,oCACJ;AAOF,YAAM,qCACJ;AAgCF,YAAM,6BACJ;AAOF,YAAM,2BACJ;AAoBF,YAAM,mCACJ;AA+EF,YAAM,uBACJ;AAKF,YAAM,iBACJ;AAcF,YAAM,qCACJ;AAUF,YAAM,+BACJ;AAKF,YAAM,wCACJ;AAYF,YAAM,kCACJ;AAGF,YAAM,kCACJ;AAQF,YAAM,0BACJ;AAWF,YAAM,YACJ,mBACA,iBACA,4BACA,sCACA,2BACA,iCACA,0BACA,4CACA,sBACA,sBACA,qCACA,oCACA,0BACA,0BACA,kBACA,uBACA,iCACA,gDACA,gDACA,uCACA,2BACA,mBACA,gCACA,oCACA,qCACA,6BACA,2BACA,mCACA,uBACA,iBACA,qCACA,+BACA,wCACA,kCACA,kCACA;AAGF,YAAM,eAAe;AAGrB,YAAM,qBACJ;AAOF,YAAM,eAAe;AAGrB,YAAM,aAAa;AAGnB,YAAM,gBACJ;AAOF,YAAM,iCAAiC;AAGvC,YAAM,mBAAmB;AAGzB,YAAM,yBAAyB;AAG/B,YAAM,cAAc;AAGpB,YAAM,YACJ;AAeF,YAAM,mBAAmB;AAGzB,YAAM,oBAAoB;AAG1B,YAAM,eACJ;AAYF,YAAM,eACJ;AAoCF,YAAM,wBACJ;AASF,YAAM,eAAe;AAGrB,YAAM,oBACJ;AA4BF,YAAM,gBAAgB;AAGtB,YAAM,kBAAkB;AAGxB,YAAM,kBAAkB;AAGxB,YAAM,gBAAgB;AAGtB,YAAM,mBAAmB;AAGzB,YAAM,yBAAyB;AAG/B,YAAM,YACJ;AAgDF,YAAM,yBAAyB;AAG/B,YAAM,yBAAyB;AAG/B,YAAM,qBAAqB;AAG3B,YAAM,yBAAyB;AAG/B,YAAM,4BAA4B;AAGlC,YAAM,4BAA4B;AAGlC,YAAM,wBAAwB;AAG9B,YAAM,8BACJ;AAGF,YAAM,yBAAyB;AAG/B,YAAM,cAAc;AAGpB,YAAM,qBAAqB;AAG3B,YAAM,0BAA0B;AAGhC,YAAM,uBAAuB;AAG7B,YAAM,iBACJ;AAUF,YAAM,cACJ;AAWF,YAAM,iBAAiB;AAGvB,YAAM,iBAAiB;AAGvB,YAAM,mBACJ;AAQF,YAAM,aAAa;AAGnB,YAAM,aAAa;AAGnB,YAAM,mBACJ;AAGF,YAAM,eACJ;AAoDF,YAAM,YAAY;AAGlB,YAAM,WAAW;AAGjB,YAAM,YAAY;AAGlB,YAAM,YAAY;AAGlB,YAAM,iBAAiB;AAGvB,YAAM,2BACJ;AASF,YAAM,2BAA2B;AAGjC,YAAM,oCACJ;AAaF,YAAM,iCACJ;AAGF,YAAM,kBACJ;AAGF,YAAM,gBACJ;AAYF,YAAM,qBAAqB;AAG3B,YAAM,eACJ;AAaF,YAAM,sBAAsB;AAG5B,YAAM,cAAc;AAGpB,YAAM,kBACJ;AAGF,YAAM,cAAc;AAGpB,YAAM,iBAAiB;AAGvB,YAAM,qBAAqB;AAG3B,YAAM,mBAAmB;AAGzB,YAAM,mBAAmB;AAGzB,YAAM,iCACJ;AAuBF,YAAM,mBAAmB;AAGzB,YAAM,iBAAiB;AAGvB,YAAM,oBACJ;AAWF,YAAM,YACJ;AAQF,YAAM,mBACJ;AAGF,YAAM,YAAY;AAGlB,YAAM,qBACJ;AAGF,YAAM,oBACJ;AAGF,YAAM,6BACJ;AAoBF,YAAM,yBACJ;AAGF,YAAM,mBACJ;AAoBF,YAAM,oBACJ;AAQF,YAAM,kBACJ;AAOF,YAAM,kBAAkB;AAGxB,YAAM,qBACJ;AAcF,YAAM,oBACJ;AAoCF,YAAM,kBAAkB;AAGxB,YAAM,iBAAiB;AAGvB,YAAM,aACJ;AAQF,YAAM,wBACJ;AAGF,YAAM,QACJ,eACA,qBACA,eACA,aACA,gBACA,iCACA,mBACA,yBACA,cACA,YACA,mBACA,oBACA,eACA,eACA,wBACA,eACA,oBACA,gBACA,kBACA,kBACA,gBACA,mBACA,yBACA,YACA,yBACA,yBACA,qBACA,yBACA,4BACA,4BACA,wBACA,8BACA,yBACA,cACA,qBACA,0BACA,uBACA,iBACA,cACA,iBACA,iBACA,mBACA,aACA,aACA,mBACA,eACA,YACA,WACA,YACA,YACA,iBACA,2BACA,2BACA,oCACA,iCACA,kBACA,gBACA,qBACA,eACA,sBACA,cACA,kBACA,cACA,iBACA,qBACA,mBACA,mBACA,iCACA,mBACA,iBACA,oBACA,YACA,mBACA,YACA,qBACA,oBACA,6BACA,yBACA,mBACA,oBACA,kBACA,kBACA,qBACA,oBACA,kBACA,iBACA,aACA;AAGF,YAAM,mBACJ;AAkbF,YAAM,uBACJ;AA+CF,YAAM,aACJ;AAyPF,YAAM,UAAU,YAAY;AAG5B,YAAM,QAAQ;AAGd,YAAM,UAAU;AAGhB,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,OAAO,KAAK;AAAA,QACZ,WAAW;AAAA,MACb;AAGA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAGA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAGA,YAAM,yBAAyB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAGA,YAAM,0BAA0B;AAAA,QAC9B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAGA,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,UACR;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAGA,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAGA,YAAM,UAAU;AAAA,QACd,OAAO,YAAY,KAAK;AAAA,QACxB,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAGA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO,cAAc,WAAW,KAAK,EAAE,QAAQ,OAAO,GAAG,IAAI;AAAA,QAC7D,KAAK;AAAA,QACL,YAAY;AAAA,MACd;AAGA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAGA,YAAM,iBAAiB,yBAAyB;AAEhD,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,UAAU;AAAA,UACR,UAAU;AAAA,UACV,UAAU;AAAA,QACZ;AAAA,QACA,OAAO;AAAA,QACP,KAAK;AAAA,QACL,aAAa;AAAA,QACb,YAAY;AAAA,MACd;AAGA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,aAAa;AAAA,QACb,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}