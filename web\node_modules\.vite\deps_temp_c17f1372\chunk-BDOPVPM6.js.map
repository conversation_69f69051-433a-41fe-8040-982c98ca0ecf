{"version": 3, "sources": ["../../refractor/lang/dhall.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = dhall\ndhall.displayName = 'dhall'\ndhall.aliases = []\nfunction dhall(Prism) {\n  // ABNF grammar:\n  // https://github.com/dhall-lang/dhall-lang/blob/master/standard/dhall.abnf\n  Prism.languages.dhall = {\n    // Multi-line comments can be nested. E.g. {- foo {- bar -} -}\n    // The multi-line pattern is essentially this:\n    //   \\{-(?:[^-{]|-(?!\\})|\\{(?!-)|<SELF>)*-\\}\n    comment:\n      /--.*|\\{-(?:[^-{]|-(?!\\})|\\{(?!-)|\\{-(?:[^-{]|-(?!\\})|\\{(?!-))*-\\})*-\\}/,\n    string: {\n      pattern: /\"(?:[^\"\\\\]|\\\\.)*\"|''(?:[^']|'(?!')|'''|''\\$\\{)*''(?!'|\\$)/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$\\{[^{}]*\\}/,\n          inside: {\n            expression: {\n              pattern: /(^\\$\\{)[\\s\\S]+(?=\\}$)/,\n              lookbehind: true,\n              alias: 'language-dhall',\n              inside: null // see blow\n            },\n            punctuation: /\\$\\{|\\}/\n          }\n        }\n      }\n    },\n    label: {\n      pattern: /`[^`]*`/,\n      greedy: true\n    },\n    url: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L596\n      pattern:\n        /\\bhttps?:\\/\\/[\\w.:%!$&'*+;=@~-]+(?:\\/[\\w.:%!$&'*+;=@~-]*)*(?:\\?[/?\\w.:%!$&'*+;=@~-]*)?/,\n      greedy: true\n    },\n    env: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L661\n      pattern: /\\benv:(?:(?!\\d)\\w+|\"(?:[^\"\\\\=]|\\\\.)*\")/,\n      greedy: true,\n      inside: {\n        function: /^env/,\n        operator: /^:/,\n        variable: /[\\s\\S]+/\n      }\n    },\n    hash: {\n      // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L725\n      pattern: /\\bsha256:[\\da-fA-F]{64}\\b/,\n      inside: {\n        function: /sha256/,\n        operator: /:/,\n        number: /[\\da-fA-F]{64}/\n      }\n    },\n    // https://github.com/dhall-lang/dhall-lang/blob/5fde8ef1bead6fb4e999d3c1ffe7044cd019d63a/standard/dhall.abnf#L359\n    keyword:\n      /\\b(?:as|assert|else|forall|if|in|let|merge|missing|then|toMap|using|with)\\b|\\u2200/,\n    builtin: /\\b(?:None|Some)\\b/,\n    boolean: /\\b(?:False|True)\\b/,\n    number:\n      /\\bNaN\\b|-?\\bInfinity\\b|[+-]?\\b(?:0x[\\da-fA-F]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/,\n    operator:\n      /\\/\\\\|\\/\\/\\\\\\\\|&&|\\|\\||===|[!=]=|\\/\\/|->|\\+\\+|::|[+*#@=:?<>|\\\\\\u2227\\u2a53\\u2261\\u2afd\\u03bb\\u2192]/,\n    punctuation: /\\.\\.|[{}\\[\\](),./]/,\n    // we'll just assume that every capital word left is a type name\n    'class-name': /\\b[A-Z]\\w*\\b/\n  }\n  Prism.languages.dhall.string.inside.interpolation.inside.expression.inside =\n    Prism.languages.dhall\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AAGpB,YAAM,UAAU,QAAQ;AAAA;AAAA;AAAA;AAAA,QAItB,SACE;AAAA,QACF,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,YAAY;AAAA,kBACV,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,OAAO;AAAA,kBACP,QAAQ;AAAA;AAAA,gBACV;AAAA,gBACA,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,KAAK;AAAA;AAAA,UAEH,SACE;AAAA,UACF,QAAQ;AAAA,QACV;AAAA,QACA,KAAK;AAAA;AAAA,UAEH,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,MAAM;AAAA;AAAA,UAEJ,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,UAAU;AAAA,YACV,UAAU;AAAA,YACV,QAAQ;AAAA,UACV;AAAA,QACF;AAAA;AAAA,QAEA,SACE;AAAA,QACF,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QACE;AAAA,QACF,UACE;AAAA,QACF,aAAa;AAAA;AAAA,QAEb,cAAc;AAAA,MAChB;AACA,YAAM,UAAU,MAAM,OAAO,OAAO,cAAc,OAAO,WAAW,SAClE,MAAM,UAAU;AAAA,IACpB;AAAA;AAAA;", "names": []}