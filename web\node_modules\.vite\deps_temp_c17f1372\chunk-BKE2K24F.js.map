{"version": 3, "sources": ["../../highlight.js/lib/languages/axapta.js"], "sourcesContent": ["/*\nLanguage: Microsoft X++\nDescription: X++ is a language used in Microsoft Dynamics 365, Dynamics AX, and Axapta.\nAuthor: <PERSON><PERSON><PERSON> <d<PERSON><PERSON>@roudakov.ru>\nWebsite: https://dynamics.microsoft.com/en-us/ax-overview/\nCategory: enterprise\n*/\n\n/** @type LanguageFn */\nfunction axapta(hljs) {\n  const BUILT_IN_KEYWORDS = [\n    'anytype',\n    'boolean',\n    'byte',\n    'char',\n    'container',\n    'date',\n    'double',\n    'enum',\n    'guid',\n    'int',\n    'int64',\n    'long',\n    'real',\n    'short',\n    'str',\n    'utcdatetime',\n    'var'\n  ];\n\n  const LITERAL_KEYWORDS = [\n    'default',\n    'false',\n    'null',\n    'true'\n  ];\n\n  const NORMAL_KEYWORDS = [\n    'abstract',\n    'as',\n    'asc',\n    'avg',\n    'break',\n    'breakpoint',\n    'by',\n    'byref',\n    'case',\n    'catch',\n    'changecompany',\n    'class',\n    'client',\n    'client',\n    'common',\n    'const',\n    'continue',\n    'count',\n    'crosscompany',\n    'delegate',\n    'delete_from',\n    'desc',\n    'display',\n    'div',\n    'do',\n    'edit',\n    'else',\n    'eventhandler',\n    'exists',\n    'extends',\n    'final',\n    'finally',\n    'firstfast',\n    'firstonly',\n    'firstonly1',\n    'firstonly10',\n    'firstonly100',\n    'firstonly1000',\n    'flush',\n    'for',\n    'forceliterals',\n    'forcenestedloop',\n    'forceplaceholders',\n    'forceselectorder',\n    'forupdate',\n    'from',\n    'generateonly',\n    'group',\n    'hint',\n    'if',\n    'implements',\n    'in',\n    'index',\n    'insert_recordset',\n    'interface',\n    'internal',\n    'is',\n    'join',\n    'like',\n    'maxof',\n    'minof',\n    'mod',\n    'namespace',\n    'new',\n    'next',\n    'nofetch',\n    'notexists',\n    'optimisticlock',\n    'order',\n    'outer',\n    'pessimisticlock',\n    'print',\n    'private',\n    'protected',\n    'public',\n    'readonly',\n    'repeatableread',\n    'retry',\n    'return',\n    'reverse',\n    'select',\n    'server',\n    'setting',\n    'static',\n    'sum',\n    'super',\n    'switch',\n    'this',\n    'throw',\n    'try',\n    'ttsabort',\n    'ttsbegin',\n    'ttscommit',\n    'unchecked',\n    'update_recordset',\n    'using',\n    'validtimestate',\n    'void',\n    'where',\n    'while'\n  ];\n\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS,\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n\n  return {\n    name: 'X++',\n    aliases: ['x++'],\n    keywords: KEYWORDS,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$'\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /\\{/,\n        excludeEnd: true,\n        illegal: ':',\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = axapta;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,MAAM;AACpB,YAAM,oBAAoB;AAAA,QACxB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,KAAK;AAAA,QACf,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,cACjB;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}