{"version": 3, "sources": ["../../refractor/lang/tsx.js"], "sourcesContent": ["'use strict'\nvar refractorJsx = require('./jsx.js')\nvar refractorTypescript = require('./typescript.js')\nmodule.exports = tsx\ntsx.displayName = 'tsx'\ntsx.aliases = []\nfunction tsx(Prism) {\n  Prism.register(refractorJsx)\n  Prism.register(refractorTypescript)\n  ;(function (Prism) {\n    var typescript = Prism.util.clone(Prism.languages.typescript)\n    Prism.languages.tsx = Prism.languages.extend('jsx', typescript) // doesn't work with TS because TS is too complex\n    delete Prism.languages.tsx['parameter']\n    delete Prism.languages.tsx['literal-property'] // This will prevent collisions between TSX tags and TS generic types.\n    // Idea by https://github.com/karlhorky\n    // Discussion: https://github.com/PrismJS/prism/issues/2594#issuecomment-*********\n    var tag = Prism.languages.tsx.tag\n    tag.pattern = RegExp(\n      /(^|[^\\w$]|(?=<\\/))/.source + '(?:' + tag.pattern.source + ')',\n      tag.pattern.flags\n    )\n    tag.lookbehind = true\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AACA,QAAI,eAAe;AACnB,QAAI,sBAAsB;AAC1B,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,SAAS,YAAY;AAC3B,YAAM,SAAS,mBAAmB;AACjC,OAAC,SAAUA,QAAO;AACjB,YAAI,aAAaA,OAAM,KAAK,MAAMA,OAAM,UAAU,UAAU;AAC5D,QAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,OAAO,UAAU;AAC9D,eAAOA,OAAM,UAAU,IAAI,WAAW;AACtC,eAAOA,OAAM,UAAU,IAAI,kBAAkB;AAG7C,YAAI,MAAMA,OAAM,UAAU,IAAI;AAC9B,YAAI,UAAU;AAAA,UACZ,qBAAqB,SAAS,QAAQ,IAAI,QAAQ,SAAS;AAAA,UAC3D,IAAI,QAAQ;AAAA,QACd;AACA,YAAI,aAAa;AAAA,MACnB,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}