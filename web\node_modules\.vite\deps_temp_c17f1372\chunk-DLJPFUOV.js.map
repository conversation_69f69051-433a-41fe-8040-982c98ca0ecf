{"version": 3, "sources": ["../../refractor/lang/jq.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jq\njq.displayName = 'jq'\njq.aliases = []\nfunction jq(Prism) {\n  ;(function (Prism) {\n    var interpolation = /\\\\\\((?:[^()]|\\([^()]*\\))*\\)/.source\n    var string = RegExp(\n      /(^|[^\\\\])\"(?:[^\"\\r\\n\\\\]|\\\\[^\\r\\n(]|__)*\"/.source.replace(\n        /__/g,\n        function () {\n          return interpolation\n        }\n      )\n    )\n    var stringInterpolation = {\n      interpolation: {\n        pattern: RegExp(/((?:^|[^\\\\])(?:\\\\{2})*)/.source + interpolation),\n        lookbehind: true,\n        inside: {\n          content: {\n            pattern: /^(\\\\\\()[\\s\\S]+(?=\\)$)/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          punctuation: /^\\\\\\(|\\)$/\n        }\n      }\n    }\n    var jq = (Prism.languages.jq = {\n      comment: /#.*/,\n      property: {\n        pattern: RegExp(string.source + /(?=\\s*:(?!:))/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: stringInterpolation\n      },\n      string: {\n        pattern: string,\n        lookbehind: true,\n        greedy: true,\n        inside: stringInterpolation\n      },\n      function: {\n        pattern: /(\\bdef\\s+)[a-z_]\\w+/i,\n        lookbehind: true\n      },\n      variable: /\\B\\$\\w+/,\n      'property-literal': {\n        pattern: /\\b[a-z_]\\w*(?=\\s*:(?!:))/i,\n        alias: 'property'\n      },\n      keyword:\n        /\\b(?:as|break|catch|def|elif|else|end|foreach|if|import|include|label|module|modulemeta|null|reduce|then|try|while)\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      number: /(?:\\b\\d+\\.|\\B\\.)?\\b\\d+(?:[eE][+-]?\\d+)?\\b/,\n      operator: [\n        {\n          pattern: /\\|=?/,\n          alias: 'pipe'\n        },\n        /\\.\\.|[!=<>]?=|\\?\\/\\/|\\/\\/=?|[-+*/%]=?|[<>?]|\\b(?:and|not|or)\\b/\n      ],\n      'c-style-function': {\n        pattern: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n        alias: 'function'\n      },\n      punctuation: /::|[()\\[\\]{},:;]|\\.(?=\\s*[\\[\\w$])/,\n      dot: {\n        pattern: /\\./,\n        alias: 'important'\n      }\n    })\n    stringInterpolation.interpolation.inside.content.inside = jq\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,OAAG,cAAc;AACjB,OAAG,UAAU,CAAC;AACd,aAAS,GAAG,OAAO;AACjB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,gBAAgB,8BAA8B;AAClD,YAAI,SAAS;AAAA,UACX,2CAA2C,OAAO;AAAA,YAChD;AAAA,YACA,WAAY;AACV,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AACA,YAAI,sBAAsB;AAAA,UACxB,eAAe;AAAA,YACb,SAAS,OAAO,0BAA0B,SAAS,aAAa;AAAA,YAChE,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA;AAAA,cACV;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AACA,YAAIC,MAAMD,OAAM,UAAU,KAAK;AAAA,UAC7B,SAAS;AAAA,UACT,UAAU;AAAA,YACR,SAAS,OAAO,OAAO,SAAS,gBAAgB,MAAM;AAAA,YACtD,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,UAAU;AAAA,UACV,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,SACE;AAAA,UACF,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,UAAU;AAAA,YACR;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA;AAAA,UACF;AAAA,UACA,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,aAAa;AAAA,UACb,KAAK;AAAA,YACH,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF;AACA,4BAAoB,cAAc,OAAO,QAAQ,SAASC;AAAA,MAC5D,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism", "jq"]}