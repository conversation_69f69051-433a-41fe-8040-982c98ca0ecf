{"version": 3, "sources": ["../../refractor/lang/false.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = $false\n$false.displayName = '$false'\n$false.aliases = []\nfunction $false(Prism) {\n  ;(function (Prism) {\n    /**\n     * Based on the manual by <PERSON><PERSON><PERSON>.\n     *\n     * @see {@link https://github.com/PrismJS/prism/issues/2801#issue-829717504}\n     */\n    Prism.languages['false'] = {\n      comment: {\n        pattern: /\\{[^}]*\\}/\n      },\n      string: {\n        pattern: /\"[^\"]*\"/,\n        greedy: true\n      },\n      'character-code': {\n        pattern: /'(?:[^\\r]|\\r\\n?)/,\n        alias: 'number'\n      },\n      'assembler-code': {\n        pattern: /\\d+`/,\n        alias: 'important'\n      },\n      number: /\\d+/,\n      operator: /[-!#$%&'*+,./:;=>?@\\\\^_`|~ßø]/,\n      punctuation: /\\[|\\]/,\n      variable: /[a-z]/,\n      'non-standard': {\n        pattern: /[()<BDO®]/,\n        alias: 'bold'\n      }\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB;AAAC,OAAC,SAAUA,QAAO;AAMjB,QAAAA,OAAM,UAAU,OAAO,IAAI;AAAA,UACzB,SAAS;AAAA,YACP,SAAS;AAAA,UACX;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,kBAAkB;AAAA,YAChB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,kBAAkB;AAAA,YAChB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,aAAa;AAAA,UACb,UAAU;AAAA,UACV,gBAAgB;AAAA,YACd,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}