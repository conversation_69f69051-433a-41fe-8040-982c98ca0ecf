{"version": 3, "sources": ["../../highlight.js/lib/languages/aspectj.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: AspectJ\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.eclipse.org/aspectj/\nDescription: Syntax Highlighting for the AspectJ Language which is a general-purpose aspect-oriented extension to the Java programming language.\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction aspectj(hljs) {\n  const KEYWORDS =\n    'false synchronized int abstract float private char boolean static null if const ' +\n    'for true while long throw strictfp finally protected import native final return void ' +\n    'enum else extends implements break transient new catch instanceof byte super volatile case ' +\n    'assert short package default double public try this switch continue throws privileged ' +\n    'aspectOf adviceexecution proceed cflowbelow cflow initialization preinitialization ' +\n    'staticinitialization withincode target within execution getWithinTypeName handler ' +\n    'thisJoinPoint thisJoinPointStaticPart thisEnclosingJoinPointStaticPart declare parents ' +\n    'warning error soft precedence thisAspectInstance';\n  const SHORTKEYS = 'get set args call';\n\n  return {\n    name: 'AspectJ',\n    keywords: KEYWORDS,\n    illegal: /<\\/|#/,\n    contains: [\n      hljs.COMMENT(\n        /\\/\\*\\*/,\n        /\\*\\//,\n        {\n          relevance: 0,\n          contains: [\n            {\n              // eat up @'s in emails to prevent them to be recognized as doctags\n              begin: /\\w+@/,\n              relevance: 0\n            },\n            {\n              className: 'doctag',\n              begin: /@[A-Za-z]+/\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'aspect',\n        end: /[{;=]/,\n        excludeEnd: true,\n        illegal: /[:;\"\\[\\]]/,\n        contains: [\n          {\n            beginKeywords: 'extends implements pertypewithin perthis pertarget percflowbelow percflow issingleton'\n          },\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            begin: /\\([^\\)]*/,\n            end: /[)]+/,\n            keywords: KEYWORDS + ' ' + SHORTKEYS,\n            excludeEnd: false\n          }\n        ]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /[{;=]/,\n        excludeEnd: true,\n        relevance: 0,\n        keywords: 'class interface',\n        illegal: /[:\"\\[\\]]/,\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        // AspectJ Constructs\n        beginKeywords: 'pointcut after before around throwing returning',\n        end: /[)]/,\n        excludeEnd: false,\n        illegal: /[\"\\[\\]]/,\n        contains: [\n          {\n            begin: concat(hljs.UNDERSCORE_IDENT_RE, /\\s*\\(/),\n            returnBegin: true,\n            contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n          }\n        ]\n      },\n      {\n        begin: /[:]/,\n        returnBegin: true,\n        end: /[{;]/,\n        relevance: 0,\n        excludeEnd: false,\n        keywords: KEYWORDS,\n        illegal: /[\"\\[\\]]/,\n        contains: [\n          {\n            begin: concat(hljs.UNDERSCORE_IDENT_RE, /\\s*\\(/),\n            keywords: KEYWORDS + ' ' + SHORTKEYS,\n            relevance: 0\n          },\n          hljs.QUOTE_STRING_MODE\n        ]\n      },\n      {\n        // this prevents 'new Name(...), or throw ...' from being recognized as a function definition\n        beginKeywords: 'new throw',\n        relevance: 0\n      },\n      {\n        // the function class is a bit different for AspectJ compared to the Java language\n        className: 'function',\n        begin: /\\w+ +\\w+(\\.\\w+)?\\s*\\([^\\)]*\\)\\s*((throws)[\\w\\s,]+)?[\\{;]/,\n        returnBegin: true,\n        end: /[{;=]/,\n        keywords: KEYWORDS,\n        excludeEnd: true,\n        contains: [\n          {\n            begin: concat(hljs.UNDERSCORE_IDENT_RE, /\\s*\\(/),\n            returnBegin: true,\n            relevance: 0,\n            contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n          },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            relevance: 0,\n            keywords: KEYWORDS,\n            contains: [\n              hljs.APOS_STRING_MODE,\n              hljs.QUOTE_STRING_MODE,\n              hljs.C_NUMBER_MODE,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      hljs.C_NUMBER_MODE,\n      {\n        // annotation is also used in this language\n        className: 'meta',\n        begin: /@[A-Za-z]+/\n      }\n    ]\n  };\n}\n\nmodule.exports = aspectj;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAWA,aAAS,QAAQ,MAAM;AACrB,YAAM,WACJ;AAQF,YAAM,YAAY;AAElB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA,gBACR;AAAA;AAAA,kBAEE,OAAO;AAAA,kBACP,WAAW;AAAA,gBACb;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,cACjB;AAAA,cACA,KAAK;AAAA,cACL;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU,WAAW,MAAM;AAAA,gBAC3B,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,UAAU;AAAA,YACV,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,cACjB;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,OAAO,KAAK,qBAAqB,OAAO;AAAA,gBAC/C,aAAa;AAAA,gBACb,UAAU,CAAE,KAAK,qBAAsB;AAAA,cACzC;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,aAAa;AAAA,YACb,KAAK;AAAA,YACL,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,OAAO,KAAK,qBAAqB,OAAO;AAAA,gBAC/C,UAAU,WAAW,MAAM;AAAA,gBAC3B,WAAW;AAAA,cACb;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,eAAe;AAAA,YACf,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,aAAa;AAAA,YACb,KAAK;AAAA,YACL,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,OAAO,KAAK,qBAAqB,OAAO;AAAA,gBAC/C,aAAa;AAAA,gBACb,WAAW;AAAA,gBACX,UAAU,CAAE,KAAK,qBAAsB;AAAA,cACzC;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,WAAW;AAAA,gBACX,UAAU;AAAA,gBACV,UAAU;AAAA,kBACR,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}