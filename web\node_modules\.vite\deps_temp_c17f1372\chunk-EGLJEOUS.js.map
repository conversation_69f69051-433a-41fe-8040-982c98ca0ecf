{"version": 3, "sources": ["../../refractor/lang/keepalived.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = keepalived\nkeepalived.displayName = 'keepalived'\nkeepalived.aliases = []\nfunction keepalived(Prism) {\n  Prism.languages.keepalived = {\n    comment: {\n      pattern: /[#!].*/,\n      greedy: true\n    },\n    string: {\n      pattern:\n        /(^|[^\\\\])(?:\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"|'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n])*')/,\n      lookbehind: true,\n      greedy: true\n    },\n    // support IPv4, IPv6, subnet mask\n    ip: {\n      pattern: RegExp(\n        /\\b(?:(?:(?:[\\da-f]{1,4}:){7}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){6}:[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){5}:(?:[\\da-f]{1,4}:)?[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){4}:(?:[\\da-f]{1,4}:){0,2}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){3}:(?:[\\da-f]{1,4}:){0,3}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){2}:(?:[\\da-f]{1,4}:){0,4}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){6}<ipv4>|(?:[\\da-f]{1,4}:){0,5}:<ipv4>|::(?:[\\da-f]{1,4}:){0,5}<ipv4>|[\\da-f]{1,4}::(?:[\\da-f]{1,4}:){0,5}[\\da-f]{1,4}|::(?:[\\da-f]{1,4}:){0,6}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,7}:)(?:\\/\\d{1,3})?|<ipv4>(?:\\/\\d{1,2})?)\\b/.source.replace(\n          /<ipv4>/g,\n          function () {\n            return /(?:(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d))/\n              .source\n          }\n        ),\n        'i'\n      ),\n      alias: 'number'\n    },\n    // support *nix / Windows, directory / file\n    path: {\n      pattern:\n        /(\\s)\\/(?:[^\\/\\s]+\\/)*[^\\/\\s]*|\\b[a-zA-Z]:\\\\(?:[^\\\\\\s]+\\\\)*[^\\\\\\s]*/,\n      lookbehind: true,\n      alias: 'string'\n    },\n    variable: /\\$\\{?\\w+\\}?/,\n    email: {\n      pattern: /[\\w-]+@[\\w-]+(?:\\.[\\w-]{2,3}){1,2}/,\n      alias: 'string'\n    },\n    'conditional-configuration': {\n      pattern: /@\\^?[\\w-]+/,\n      alias: 'variable'\n    },\n    operator: /=/,\n    property:\n      /\\b(?:BFD_CHECK|DNS_CHECK|FILE_CHECK|HTTP_GET|MISC_CHECK|NAME|PING_CHECK|SCRIPTS|SMTP_CHECK|SSL|SSL_GET|TCP_CHECK|UDP_CHECK|accept|advert_int|alpha|auth_pass|auth_type|authentication|bfd_cpu_affinity|bfd_instance|bfd_no_swap|bfd_priority|bfd_process_name|bfd_rlimit_rttime|bfd_rt_priority|bind_if|bind_port|bindto|ca|certificate|check_unicast_src|checker|checker_cpu_affinity|checker_log_all_failures|checker_no_swap|checker_priority|checker_rlimit_rttime|checker_rt_priority|child_wait_time|connect_ip|connect_port|connect_timeout|dbus_service_name|debug|default_interface|delay|delay_before_retry|delay_loop|digest|dont_track_primary|dynamic|dynamic_interfaces|enable_(?:dbus|script_security|sni|snmp_checker|snmp_rfc|snmp_rfcv2|snmp_rfcv3|snmp_vrrp|traps)|end|fall|fast_recovery|file|flag-[123]|fork_delay|full_command|fwmark|garp_group|garp_interval|garp_lower_prio_delay|garp_lower_prio_repeat|garp_master_delay|garp_master_refresh|garp_master_refresh_repeat|garp_master_repeat|global_defs|global_tracking|gna_interval|group|ha_suspend|hashed|helo_name|higher_prio_send_advert|hoplimit|http_protocol|hysteresis|idle_tx|include|inhibit_on_failure|init_fail|init_file|instance|interface|interfaces|interval|ip_family|ipvs_process_name|keepalived.conf|kernel_rx_buf_size|key|linkbeat_interfaces|linkbeat_use_polling|log_all_failures|log_unknown_vrids|lower_prio_no_advert|lthreshold|lvs_flush|lvs_flush_onstop|lvs_method|lvs_netlink_cmd_rcv_bufs|lvs_netlink_cmd_rcv_bufs_force|lvs_netlink_monitor_rcv_bufs|lvs_netlink_monitor_rcv_bufs_force|lvs_notify_fifo|lvs_notify_fifo_script|lvs_sched|lvs_sync_daemon|max_auto_priority|max_hops|mcast_src_ip|mh-fallback|mh-port|min_auto_priority_delay|min_rx|min_tx|misc_dynamic|misc_path|misc_timeout|multiplier|name|namespace_with_ipsets|native_ipv6|neighbor_ip|net_namespace|net_namespace_ipvs|nftables|nftables_counters|nftables_ifindex|nftables_priority|no_accept|no_checker_emails|no_email_faults|nopreempt|notification_email|notification_email_from|notify|notify_backup|notify_deleted|notify_down|notify_fault|notify_fifo|notify_fifo_script|notify_master|notify_master_rx_lower_pri|notify_priority_changes|notify_stop|notify_up|old_unicast_checksum|omega|ops|param_match|passive|password|path|persistence_engine|persistence_granularity|persistence_timeout|preempt|preempt_delay|priority|process|process_monitor_rcv_bufs|process_monitor_rcv_bufs_force|process_name|process_names|promote_secondaries|protocol|proxy_arp|proxy_arp_pvlan|quorum|quorum_down|quorum_max|quorum_up|random_seed|real_server|regex|regex_max_offset|regex_min_offset|regex_no_match|regex_options|regex_stack|reload_repeat|reload_time_file|require_reply|retry|rise|router_id|rs_init_notifies|script|script_user|sh-fallback|sh-port|shutdown_script|shutdown_script_timeout|skip_check_adv_addr|smtp_alert|smtp_alert_checker|smtp_alert_vrrp|smtp_connect_timeout|smtp_helo_name|smtp_server|snmp_socket|sorry_server|sorry_server_inhibit|sorry_server_lvs_method|source_ip|start|startup_script|startup_script_timeout|state|static_ipaddress|static_routes|static_rules|status_code|step|strict_mode|sync_group_tracking_weight|terminate_delay|timeout|track_bfd|track_file|track_group|track_interface|track_process|track_script|track_src_ip|ttl|type|umask|unicast_peer|unicast_src_ip|unicast_ttl|url|use_ipvlan|use_pid_dir|use_vmac|user|uthreshold|val[123]|version|virtual_ipaddress|virtual_ipaddress_excluded|virtual_router_id|virtual_routes|virtual_rules|virtual_server|virtual_server_group|virtualhost|vmac_xmit_base|vrrp|vrrp_(?:check_unicast_src|cpu_affinity|garp_interval|garp_lower_prio_delay|garp_lower_prio_repeat|garp_master_delay|garp_master_refresh|garp_master_refresh_repeat|garp_master_repeat|gna_interval|higher_prio_send_advert|instance|ipsets|iptables|lower_prio_no_advert|mcast_group4|mcast_group6|min_garp|netlink_cmd_rcv_bufs|netlink_cmd_rcv_bufs_force|netlink_monitor_rcv_bufs|netlink_monitor_rcv_bufs_force|no_swap|notify_fifo|notify_fifo_script|notify_priority_changes|priority|process_name|rlimit_rttime|rt_priority|rx_bufs_multiplier|rx_bufs_policy|script|skip_check_adv_addr|startup_delay|strict|sync_group|track_process|version)|warmup|weight)\\b/,\n    constant:\n      /\\b(?:A|AAAA|AH|BACKUP|CNAME|DR|MASTER|MX|NAT|NS|PASS|SCTP|SOA|TCP|TUN|TXT|UDP|dh|fo|lblc|lblcr|lc|mh|nq|ovf|rr|sed|sh|wlc|wrr)\\b/,\n    number: {\n      pattern: /(^|[^\\w.-])-?\\d+(?:\\.\\d+)?/,\n      lookbehind: true\n    },\n    boolean: /\\b(?:false|no|off|on|true|yes)\\b/,\n    punctuation: /[\\{\\}]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,eAAW,cAAc;AACzB,eAAW,UAAU,CAAC;AACtB,aAAS,WAAW,OAAO;AACzB,YAAM,UAAU,aAAa;AAAA,QAC3B,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA;AAAA,QAEA,IAAI;AAAA,UACF,SAAS;AAAA,YACP,shBAAshB,OAAO;AAAA,cAC3hB;AAAA,cACA,WAAY;AACV,uBAAO,0FACJ;AAAA,cACL;AAAA,YACF;AAAA,YACA;AAAA,UACF;AAAA,UACA,OAAO;AAAA,QACT;AAAA;AAAA,QAEA,MAAM;AAAA,UACJ,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,6BAA6B;AAAA,UAC3B,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,UACE;AAAA,QACF,UACE;AAAA,QACF,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}