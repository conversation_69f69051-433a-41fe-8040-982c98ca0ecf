{"version": 3, "sources": ["../../refractor/lang/reason.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = reason\nreason.displayName = 'reason'\nreason.aliases = []\nfunction reason(Prism) {\n  Prism.languages.reason = Prism.languages.extend('clike', {\n    string: {\n      pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n\"])*\"/,\n      greedy: true\n    },\n    // 'class-name' must be matched *after* 'constructor' defined below\n    'class-name': /\\b[A-Z]\\w*/,\n    keyword:\n      /\\b(?:and|as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|method|module|mutable|new|nonrec|object|of|open|or|private|rec|sig|struct|switch|then|to|try|type|val|virtual|when|while|with)\\b/,\n    operator:\n      /\\.{3}|:[:=]|\\|>|->|=(?:==?|>)?|<=?|>=?|[|^?'#!~`]|[+\\-*\\/]\\.?|\\b(?:asr|land|lor|lsl|lsr|lxor|mod)\\b/\n  })\n  Prism.languages.insertBefore('reason', 'class-name', {\n    char: {\n      pattern: /'(?:\\\\x[\\da-f]{2}|\\\\o[0-3][0-7][0-7]|\\\\\\d{3}|\\\\.|[^'\\\\\\r\\n])'/,\n      greedy: true\n    },\n    // Negative look-ahead prevents from matching things like String.capitalize\n    constructor: /\\b[A-Z]\\w*\\b(?!\\s*\\.)/,\n    label: {\n      pattern: /\\b[a-z]\\w*(?=::)/,\n      alias: 'symbol'\n    }\n  }) // We can't match functions property, so let's not even try.\n  delete Prism.languages.reason.function\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS,MAAM,UAAU,OAAO,SAAS;AAAA,QACvD,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA;AAAA,QAEA,cAAc;AAAA,QACd,SACE;AAAA,QACF,UACE;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,aAAa,UAAU,cAAc;AAAA,QACnD,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA;AAAA,QAEA,aAAa;AAAA,QACb,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,aAAO,MAAM,UAAU,OAAO;AAAA,IAChC;AAAA;AAAA;", "names": []}