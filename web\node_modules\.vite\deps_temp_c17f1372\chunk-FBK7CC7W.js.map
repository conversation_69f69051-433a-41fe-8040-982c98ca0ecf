{"version": 3, "sources": ["../../highlight.js/lib/languages/ebnf.js"], "sourcesContent": ["/*\nLanguage: Extended Backus-Naur Form\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Extended_Backus–Naur_form\n*/\n\n/** @type LanguageFn */\nfunction ebnf(hljs) {\n  const commentMode = hljs.COMMENT(/\\(\\*/, /\\*\\)/);\n\n  const nonTerminalMode = {\n    className: \"attribute\",\n    begin: /^[ ]*[a-zA-Z]+([\\s_-]+[a-zA-Z]+)*/\n  };\n\n  const specialSequenceMode = {\n    className: \"meta\",\n    begin: /\\?.*\\?/\n  };\n\n  const ruleBodyMode = {\n    begin: /=/,\n    end: /[.;]/,\n    contains: [\n      commentMode,\n      specialSequenceMode,\n      {\n        // terminals\n        className: 'string',\n        variants: [\n          hljs.APOS_STRING_MODE,\n          hljs.QUOTE_STRING_MODE,\n          {\n            begin: '`',\n            end: '`'\n          }\n        ]\n      }\n    ]\n  };\n\n  return {\n    name: 'Extended Backus-Naur Form',\n    illegal: /\\S/,\n    contains: [\n      commentMode,\n      nonTerminalMode,\n      ruleBodyMode\n    ]\n  };\n}\n\nmodule.exports = ebnf;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,KAAK,MAAM;AAClB,YAAM,cAAc,KAAK,QAAQ,QAAQ,MAAM;AAE/C,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,sBAAsB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,eAAe;AAAA,QACnB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,UAAU;AAAA,cACR,KAAK;AAAA,cACL,KAAK;AAAA,cACL;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}