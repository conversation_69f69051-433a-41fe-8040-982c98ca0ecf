{"version": 3, "sources": ["../../refractor/lang/turtle.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = turtle\nturtle.displayName = 'turtle'\nturtle.aliases = []\nfunction turtle(Prism) {\n  Prism.languages.turtle = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    'multiline-string': {\n      pattern:\n        /\"\"\"(?:(?:\"\"?)?(?:[^\"\\\\]|\\\\.))*\"\"\"|'''(?:(?:''?)?(?:[^'\\\\]|\\\\.))*'''/,\n      greedy: true,\n      alias: 'string',\n      inside: {\n        comment: /#.*/\n      }\n    },\n    string: {\n      pattern: /\"(?:[^\\\\\"\\r\\n]|\\\\.)*\"|'(?:[^\\\\'\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    url: {\n      pattern:\n        /<(?:[^\\x00-\\x20<>\"{}|^`\\\\]|\\\\(?:u[\\da-fA-F]{4}|U[\\da-fA-F]{8}))*>/,\n      greedy: true,\n      inside: {\n        punctuation: /[<>]/\n      }\n    },\n    function: {\n      pattern:\n        /(?:(?![-.\\d\\xB7])[-.\\w\\xB7\\xC0-\\uFFFD]+)?:(?:(?![-.])(?:[-.:\\w\\xC0-\\uFFFD]|%[\\da-f]{2}|\\\\.)+)?/i,\n      inside: {\n        'local-name': {\n          pattern: /([^:]*:)[\\s\\S]+/,\n          lookbehind: true\n        },\n        prefix: {\n          pattern: /[\\s\\S]+/,\n          inside: {\n            punctuation: /:/\n          }\n        }\n      }\n    },\n    number: /[+-]?\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n    punctuation: /[{}.,;()[\\]]|\\^\\^/,\n    boolean: /\\b(?:false|true)\\b/,\n    keyword: [/(?:\\ba|@prefix|@base)\\b|=/, /\\b(?:base|graph|prefix)\\b/i],\n    tag: {\n      pattern: /@[a-z]+(?:-[a-z\\d]+)*/i,\n      inside: {\n        punctuation: /@/\n      }\n    }\n  }\n  Prism.languages.trig = Prism.languages['turtle']\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS;AAAA,QACvB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,oBAAoB;AAAA,UAClB,SACE;AAAA,UACF,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,KAAK;AAAA,UACH,SACE;AAAA,UACF,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SACE;AAAA,UACF,QAAQ;AAAA,YACN,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,SAAS;AAAA,QACT,SAAS,CAAC,6BAA6B,4BAA4B;AAAA,QACnE,KAAK;AAAA,UACH,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU,OAAO,MAAM,UAAU,QAAQ;AAAA,IACjD;AAAA;AAAA;", "names": []}