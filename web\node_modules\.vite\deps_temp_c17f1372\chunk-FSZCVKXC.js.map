{"version": 3, "sources": ["../../refractor/lang/mizar.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = mizar\nmizar.displayName = 'mizar'\nmizar.aliases = []\nfunction mizar(Prism) {\n  Prism.languages.mizar = {\n    comment: /::.+/,\n    keyword:\n      /@proof\\b|\\b(?:according|aggregate|all|and|antonym|are|as|associativity|assume|asymmetry|attr|be|begin|being|by|canceled|case|cases|clusters?|coherence|commutativity|compatibility|connectedness|consider|consistency|constructors|contradiction|correctness|def|deffunc|define|definitions?|defpred|do|does|end|environ|equals|ex|exactly|existence|for|from|func|given|hence|hereby|holds|idempotence|identity|iff?|implies|involutiveness|irreflexivity|is|it|let|means|mode|non|not|notations?|now|of|or|otherwise|over|per|pred|prefix|projectivity|proof|provided|qua|reconsider|redefine|reduce|reducibility|reflexivity|registrations?|requirements|reserve|sch|schemes?|section|selector|set|sethood|st|struct|such|suppose|symmetry|synonym|take|that|the|then|theorems?|thesis|thus|to|transitivity|uniqueness|vocabular(?:ies|y)|when|where|with|wrt)\\b/,\n    parameter: {\n      pattern: /\\$(?:10|\\d)/,\n      alias: 'variable'\n    },\n    variable: /\\b\\w+(?=:)/,\n    number: /(?:\\b|-)\\d+\\b/,\n    operator: /\\.\\.\\.|->|&|\\.?=/,\n    punctuation: /\\(#|#\\)|[,:;\\[\\](){}]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB,YAAM,UAAU,QAAQ;AAAA,QACtB,SAAS;AAAA,QACT,SACE;AAAA,QACF,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}