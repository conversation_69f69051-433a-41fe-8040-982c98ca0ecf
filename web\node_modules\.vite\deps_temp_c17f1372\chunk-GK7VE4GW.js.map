{"version": 3, "sources": ["../../refractor/lang/markup-templating.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = markupTemplating\nmarkupTemplating.displayName = 'markupTemplating'\nmarkupTemplating.aliases = []\nfunction markupTemplating(Prism) {\n  ;(function (Prism) {\n    /**\n     * Returns the placeholder for the given language id and index.\n     *\n     * @param {string} language\n     * @param {string|number} index\n     * @returns {string}\n     */\n    function getPlaceholder(language, index) {\n      return '___' + language.toUpperCase() + index + '___'\n    }\n    Object.defineProperties((Prism.languages['markup-templating'] = {}), {\n      buildPlaceholders: {\n        /**\n         * Tokenize all inline templating expressions matching `placeholderPattern`.\n         *\n         * If `replaceFilter` is provided, only matches of `placeholderPattern` for which `replaceFilter` returns\n         * `true` will be replaced.\n         *\n         * @param {object} env The environment of the `before-tokenize` hook.\n         * @param {string} language The language id.\n         * @param {RegExp} placeholderPattern The matches of this pattern will be replaced by placeholders.\n         * @param {(match: string) => boolean} [replaceFilter]\n         */\n        value: function (env, language, placeholderPattern, replaceFilter) {\n          if (env.language !== language) {\n            return\n          }\n          var tokenStack = (env.tokenStack = [])\n          env.code = env.code.replace(placeholderPattern, function (match) {\n            if (typeof replaceFilter === 'function' && !replaceFilter(match)) {\n              return match\n            }\n            var i = tokenStack.length\n            var placeholder // Check for existing strings\n            while (\n              env.code.indexOf((placeholder = getPlaceholder(language, i))) !==\n              -1\n            ) {\n              ++i\n            } // Create a sparse array\n            tokenStack[i] = match\n            return placeholder\n          }) // Switch the grammar to markup\n          env.grammar = Prism.languages.markup\n        }\n      },\n      tokenizePlaceholders: {\n        /**\n         * Replace placeholders with proper tokens after tokenizing.\n         *\n         * @param {object} env The environment of the `after-tokenize` hook.\n         * @param {string} language The language id.\n         */\n        value: function (env, language) {\n          if (env.language !== language || !env.tokenStack) {\n            return\n          } // Switch the grammar back\n          env.grammar = Prism.languages[language]\n          var j = 0\n          var keys = Object.keys(env.tokenStack)\n          function walkTokens(tokens) {\n            for (var i = 0; i < tokens.length; i++) {\n              // all placeholders are replaced already\n              if (j >= keys.length) {\n                break\n              }\n              var token = tokens[i]\n              if (\n                typeof token === 'string' ||\n                (token.content && typeof token.content === 'string')\n              ) {\n                var k = keys[j]\n                var t = env.tokenStack[k]\n                var s = typeof token === 'string' ? token : token.content\n                var placeholder = getPlaceholder(language, k)\n                var index = s.indexOf(placeholder)\n                if (index > -1) {\n                  ++j\n                  var before = s.substring(0, index)\n                  var middle = new Prism.Token(\n                    language,\n                    Prism.tokenize(t, env.grammar),\n                    'language-' + language,\n                    t\n                  )\n                  var after = s.substring(index + placeholder.length)\n                  var replacement = []\n                  if (before) {\n                    replacement.push.apply(replacement, walkTokens([before]))\n                  }\n                  replacement.push(middle)\n                  if (after) {\n                    replacement.push.apply(replacement, walkTokens([after]))\n                  }\n                  if (typeof token === 'string') {\n                    tokens.splice.apply(tokens, [i, 1].concat(replacement))\n                  } else {\n                    token.content = replacement\n                  }\n                }\n              } else if (\n                token.content\n                /* && typeof token.content !== 'string' */\n              ) {\n                walkTokens(token.content)\n              }\n            }\n            return tokens\n          }\n          walkTokens(env.tokens)\n        }\n      }\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,qBAAiB,cAAc;AAC/B,qBAAiB,UAAU,CAAC;AAC5B,aAAS,iBAAiB,OAAO;AAC/B;AAAC,OAAC,SAAUA,QAAO;AAQjB,iBAAS,eAAe,UAAU,OAAO;AACvC,iBAAO,QAAQ,SAAS,YAAY,IAAI,QAAQ;AAAA,QAClD;AACA,eAAO,iBAAkBA,OAAM,UAAU,mBAAmB,IAAI,CAAC,GAAI;AAAA,UACnE,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAYjB,OAAO,SAAU,KAAK,UAAU,oBAAoB,eAAe;AACjE,kBAAI,IAAI,aAAa,UAAU;AAC7B;AAAA,cACF;AACA,kBAAI,aAAc,IAAI,aAAa,CAAC;AACpC,kBAAI,OAAO,IAAI,KAAK,QAAQ,oBAAoB,SAAU,OAAO;AAC/D,oBAAI,OAAO,kBAAkB,cAAc,CAAC,cAAc,KAAK,GAAG;AAChE,yBAAO;AAAA,gBACT;AACA,oBAAI,IAAI,WAAW;AACnB,oBAAI;AACJ,uBACE,IAAI,KAAK,QAAS,cAAc,eAAe,UAAU,CAAC,CAAE,MAC5D,IACA;AACA,oBAAE;AAAA,gBACJ;AACA,2BAAW,CAAC,IAAI;AAChB,uBAAO;AAAA,cACT,CAAC;AACD,kBAAI,UAAUA,OAAM,UAAU;AAAA,YAChC;AAAA,UACF;AAAA,UACA,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAOpB,OAAO,SAAU,KAAK,UAAU;AAC9B,kBAAI,IAAI,aAAa,YAAY,CAAC,IAAI,YAAY;AAChD;AAAA,cACF;AACA,kBAAI,UAAUA,OAAM,UAAU,QAAQ;AACtC,kBAAI,IAAI;AACR,kBAAI,OAAO,OAAO,KAAK,IAAI,UAAU;AACrC,uBAAS,WAAW,QAAQ;AAC1B,yBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAEtC,sBAAI,KAAK,KAAK,QAAQ;AACpB;AAAA,kBACF;AACA,sBAAI,QAAQ,OAAO,CAAC;AACpB,sBACE,OAAO,UAAU,YAChB,MAAM,WAAW,OAAO,MAAM,YAAY,UAC3C;AACA,wBAAI,IAAI,KAAK,CAAC;AACd,wBAAI,IAAI,IAAI,WAAW,CAAC;AACxB,wBAAI,IAAI,OAAO,UAAU,WAAW,QAAQ,MAAM;AAClD,wBAAI,cAAc,eAAe,UAAU,CAAC;AAC5C,wBAAI,QAAQ,EAAE,QAAQ,WAAW;AACjC,wBAAI,QAAQ,IAAI;AACd,wBAAE;AACF,0BAAI,SAAS,EAAE,UAAU,GAAG,KAAK;AACjC,0BAAI,SAAS,IAAIA,OAAM;AAAA,wBACrB;AAAA,wBACAA,OAAM,SAAS,GAAG,IAAI,OAAO;AAAA,wBAC7B,cAAc;AAAA,wBACd;AAAA,sBACF;AACA,0BAAI,QAAQ,EAAE,UAAU,QAAQ,YAAY,MAAM;AAClD,0BAAI,cAAc,CAAC;AACnB,0BAAI,QAAQ;AACV,oCAAY,KAAK,MAAM,aAAa,WAAW,CAAC,MAAM,CAAC,CAAC;AAAA,sBAC1D;AACA,kCAAY,KAAK,MAAM;AACvB,0BAAI,OAAO;AACT,oCAAY,KAAK,MAAM,aAAa,WAAW,CAAC,KAAK,CAAC,CAAC;AAAA,sBACzD;AACA,0BAAI,OAAO,UAAU,UAAU;AAC7B,+BAAO,OAAO,MAAM,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,WAAW,CAAC;AAAA,sBACxD,OAAO;AACL,8BAAM,UAAU;AAAA,sBAClB;AAAA,oBACF;AAAA,kBACF,WACE,MAAM,SAEN;AACA,+BAAW,MAAM,OAAO;AAAA,kBAC1B;AAAA,gBACF;AACA,uBAAO;AAAA,cACT;AACA,yBAAW,IAAI,MAAM;AAAA,YACvB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}