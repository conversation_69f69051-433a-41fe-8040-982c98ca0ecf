{"version": 3, "sources": ["../../highlight.js/lib/languages/gams.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(', re, ')*');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/** @type LanguageFn */\nfunction gams(hljs) {\n  const KEYWORDS = {\n    keyword:\n      'abort acronym acronyms alias all and assign binary card diag display ' +\n      'else eq file files for free ge gt if integer le loop lt maximizing ' +\n      'minimizing model models ne negative no not option options or ord ' +\n      'positive prod put putpage puttl repeat sameas semicont semiint smax ' +\n      'smin solve sos1 sos2 sum system table then until using while xor yes',\n    literal:\n      'eps inf na',\n    built_in:\n      'abs arccos arcsin arctan arctan2 Beta betaReg binomial ceil centropy ' +\n      'cos cosh cvPower div div0 eDist entropy errorf execSeed exp fact ' +\n      'floor frac gamma gammaReg log logBeta logGamma log10 log2 mapVal max ' +\n      'min mod ncpCM ncpF ncpVUpow ncpVUsin normal pi poly power ' +\n      'randBinomial randLinear randTriangle round rPower sigmoid sign ' +\n      'signPower sin sinh slexp sllog10 slrec sqexp sqlog10 sqr sqrec sqrt ' +\n      'tan tanh trunc uniform uniformInt vcPower bool_and bool_eqv bool_imp ' +\n      'bool_not bool_or bool_xor ifThen rel_eq rel_ge rel_gt rel_le rel_lt ' +\n      'rel_ne gday gdow ghour gleap gmillisec gminute gmonth gsecond gyear ' +\n      'jdate jnow jstart jtime errorLevel execError gamsRelease gamsVersion ' +\n      'handleCollect handleDelete handleStatus handleSubmit heapFree ' +\n      'heapLimit heapSize jobHandle jobKill jobStatus jobTerminate ' +\n      'licenseLevel licenseStatus maxExecError sleep timeClose timeComp ' +\n      'timeElapsed timeExec timeStart'\n  };\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true\n  };\n  const SYMBOLS = {\n    className: 'symbol',\n    variants: [\n      {\n        begin: /=[lgenxc]=/\n      },\n      {\n        begin: /\\$/\n      }\n    ]\n  };\n  const QSTR = { // One-line quoted comment string\n    className: 'comment',\n    variants: [\n      {\n        begin: '\\'',\n        end: '\\''\n      },\n      {\n        begin: '\"',\n        end: '\"'\n      }\n    ],\n    illegal: '\\\\n',\n    contains: [hljs.BACKSLASH_ESCAPE]\n  };\n  const ASSIGNMENT = {\n    begin: '/',\n    end: '/',\n    keywords: KEYWORDS,\n    contains: [\n      QSTR,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n  const COMMENT_WORD = /[a-z0-9&#*=?@\\\\><:,()$[\\]_.{}!+%^-]+/;\n  const DESCTEXT = { // Parameter/set/variable description text\n    begin: /[a-z][a-z0-9_]*(\\([a-z0-9_, ]*\\))?[ \\t]+/,\n    excludeBegin: true,\n    end: '$',\n    endsWithParent: true,\n    contains: [\n      QSTR,\n      ASSIGNMENT,\n      {\n        className: 'comment',\n        // one comment word, then possibly more\n        begin: concat(\n          COMMENT_WORD,\n          // [ ] because \\s would be too broad (matching newlines)\n          anyNumberOfTimes(concat(/[ ]+/, COMMENT_WORD))\n        ),\n        relevance: 0\n      }\n    ]\n  };\n\n  return {\n    name: 'GAMS',\n    aliases: ['gms'],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    contains: [\n      hljs.COMMENT(/^\\$ontext/, /^\\$offtext/),\n      {\n        className: 'meta',\n        begin: '^\\\\$[a-z0-9]+',\n        end: '$',\n        returnBegin: true,\n        contains: [\n          {\n            className: 'meta-keyword',\n            begin: '^\\\\$[a-z0-9]+'\n          }\n        ]\n      },\n      hljs.COMMENT('^\\\\*', '$'),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      // Declarations\n      {\n        beginKeywords:\n          'set sets parameter parameters variable variables ' +\n          'scalar scalars equation equations',\n        end: ';',\n        contains: [\n          hljs.COMMENT('^\\\\*', '$'),\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          ASSIGNMENT,\n          DESCTEXT\n        ]\n      },\n      { // table environment\n        beginKeywords: 'table',\n        end: ';',\n        returnBegin: true,\n        contains: [\n          { // table header row\n            beginKeywords: 'table',\n            end: '$',\n            contains: [DESCTEXT]\n          },\n          hljs.COMMENT('^\\\\*', '$'),\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          hljs.C_NUMBER_MODE\n          // Table does not contain DESCTEXT or ASSIGNMENT\n        ]\n      },\n      // Function definitions\n      {\n        className: 'function',\n        begin: /^[a-z][a-z0-9_,\\-+' ()$]+\\.{2}/,\n        returnBegin: true,\n        contains: [\n          { // Function title\n            className: 'title',\n            begin: /^[a-z0-9_]+/\n          },\n          PARAMS,\n          SYMBOLS\n        ]\n      },\n      hljs.C_NUMBER_MODE,\n      SYMBOLS\n    ]\n  };\n}\n\nmodule.exports = gams;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,iBAAiB,IAAI;AAC5B,aAAO,OAAO,KAAK,IAAI,IAAI;AAAA,IAC7B;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAGA,aAAS,KAAK,MAAM;AAClB,YAAM,WAAW;AAAA,QACf,SACE;AAAA,QAKF,SACE;AAAA,QACF,UACE;AAAA,MAcJ;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,cAAc;AAAA,QACd,YAAY;AAAA,MACd;AACA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,OAAO;AAAA;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,SAAS;AAAA,QACT,UAAU,CAAC,KAAK,gBAAgB;AAAA,MAClC;AACA,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,YAAM,eAAe;AACrB,YAAM,WAAW;AAAA;AAAA,QACf,OAAO;AAAA,QACP,cAAc;AAAA,QACd,KAAK;AAAA,QACL,gBAAgB;AAAA,QAChB,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA;AAAA,YAEX,OAAO;AAAA,cACL;AAAA;AAAA,cAEA,iBAAiB,OAAO,QAAQ,YAAY,CAAC;AAAA,YAC/C;AAAA,YACA,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,KAAK;AAAA,QACf,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK,QAAQ,aAAa,YAAY;AAAA,UACtC;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK,QAAQ,QAAQ,GAAG;AAAA,UACxB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA;AAAA,UAEL;AAAA,YACE,eACE;AAAA,YAEF,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK,QAAQ,QAAQ,GAAG;AAAA,cACxB,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA;AAAA,gBACE,eAAe;AAAA,gBACf,KAAK;AAAA,gBACL,UAAU,CAAC,QAAQ;AAAA,cACrB;AAAA,cACA,KAAK,QAAQ,QAAQ,GAAG;AAAA,cACxB,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA;AAAA,YAEP;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,aAAa;AAAA,YACb,UAAU;AAAA,cACR;AAAA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}