{"version": 3, "sources": ["../../refractor/lang/icon.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = icon\nicon.displayName = 'icon'\nicon.aliases = []\nfunction icon(Prism) {\n  Prism.languages.icon = {\n    comment: /#.*/,\n    string: {\n      pattern: /([\"'])(?:(?!\\1)[^\\\\\\r\\n_]|\\\\.|_(?!\\1)(?:\\r\\n|[\\s\\S]))*\\1/,\n      greedy: true\n    },\n    number: /\\b(?:\\d+r[a-z\\d]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b|\\.\\d+\\b/i,\n    'builtin-keyword': {\n      pattern:\n        /&(?:allocated|ascii|clock|collections|cset|current|date|dateline|digits|dump|e|error(?:number|text|value)?|errout|fail|features|file|host|input|lcase|letters|level|line|main|null|output|phi|pi|pos|progname|random|regions|source|storage|subject|time|trace|ucase|version)\\b/,\n      alias: 'variable'\n    },\n    directive: {\n      pattern: /\\$\\w+/,\n      alias: 'builtin'\n    },\n    keyword:\n      /\\b(?:break|by|case|create|default|do|else|end|every|fail|global|if|initial|invocable|link|local|next|not|of|procedure|record|repeat|return|static|suspend|then|to|until|while)\\b/,\n    function: /\\b(?!\\d)\\w+(?=\\s*[({]|\\s*!\\s*\\[)/,\n    operator:\n      /[+-]:(?!=)|(?:[\\/?@^%&]|\\+\\+?|--?|==?=?|~==?=?|\\*\\*?|\\|\\|\\|?|<(?:->?|<?=?)|>>?=?)(?::=)?|:(?:=:?)?|[!.\\\\|~]/,\n    punctuation: /[\\[\\](){},;]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,UAAU,OAAO;AAAA,QACrB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,QACR,mBAAmB;AAAA,UACjB,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,QACV,UACE;AAAA,QACF,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}