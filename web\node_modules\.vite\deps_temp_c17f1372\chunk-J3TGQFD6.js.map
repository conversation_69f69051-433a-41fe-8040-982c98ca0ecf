{"version": 3, "sources": ["../../highlight.js/lib/languages/json.js"], "sourcesContent": ["/*\nLanguage: JSON\nDescription: JSON (JavaScript Object Notation) is a lightweight data-interchange format.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://www.json.org\nCategory: common, protocols\n*/\n\nfunction json(hljs) {\n  const LITERALS = {\n    literal: 'true false null'\n  };\n  const ALLOWED_COMMENTS = [\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE\n  ];\n  const TYPES = [\n    hljs.QUOTE_STRING_MODE,\n    hljs.C_NUMBER_MODE\n  ];\n  const VALUE_CONTAINER = {\n    end: ',',\n    endsWithParent: true,\n    excludeEnd: true,\n    contains: TYPES,\n    keywords: LITERALS\n  };\n  const OBJECT = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: [\n      {\n        className: 'attr',\n        begin: /\"/,\n        end: /\"/,\n        contains: [hljs.BACKSLASH_ESCAPE],\n        illegal: '\\\\n'\n      },\n      hljs.inherit(VALUE_CONTAINER, {\n        begin: /:/\n      })\n    ].concat(ALLOWED_COMMENTS),\n    illegal: '\\\\S'\n  };\n  const ARRAY = {\n    begin: '\\\\[',\n    end: '\\\\]',\n    contains: [hljs.inherit(VALUE_CONTAINER)], // inherit is a workaround for a bug that makes shared modes with endsWithParent compile only the ending of one of the parents\n    illegal: '\\\\S'\n  };\n  TYPES.push(OBJECT, ARRAY);\n  ALLOWED_COMMENTS.forEach(function(rule) {\n    TYPES.push(rule);\n  });\n  return {\n    name: 'JSON',\n    contains: TYPES,\n    keywords: LITERALS,\n    illegal: '\\\\S'\n  };\n}\n\nmodule.exports = json;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,KAAK,MAAM;AAClB,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,MACX;AACA,YAAM,mBAAmB;AAAA,QACvB,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AACA,YAAM,QAAQ;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AACA,YAAM,kBAAkB;AAAA,QACtB,KAAK;AAAA,QACL,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AACA,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC,KAAK,gBAAgB;AAAA,YAChC,SAAS;AAAA,UACX;AAAA,UACA,KAAK,QAAQ,iBAAiB;AAAA,YAC5B,OAAO;AAAA,UACT,CAAC;AAAA,QACH,EAAE,OAAO,gBAAgB;AAAA,QACzB,SAAS;AAAA,MACX;AACA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC,KAAK,QAAQ,eAAe,CAAC;AAAA;AAAA,QACxC,SAAS;AAAA,MACX;AACA,YAAM,KAAK,QAAQ,KAAK;AACxB,uBAAiB,QAAQ,SAAS,MAAM;AACtC,cAAM,KAAK,IAAI;AAAA,MACjB,CAAC;AACD,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}