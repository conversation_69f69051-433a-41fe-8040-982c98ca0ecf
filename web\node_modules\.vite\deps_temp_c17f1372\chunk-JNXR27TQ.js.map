{"version": 3, "sources": ["../../refractor/lang/ini.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = ini\nini.displayName = 'ini'\nini.aliases = []\nfunction ini(Prism) {\n  Prism.languages.ini = {\n    /**\n     * The component mimics the behavior of the Win32 API parser.\n     *\n     * @see {@link https://github.com/PrismJS/prism/issues/2775#issuecomment-787477723}\n     */\n    comment: {\n      pattern: /(^[ \\f\\t\\v]*)[#;][^\\n\\r]*/m,\n      lookbehind: true\n    },\n    section: {\n      pattern: /(^[ \\f\\t\\v]*)\\[[^\\n\\r\\]]*\\]?/m,\n      lookbehind: true,\n      inside: {\n        'section-name': {\n          pattern: /(^\\[[ \\f\\t\\v]*)[^ \\f\\t\\v\\]]+(?:[ \\f\\t\\v]+[^ \\f\\t\\v\\]]+)*/,\n          lookbehind: true,\n          alias: 'selector'\n        },\n        punctuation: /\\[|\\]/\n      }\n    },\n    key: {\n      pattern:\n        /(^[ \\f\\t\\v]*)[^ \\f\\n\\r\\t\\v=]+(?:[ \\f\\t\\v]+[^ \\f\\n\\r\\t\\v=]+)*(?=[ \\f\\t\\v]*=)/m,\n      lookbehind: true,\n      alias: 'attr-name'\n    },\n    value: {\n      pattern: /(=[ \\f\\t\\v]*)[^ \\f\\n\\r\\t\\v]+(?:[ \\f\\t\\v]+[^ \\f\\n\\r\\t\\v]+)*/,\n      lookbehind: true,\n      alias: 'attr-value',\n      inside: {\n        'inner-value': {\n          pattern: /^(\"|').+(?=\\1$)/,\n          lookbehind: true\n        }\n      }\n    },\n    punctuation: /=/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,UAAU,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMpB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,gBAAgB;AAAA,cACd,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,KAAK;AAAA,UACH,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}