{"version": 3, "sources": ["../../refractor/lang/v.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = v\nv.displayName = 'v'\nv.aliases = []\nfunction v(Prism) {\n  ;(function (Prism) {\n    var interpolationExpr = {\n      pattern: /[\\s\\S]+/,\n      inside: null\n    }\n    Prism.languages.v = Prism.languages.extend('clike', {\n      string: {\n        pattern: /r?([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        alias: 'quoted-string',\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern:\n              /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:\\{[^{}]*\\}|\\w+(?:\\.\\w+(?:\\([^\\(\\)]*\\))?|\\[[^\\[\\]]+\\])*)/,\n            lookbehind: true,\n            inside: {\n              'interpolation-variable': {\n                pattern: /^\\$\\w[\\s\\S]*$/,\n                alias: 'variable'\n              },\n              'interpolation-punctuation': {\n                pattern: /^\\$\\{|\\}$/,\n                alias: 'punctuation'\n              },\n              'interpolation-expression': interpolationExpr\n            }\n          }\n        }\n      },\n      'class-name': {\n        pattern: /(\\b(?:enum|interface|struct|type)\\s+)(?:C\\.)?\\w+/,\n        lookbehind: true\n      },\n      keyword:\n        /(?:\\b(?:__global|as|asm|assert|atomic|break|chan|const|continue|defer|else|embed|enum|fn|for|go(?:to)?|if|import|in|interface|is|lock|match|module|mut|none|or|pub|return|rlock|select|shared|sizeof|static|struct|type(?:of)?|union|unsafe)|\\$(?:else|for|if)|#(?:flag|include))\\b/,\n      number:\n        /\\b(?:0x[a-f\\d]+(?:_[a-f\\d]+)*|0b[01]+(?:_[01]+)*|0o[0-7]+(?:_[0-7]+)*|\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?)\\b/i,\n      operator:\n        /~|\\?|[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\.?/,\n      builtin:\n        /\\b(?:any(?:_float|_int)?|bool|byte(?:ptr)?|charptr|f(?:32|64)|i(?:8|16|64|128|nt)|rune|size_t|string|u(?:16|32|64|128)|voidptr)\\b/\n    })\n    interpolationExpr.inside = Prism.languages.v\n    Prism.languages.insertBefore('v', 'string', {\n      char: {\n        pattern: /`(?:\\\\`|\\\\?[^`]{1,2})`/,\n        // using {1,2} instead of `u` flag for compatibility\n        alias: 'rune'\n      }\n    })\n    Prism.languages.insertBefore('v', 'operator', {\n      attribute: {\n        pattern:\n          /(^[\\t ]*)\\[(?:deprecated|direct_array_access|flag|inline|live|ref_only|typedef|unsafe_fn|windows_stdcall)\\]/m,\n        lookbehind: true,\n        alias: 'annotation',\n        inside: {\n          punctuation: /[\\[\\]]/,\n          keyword: /\\w+/\n        }\n      },\n      generic: {\n        pattern: /<\\w+>(?=\\s*[\\)\\{])/,\n        inside: {\n          punctuation: /[<>]/,\n          'class-name': /\\w+/\n        }\n      }\n    })\n    Prism.languages.insertBefore('v', 'function', {\n      'generic-function': {\n        // e.g. foo<T>( ...\n        pattern: /\\b\\w+\\s*<\\w+>(?=\\()/,\n        inside: {\n          function: /^\\w+/,\n          generic: {\n            pattern: /<\\w+>/,\n            inside: Prism.languages.v.generic.inside\n          }\n        }\n      }\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,MAAE,cAAc;AAChB,MAAE,UAAU,CAAC;AACb,aAAS,EAAE,OAAO;AAChB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,oBAAoB;AAAA,UACtB,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AACA,QAAAA,OAAM,UAAU,IAAIA,OAAM,UAAU,OAAO,SAAS;AAAA,UAClD,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,eAAe;AAAA,gBACb,SACE;AAAA,gBACF,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,0BAA0B;AAAA,oBACxB,SAAS;AAAA,oBACT,OAAO;AAAA,kBACT;AAAA,kBACA,6BAA6B;AAAA,oBAC3B,SAAS;AAAA,oBACT,OAAO;AAAA,kBACT;AAAA,kBACA,4BAA4B;AAAA,gBAC9B;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,SACE;AAAA,UACF,QACE;AAAA,UACF,UACE;AAAA,UACF,SACE;AAAA,QACJ,CAAC;AACD,0BAAkB,SAASA,OAAM,UAAU;AAC3C,QAAAA,OAAM,UAAU,aAAa,KAAK,UAAU;AAAA,UAC1C,MAAM;AAAA,YACJ,SAAS;AAAA;AAAA,YAET,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,KAAK,YAAY;AAAA,UAC5C,WAAW;AAAA,YACT,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,aAAa;AAAA,cACb,SAAS;AAAA,YACX;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,cACb,cAAc;AAAA,YAChB;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,KAAK,YAAY;AAAA,UAC5C,oBAAoB;AAAA;AAAA,YAElB,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,UAAU;AAAA,cACV,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,QAAQA,OAAM,UAAU,EAAE,QAAQ;AAAA,cACpC;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}