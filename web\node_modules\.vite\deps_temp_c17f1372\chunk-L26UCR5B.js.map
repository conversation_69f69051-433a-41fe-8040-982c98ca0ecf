{"version": 3, "sources": ["../../refractor/lang/wiki.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = wiki\nwiki.displayName = 'wiki'\nwiki.aliases = []\nfunction wiki(Prism) {\n  Prism.languages.wiki = Prism.languages.extend('markup', {\n    'block-comment': {\n      pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?\\*\\//,\n      lookbehind: true,\n      alias: 'comment'\n    },\n    heading: {\n      pattern: /^(=+)[^=\\r\\n].*?\\1/m,\n      inside: {\n        punctuation: /^=+|=+$/,\n        important: /.+/\n      }\n    },\n    emphasis: {\n      // TODO Multi-line\n      pattern: /('{2,5}).+?\\1/,\n      inside: {\n        'bold-italic': {\n          pattern: /(''''').+?(?=\\1)/,\n          lookbehind: true,\n          alias: ['bold', 'italic']\n        },\n        bold: {\n          pattern: /(''')[^'](?:.*?[^'])?(?=\\1)/,\n          lookbehind: true\n        },\n        italic: {\n          pattern: /('')[^'](?:.*?[^'])?(?=\\1)/,\n          lookbehind: true\n        },\n        punctuation: /^''+|''+$/\n      }\n    },\n    hr: {\n      pattern: /^-{4,}/m,\n      alias: 'punctuation'\n    },\n    url: [\n      /ISBN +(?:97[89][ -]?)?(?:\\d[ -]?){9}[\\dx]\\b|(?:PMID|RFC) +\\d+/i,\n      /\\[\\[.+?\\]\\]|\\[.+?\\]/\n    ],\n    variable: [\n      /__[A-Z]+__/, // FIXME Nested structures should be handled\n      // {{formatnum:{{#expr:{{{3}}}}}}}\n      /\\{{3}.+?\\}{3}/,\n      /\\{\\{.+?\\}\\}/\n    ],\n    symbol: [/^#redirect/im, /~{3,5}/],\n    // Handle table attrs:\n    // {|\n    // ! style=\"text-align:left;\"| Item\n    // |}\n    'table-tag': {\n      pattern: /((?:^|[|!])[|!])[^|\\r\\n]+\\|(?!\\|)/m,\n      lookbehind: true,\n      inside: {\n        'table-bar': {\n          pattern: /\\|$/,\n          alias: 'punctuation'\n        },\n        rest: Prism.languages.markup['tag'].inside\n      }\n    },\n    punctuation: /^(?:\\{\\||\\|\\}|\\|-|[*#:;!|])|\\|\\||!!/m\n  })\n  Prism.languages.insertBefore('wiki', 'tag', {\n    // Prevent highlighting inside <nowiki>, <source> and <pre> tags\n    nowiki: {\n      pattern: /<(nowiki|pre|source)\\b[^>]*>[\\s\\S]*?<\\/\\1>/i,\n      inside: {\n        tag: {\n          pattern: /<(?:nowiki|pre|source)\\b[^>]*>|<\\/(?:nowiki|pre|source)>/i,\n          inside: Prism.languages.markup['tag'].inside\n        }\n      }\n    }\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,UAAU,OAAO,MAAM,UAAU,OAAO,UAAU;AAAA,QACtD,iBAAiB;AAAA,UACf,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,aAAa;AAAA,YACb,WAAW;AAAA,UACb;AAAA,QACF;AAAA,QACA,UAAU;AAAA;AAAA,UAER,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO,CAAC,QAAQ,QAAQ;AAAA,YAC1B;AAAA,YACA,MAAM;AAAA,cACJ,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,IAAI;AAAA,UACF,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,KAAK;AAAA,UACH;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA;AAAA;AAAA,UAEA;AAAA,UACA;AAAA,QACF;AAAA,QACA,QAAQ,CAAC,gBAAgB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,QAKjC,aAAa;AAAA,UACX,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,aAAa;AAAA,cACX,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,MAAM,MAAM,UAAU,OAAO,KAAK,EAAE;AAAA,UACtC;AAAA,QACF;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AACD,YAAM,UAAU,aAAa,QAAQ,OAAO;AAAA;AAAA,QAE1C,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,KAAK;AAAA,cACH,SAAS;AAAA,cACT,QAAQ,MAAM,UAAU,OAAO,KAAK,EAAE;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}