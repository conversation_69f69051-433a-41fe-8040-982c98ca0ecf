{"version": 3, "sources": ["../../refractor/lang/csharp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = csharp\ncsharp.displayName = 'csharp'\ncsharp.aliases = ['dotnet', 'cs']\nfunction csharp(Prism) {\n  ;(function (Prism) {\n    /**\n     * Replaces all placeholders \"<<n>>\" of given pattern with the n-th replacement (zero based).\n     *\n     * Note: This is a simple text based replacement. Be careful when using backreferences!\n     *\n     * @param {string} pattern the given pattern.\n     * @param {string[]} replacements a list of replacement which can be inserted into the given pattern.\n     * @returns {string} the pattern with all placeholders replaced with their corresponding replacements.\n     * @example replace(/a<<0>>a/.source, [/b+/.source]) === /a(?:b+)a/.source\n     */\n    function replace(pattern, replacements) {\n      return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n        return '(?:' + replacements[+index] + ')'\n      })\n    }\n    /**\n     * @param {string} pattern\n     * @param {string[]} replacements\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function re(pattern, replacements, flags) {\n      return RegExp(replace(pattern, replacements), flags || '')\n    }\n    /**\n     * Creates a nested pattern where all occurrences of the string `<<self>>` are replaced with the pattern itself.\n     *\n     * @param {string} pattern\n     * @param {number} depthLog2\n     * @returns {string}\n     */\n    function nested(pattern, depthLog2) {\n      for (var i = 0; i < depthLog2; i++) {\n        pattern = pattern.replace(/<<self>>/g, function () {\n          return '(?:' + pattern + ')'\n        })\n      }\n      return pattern.replace(/<<self>>/g, '[^\\\\s\\\\S]')\n    } // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/keywords/\n    var keywordKinds = {\n      // keywords which represent a return or variable type\n      type: 'bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void',\n      // keywords which are used to declare a type\n      typeDeclaration: 'class enum interface record struct',\n      // contextual keywords\n      // (\"var\" and \"dynamic\" are missing because they are used like types)\n      contextual:\n        'add alias and ascending async await by descending from(?=\\\\s*(?:\\\\w|$)) get global group into init(?=\\\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\\\s*{)',\n      // all other keywords\n      other:\n        'abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield'\n    } // keywords\n    function keywordsToPattern(words) {\n      return '\\\\b(?:' + words.trim().replace(/ /g, '|') + ')\\\\b'\n    }\n    var typeDeclarationKeywords = keywordsToPattern(\n      keywordKinds.typeDeclaration\n    )\n    var keywords = RegExp(\n      keywordsToPattern(\n        keywordKinds.type +\n          ' ' +\n          keywordKinds.typeDeclaration +\n          ' ' +\n          keywordKinds.contextual +\n          ' ' +\n          keywordKinds.other\n      )\n    )\n    var nonTypeKeywords = keywordsToPattern(\n      keywordKinds.typeDeclaration +\n        ' ' +\n        keywordKinds.contextual +\n        ' ' +\n        keywordKinds.other\n    )\n    var nonContextualKeywords = keywordsToPattern(\n      keywordKinds.type +\n        ' ' +\n        keywordKinds.typeDeclaration +\n        ' ' +\n        keywordKinds.other\n    ) // types\n    var generic = nested(/<(?:[^<>;=+\\-*/%&|^]|<<self>>)*>/.source, 2) // the idea behind the other forbidden characters is to prevent false positives. Same for tupleElement.\n    var nestedRound = nested(/\\((?:[^()]|<<self>>)*\\)/.source, 2)\n    var name = /@?\\b[A-Za-z_]\\w*\\b/.source\n    var genericName = replace(/<<0>>(?:\\s*<<1>>)?/.source, [name, generic])\n    var identifier = replace(/(?!<<0>>)<<1>>(?:\\s*\\.\\s*<<1>>)*/.source, [\n      nonTypeKeywords,\n      genericName\n    ])\n    var array = /\\[\\s*(?:,\\s*)*\\]/.source\n    var typeExpressionWithoutTuple = replace(\n      /<<0>>(?:\\s*(?:\\?\\s*)?<<1>>)*(?:\\s*\\?)?/.source,\n      [identifier, array]\n    )\n    var tupleElement = replace(\n      /[^,()<>[\\];=+\\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source,\n      [generic, nestedRound, array]\n    )\n    var tuple = replace(/\\(<<0>>+(?:,<<0>>+)+\\)/.source, [tupleElement])\n    var typeExpression = replace(\n      /(?:<<0>>|<<1>>)(?:\\s*(?:\\?\\s*)?<<2>>)*(?:\\s*\\?)?/.source,\n      [tuple, identifier, array]\n    )\n    var typeInside = {\n      keyword: keywords,\n      punctuation: /[<>()?,.:[\\]]/\n    } // strings & characters\n    // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#character-literals\n    // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#string-literals\n    var character = /'(?:[^\\r\\n'\\\\]|\\\\.|\\\\[Uux][\\da-fA-F]{1,8})'/.source // simplified pattern\n    var regularString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/.source\n    var verbatimString = /@\"(?:\"\"|\\\\[\\s\\S]|[^\\\\\"])*\"(?!\")/.source\n    Prism.languages.csharp = Prism.languages.extend('clike', {\n      string: [\n        {\n          pattern: re(/(^|[^$\\\\])<<0>>/.source, [verbatimString]),\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: re(/(^|[^@$\\\\])<<0>>/.source, [regularString]),\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      'class-name': [\n        {\n          // Using static\n          // using static System.Math;\n          pattern: re(/(\\busing\\s+static\\s+)<<0>>(?=\\s*;)/.source, [\n            identifier\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Using alias (type)\n          // using Project = PC.MyCompany.Project;\n          pattern: re(/(\\busing\\s+<<0>>\\s*=\\s*)<<1>>(?=\\s*;)/.source, [\n            name,\n            typeExpression\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Using alias (alias)\n          // using Project = PC.MyCompany.Project;\n          pattern: re(/(\\busing\\s+)<<0>>(?=\\s*=)/.source, [name]),\n          lookbehind: true\n        },\n        {\n          // Type declarations\n          // class Foo<A, B>\n          // interface Foo<out A, B>\n          pattern: re(/(\\b<<0>>\\s+)<<1>>/.source, [\n            typeDeclarationKeywords,\n            genericName\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Single catch exception declaration\n          // catch(Foo)\n          // (things like catch(Foo e) is covered by variable declaration)\n          pattern: re(/(\\bcatch\\s*\\(\\s*)<<0>>/.source, [identifier]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Name of the type parameter of generic constraints\n          // where Foo : class\n          pattern: re(/(\\bwhere\\s+)<<0>>/.source, [name]),\n          lookbehind: true\n        },\n        {\n          // Casts and checks via as and is.\n          // as Foo<A>, is Bar<B>\n          // (things like if(a is Foo b) is covered by variable declaration)\n          pattern: re(/(\\b(?:is(?:\\s+not)?|as)\\s+)<<0>>/.source, [\n            typeExpressionWithoutTuple\n          ]),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // Variable, field and parameter declaration\n          // (Foo bar, Bar baz, Foo[,,] bay, Foo<Bar, FooBar<Bar>> bax)\n          pattern: re(\n            /\\b<<0>>(?=\\s+(?!<<1>>|with\\s*\\{)<<2>>(?:\\s*[=,;:{)\\]]|\\s+(?:in|when)\\b))/\n              .source,\n            [typeExpression, nonContextualKeywords, name]\n          ),\n          inside: typeInside\n        }\n      ],\n      keyword: keywords,\n      // https://docs.microsoft.com/en-us/dotnet/csharp/language-reference/language-specification/lexical-structure#literals\n      number:\n        /(?:\\b0(?:x[\\da-f_]*[\\da-f]|b[01_]*[01])|(?:\\B\\.\\d+(?:_+\\d+)*|\\b\\d+(?:_+\\d+)*(?:\\.\\d+(?:_+\\d+)*)?)(?:e[-+]?\\d+(?:_+\\d+)*)?)(?:[dflmu]|lu|ul)?\\b/i,\n      operator: />>=?|<<=?|[-=]>|([-+&|])\\1|~|\\?\\?=?|[-+*/%&|^!=<>]=?/,\n      punctuation: /\\?\\.?|::|[{}[\\];(),.:]/\n    })\n    Prism.languages.insertBefore('csharp', 'number', {\n      range: {\n        pattern: /\\.\\./,\n        alias: 'operator'\n      }\n    })\n    Prism.languages.insertBefore('csharp', 'punctuation', {\n      'named-parameter': {\n        pattern: re(/([(,]\\s*)<<0>>(?=\\s*:)/.source, [name]),\n        lookbehind: true,\n        alias: 'punctuation'\n      }\n    })\n    Prism.languages.insertBefore('csharp', 'class-name', {\n      namespace: {\n        // namespace Foo.Bar {}\n        // using Foo.Bar;\n        pattern: re(\n          /(\\b(?:namespace|using)\\s+)<<0>>(?:\\s*\\.\\s*<<0>>)*(?=\\s*[;{])/.source,\n          [name]\n        ),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      'type-expression': {\n        // default(Foo), typeof(Foo<Bar>), sizeof(int)\n        pattern: re(\n          /(\\b(?:default|sizeof|typeof)\\s*\\(\\s*(?!\\s))(?:[^()\\s]|\\s(?!\\s)|<<0>>)*(?=\\s*\\))/\n            .source,\n          [nestedRound]\n        ),\n        lookbehind: true,\n        alias: 'class-name',\n        inside: typeInside\n      },\n      'return-type': {\n        // Foo<Bar> ForBar(); Foo IFoo.Bar() => 0\n        // int this[int index] => 0; T IReadOnlyList<T>.this[int index] => this[index];\n        // int Foo => 0; int Foo { get; set } = 0;\n        pattern: re(\n          /<<0>>(?=\\s+(?:<<1>>\\s*(?:=>|[({]|\\.\\s*this\\s*\\[)|this\\s*\\[))/.source,\n          [typeExpression, identifier]\n        ),\n        inside: typeInside,\n        alias: 'class-name'\n      },\n      'constructor-invocation': {\n        // new List<Foo<Bar[]>> { }\n        pattern: re(/(\\bnew\\s+)<<0>>(?=\\s*[[({])/.source, [typeExpression]),\n        lookbehind: true,\n        inside: typeInside,\n        alias: 'class-name'\n      },\n      /*'explicit-implementation': {\n// int IFoo<Foo>.Bar => 0; void IFoo<Foo<Foo>>.Foo<T>();\npattern: replace(/\\b<<0>>(?=\\.<<1>>)/, className, methodOrPropertyDeclaration),\ninside: classNameInside,\nalias: 'class-name'\n},*/\n      'generic-method': {\n        // foo<Bar>()\n        pattern: re(/<<0>>\\s*<<1>>(?=\\s*\\()/.source, [name, generic]),\n        inside: {\n          function: re(/^<<0>>/.source, [name]),\n          generic: {\n            pattern: RegExp(generic),\n            alias: 'class-name',\n            inside: typeInside\n          }\n        }\n      },\n      'type-list': {\n        // The list of types inherited or of generic constraints\n        // class Foo<F> : Bar, IList<FooBar>\n        // where F : Bar, IList<int>\n        pattern: re(\n          /\\b((?:<<0>>\\s+<<1>>|record\\s+<<1>>\\s*<<5>>|where\\s+<<2>>)\\s*:\\s*)(?:<<3>>|<<4>>|<<1>>\\s*<<5>>|<<6>>)(?:\\s*,\\s*(?:<<3>>|<<4>>|<<6>>))*(?=\\s*(?:where|[{;]|=>|$))/\n            .source,\n          [\n            typeDeclarationKeywords,\n            genericName,\n            name,\n            typeExpression,\n            keywords.source,\n            nestedRound,\n            /\\bnew\\s*\\(\\s*\\)/.source\n          ]\n        ),\n        lookbehind: true,\n        inside: {\n          'record-arguments': {\n            pattern: re(/(^(?!new\\s*\\()<<0>>\\s*)<<1>>/.source, [\n              genericName,\n              nestedRound\n            ]),\n            lookbehind: true,\n            greedy: true,\n            inside: Prism.languages.csharp\n          },\n          keyword: keywords,\n          'class-name': {\n            pattern: RegExp(typeExpression),\n            greedy: true,\n            inside: typeInside\n          },\n          punctuation: /[,()]/\n        }\n      },\n      preprocessor: {\n        pattern: /(^[\\t ]*)#.*/m,\n        lookbehind: true,\n        alias: 'property',\n        inside: {\n          // highlight preprocessor directives as keywords\n          directive: {\n            pattern:\n              /(#)\\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\\b/,\n            lookbehind: true,\n            alias: 'keyword'\n          }\n        }\n      }\n    }) // attributes\n    var regularStringOrCharacter = regularString + '|' + character\n    var regularStringCharacterOrComment = replace(\n      /\\/(?![*/])|\\/\\/[^\\r\\n]*[\\r\\n]|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>/.source,\n      [regularStringOrCharacter]\n    )\n    var roundExpression = nested(\n      replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [\n        regularStringCharacterOrComment\n      ]),\n      2\n    ) // https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/concepts/attributes/#attribute-targets\n    var attrTarget =\n      /\\b(?:assembly|event|field|method|module|param|property|return|type)\\b/\n        .source\n    var attr = replace(/<<0>>(?:\\s*\\(<<1>>*\\))?/.source, [\n      identifier,\n      roundExpression\n    ])\n    Prism.languages.insertBefore('csharp', 'class-name', {\n      attribute: {\n        // Attributes\n        // [Foo], [Foo(1), Bar(2, Prop = \"foo\")], [return: Foo(1), Bar(2)], [assembly: Foo(Bar)]\n        pattern: re(\n          /((?:^|[^\\s\\w>)?])\\s*\\[\\s*)(?:<<0>>\\s*:\\s*)?<<1>>(?:\\s*,\\s*<<1>>)*(?=\\s*\\])/\n            .source,\n          [attrTarget, attr]\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          target: {\n            pattern: re(/^<<0>>(?=\\s*:)/.source, [attrTarget]),\n            alias: 'keyword'\n          },\n          'attribute-arguments': {\n            pattern: re(/\\(<<0>>*\\)/.source, [roundExpression]),\n            inside: Prism.languages.csharp\n          },\n          'class-name': {\n            pattern: RegExp(identifier),\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          punctuation: /[:,]/\n        }\n      }\n    }) // string interpolation\n    var formatString = /:[^}\\r\\n]+/.source // multi line\n    var mInterpolationRound = nested(\n      replace(/[^\"'/()]|<<0>>|\\(<<self>>*\\)/.source, [\n        regularStringCharacterOrComment\n      ]),\n      2\n    )\n    var mInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [\n      mInterpolationRound,\n      formatString\n    ]) // single line\n    var sInterpolationRound = nested(\n      replace(\n        /[^\"'/()]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|<<0>>|\\(<<self>>*\\)/\n          .source,\n        [regularStringOrCharacter]\n      ),\n      2\n    )\n    var sInterpolation = replace(/\\{(?!\\{)(?:(?![}:])<<0>>)*<<1>>?\\}/.source, [\n      sInterpolationRound,\n      formatString\n    ])\n    function createInterpolationInside(interpolation, interpolationRound) {\n      return {\n        interpolation: {\n          pattern: re(/((?:^|[^{])(?:\\{\\{)*)<<0>>/.source, [interpolation]),\n          lookbehind: true,\n          inside: {\n            'format-string': {\n              pattern: re(/(^\\{(?:(?![}:])<<0>>)*)<<1>>(?=\\}$)/.source, [\n                interpolationRound,\n                formatString\n              ]),\n              lookbehind: true,\n              inside: {\n                punctuation: /^:/\n              }\n            },\n            punctuation: /^\\{|\\}$/,\n            expression: {\n              pattern: /[\\s\\S]+/,\n              alias: 'language-csharp',\n              inside: Prism.languages.csharp\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n    Prism.languages.insertBefore('csharp', 'string', {\n      'interpolation-string': [\n        {\n          pattern: re(\n            /(^|[^\\\\])(?:\\$@|@\\$)\"(?:\"\"|\\\\[\\s\\S]|\\{\\{|<<0>>|[^\\\\{\"])*\"/.source,\n            [mInterpolation]\n          ),\n          lookbehind: true,\n          greedy: true,\n          inside: createInterpolationInside(mInterpolation, mInterpolationRound)\n        },\n        {\n          pattern: re(/(^|[^@\\\\])\\$\"(?:\\\\.|\\{\\{|<<0>>|[^\\\\\"{])*\"/.source, [\n            sInterpolation\n          ]),\n          lookbehind: true,\n          greedy: true,\n          inside: createInterpolationInside(sInterpolation, sInterpolationRound)\n        }\n      ],\n      char: {\n        pattern: RegExp(character),\n        greedy: true\n      }\n    })\n    Prism.languages.dotnet = Prism.languages.cs = Prism.languages.csharp\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC,UAAU,IAAI;AAChC,aAAS,OAAO,OAAO;AACrB;AAAC,OAAC,SAAUA,QAAO;AAWjB,iBAAS,QAAQ,SAAS,cAAc;AACtC,iBAAO,QAAQ,QAAQ,cAAc,SAAU,GAAG,OAAO;AACvD,mBAAO,QAAQ,aAAa,CAAC,KAAK,IAAI;AAAA,UACxC,CAAC;AAAA,QACH;AAOA,iBAAS,GAAG,SAAS,cAAc,OAAO;AACxC,iBAAO,OAAO,QAAQ,SAAS,YAAY,GAAG,SAAS,EAAE;AAAA,QAC3D;AAQA,iBAAS,OAAO,SAAS,WAAW;AAClC,mBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,sBAAU,QAAQ,QAAQ,aAAa,WAAY;AACjD,qBAAO,QAAQ,UAAU;AAAA,YAC3B,CAAC;AAAA,UACH;AACA,iBAAO,QAAQ,QAAQ,aAAa,WAAW;AAAA,QACjD;AACA,YAAI,eAAe;AAAA;AAAA,UAEjB,MAAM;AAAA;AAAA,UAEN,iBAAiB;AAAA;AAAA;AAAA,UAGjB,YACE;AAAA;AAAA,UAEF,OACE;AAAA,QACJ;AACA,iBAAS,kBAAkB,OAAO;AAChC,iBAAO,WAAW,MAAM,KAAK,EAAE,QAAQ,MAAM,GAAG,IAAI;AAAA,QACtD;AACA,YAAI,0BAA0B;AAAA,UAC5B,aAAa;AAAA,QACf;AACA,YAAI,WAAW;AAAA,UACb;AAAA,YACE,aAAa,OACX,MACA,aAAa,kBACb,MACA,aAAa,aACb,MACA,aAAa;AAAA,UACjB;AAAA,QACF;AACA,YAAI,kBAAkB;AAAA,UACpB,aAAa,kBACX,MACA,aAAa,aACb,MACA,aAAa;AAAA,QACjB;AACA,YAAI,wBAAwB;AAAA,UAC1B,aAAa,OACX,MACA,aAAa,kBACb,MACA,aAAa;AAAA,QACjB;AACA,YAAI,UAAU,OAAO,mCAAmC,QAAQ,CAAC;AACjE,YAAI,cAAc,OAAO,0BAA0B,QAAQ,CAAC;AAC5D,YAAI,OAAO,qBAAqB;AAChC,YAAI,cAAc,QAAQ,qBAAqB,QAAQ,CAAC,MAAM,OAAO,CAAC;AACtE,YAAI,aAAa,QAAQ,mCAAmC,QAAQ;AAAA,UAClE;AAAA,UACA;AAAA,QACF,CAAC;AACD,YAAI,QAAQ,mBAAmB;AAC/B,YAAI,6BAA6B;AAAA,UAC/B,yCAAyC;AAAA,UACzC,CAAC,YAAY,KAAK;AAAA,QACpB;AACA,YAAI,eAAe;AAAA,UACjB,2CAA2C;AAAA,UAC3C,CAAC,SAAS,aAAa,KAAK;AAAA,QAC9B;AACA,YAAI,QAAQ,QAAQ,yBAAyB,QAAQ,CAAC,YAAY,CAAC;AACnE,YAAI,iBAAiB;AAAA,UACnB,mDAAmD;AAAA,UACnD,CAAC,OAAO,YAAY,KAAK;AAAA,QAC3B;AACA,YAAI,aAAa;AAAA,UACf,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAGA,YAAI,YAAY,8CAA8C;AAC9D,YAAI,gBAAgB,wBAAwB;AAC5C,YAAI,iBAAiB,kCAAkC;AACvD,QAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,OAAO,SAAS;AAAA,UACvD,QAAQ;AAAA,YACN;AAAA,cACE,SAAS,GAAG,kBAAkB,QAAQ,CAAC,cAAc,CAAC;AAAA,cACtD,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS,GAAG,mBAAmB,QAAQ,CAAC,aAAa,CAAC;AAAA,cACtD,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,cAAc;AAAA,YACZ;AAAA;AAAA;AAAA,cAGE,SAAS,GAAG,qCAAqC,QAAQ;AAAA,gBACvD;AAAA,cACF,CAAC;AAAA,cACD,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA;AAAA,cAGE,SAAS,GAAG,wCAAwC,QAAQ;AAAA,gBAC1D;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,cACD,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA;AAAA,cAGE,SAAS,GAAG,4BAA4B,QAAQ,CAAC,IAAI,CAAC;AAAA,cACtD,YAAY;AAAA,YACd;AAAA,YACA;AAAA;AAAA;AAAA;AAAA,cAIE,SAAS,GAAG,oBAAoB,QAAQ;AAAA,gBACtC;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,cACD,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA;AAAA;AAAA,cAIE,SAAS,GAAG,yBAAyB,QAAQ,CAAC,UAAU,CAAC;AAAA,cACzD,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA;AAAA,cAGE,SAAS,GAAG,oBAAoB,QAAQ,CAAC,IAAI,CAAC;AAAA,cAC9C,YAAY;AAAA,YACd;AAAA,YACA;AAAA;AAAA;AAAA;AAAA,cAIE,SAAS,GAAG,mCAAmC,QAAQ;AAAA,gBACrD;AAAA,cACF,CAAC;AAAA,cACD,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA;AAAA,cAGE,SAAS;AAAA,gBACP,2EACG;AAAA,gBACH,CAAC,gBAAgB,uBAAuB,IAAI;AAAA,cAC9C;AAAA,cACA,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,SAAS;AAAA;AAAA,UAET,QACE;AAAA,UACF,UAAU;AAAA,UACV,aAAa;AAAA,QACf,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,UAAU,UAAU;AAAA,UAC/C,OAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,UAAU,eAAe;AAAA,UACpD,mBAAmB;AAAA,YACjB,SAAS,GAAG,yBAAyB,QAAQ,CAAC,IAAI,CAAC;AAAA,YACnD,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,UAAU,cAAc;AAAA,UACnD,WAAW;AAAA;AAAA;AAAA,YAGT,SAAS;AAAA,cACP,+DAA+D;AAAA,cAC/D,CAAC,IAAI;AAAA,YACP;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,mBAAmB;AAAA;AAAA,YAEjB,SAAS;AAAA,cACP,kFACG;AAAA,cACH,CAAC,WAAW;AAAA,YACd;AAAA,YACA,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AAAA,UACA,eAAe;AAAA;AAAA;AAAA;AAAA,YAIb,SAAS;AAAA,cACP,+DAA+D;AAAA,cAC/D,CAAC,gBAAgB,UAAU;AAAA,YAC7B;AAAA,YACA,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,0BAA0B;AAAA;AAAA,YAExB,SAAS,GAAG,8BAA8B,QAAQ,CAAC,cAAc,CAAC;AAAA,YAClE,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA,kBAAkB;AAAA;AAAA,YAEhB,SAAS,GAAG,yBAAyB,QAAQ,CAAC,MAAM,OAAO,CAAC;AAAA,YAC5D,QAAQ;AAAA,cACN,UAAU,GAAG,SAAS,QAAQ,CAAC,IAAI,CAAC;AAAA,cACpC,SAAS;AAAA,gBACP,SAAS,OAAO,OAAO;AAAA,gBACvB,OAAO;AAAA,gBACP,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa;AAAA;AAAA;AAAA;AAAA,YAIX,SAAS;AAAA,cACP,kKACG;AAAA,cACH;AAAA,gBACE;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,SAAS;AAAA,gBACT;AAAA,gBACA,kBAAkB;AAAA,cACpB;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,oBAAoB;AAAA,gBAClB,SAAS,GAAG,+BAA+B,QAAQ;AAAA,kBACjD;AAAA,kBACA;AAAA,gBACF,CAAC;AAAA,gBACD,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,QAAQA,OAAM,UAAU;AAAA,cAC1B;AAAA,cACA,SAAS;AAAA,cACT,cAAc;AAAA,gBACZ,SAAS,OAAO,cAAc;AAAA,gBAC9B,QAAQ;AAAA,gBACR,QAAQ;AAAA,cACV;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA;AAAA,cAEN,WAAW;AAAA,gBACT,SACE;AAAA,gBACF,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,YAAI,2BAA2B,gBAAgB,MAAM;AACrD,YAAI,kCAAkC;AAAA,UACpC,iEAAiE;AAAA,UACjE,CAAC,wBAAwB;AAAA,QAC3B;AACA,YAAI,kBAAkB;AAAA,UACpB,QAAQ,+BAA+B,QAAQ;AAAA,YAC7C;AAAA,UACF,CAAC;AAAA,UACD;AAAA,QACF;AACA,YAAI,aACF,wEACG;AACL,YAAI,OAAO,QAAQ,0BAA0B,QAAQ;AAAA,UACnD;AAAA,UACA;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,UAAU,cAAc;AAAA,UACnD,WAAW;AAAA;AAAA;AAAA,YAGT,SAAS;AAAA,cACP,6EACG;AAAA,cACH,CAAC,YAAY,IAAI;AAAA,YACnB;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,QAAQ;AAAA,gBACN,SAAS,GAAG,iBAAiB,QAAQ,CAAC,UAAU,CAAC;AAAA,gBACjD,OAAO;AAAA,cACT;AAAA,cACA,uBAAuB;AAAA,gBACrB,SAAS,GAAG,aAAa,QAAQ,CAAC,eAAe,CAAC;AAAA,gBAClD,QAAQA,OAAM,UAAU;AAAA,cAC1B;AAAA,cACA,cAAc;AAAA,gBACZ,SAAS,OAAO,UAAU;AAAA,gBAC1B,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF,CAAC;AACD,YAAI,eAAe,aAAa;AAChC,YAAI,sBAAsB;AAAA,UACxB,QAAQ,+BAA+B,QAAQ;AAAA,YAC7C;AAAA,UACF,CAAC;AAAA,UACD;AAAA,QACF;AACA,YAAI,iBAAiB,QAAQ,qCAAqC,QAAQ;AAAA,UACxE;AAAA,UACA;AAAA,QACF,CAAC;AACD,YAAI,sBAAsB;AAAA,UACxB;AAAA,YACE,mEACG;AAAA,YACH,CAAC,wBAAwB;AAAA,UAC3B;AAAA,UACA;AAAA,QACF;AACA,YAAI,iBAAiB,QAAQ,qCAAqC,QAAQ;AAAA,UACxE;AAAA,UACA;AAAA,QACF,CAAC;AACD,iBAAS,0BAA0B,eAAe,oBAAoB;AACpE,iBAAO;AAAA,YACL,eAAe;AAAA,cACb,SAAS,GAAG,6BAA6B,QAAQ,CAAC,aAAa,CAAC;AAAA,cAChE,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,iBAAiB;AAAA,kBACf,SAAS,GAAG,sCAAsC,QAAQ;AAAA,oBACxD;AAAA,oBACA;AAAA,kBACF,CAAC;AAAA,kBACD,YAAY;AAAA,kBACZ,QAAQ;AAAA,oBACN,aAAa;AAAA,kBACf;AAAA,gBACF;AAAA,gBACA,aAAa;AAAA,gBACb,YAAY;AAAA,kBACV,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQA,OAAM,UAAU;AAAA,gBAC1B;AAAA,cACF;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,aAAa,UAAU,UAAU;AAAA,UAC/C,wBAAwB;AAAA,YACtB;AAAA,cACE,SAAS;AAAA,gBACP,4DAA4D;AAAA,gBAC5D,CAAC,cAAc;AAAA,cACjB;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,QAAQ,0BAA0B,gBAAgB,mBAAmB;AAAA,YACvE;AAAA,YACA;AAAA,cACE,SAAS,GAAG,4CAA4C,QAAQ;AAAA,gBAC9D;AAAA,cACF,CAAC;AAAA,cACD,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,QAAQ,0BAA0B,gBAAgB,mBAAmB;AAAA,YACvE;AAAA,UACF;AAAA,UACA,MAAM;AAAA,YACJ,SAAS,OAAO,SAAS;AAAA,YACzB,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,KAAKA,OAAM,UAAU;AAAA,MAChE,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}