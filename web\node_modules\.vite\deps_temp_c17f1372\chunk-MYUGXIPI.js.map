{"version": 3, "sources": ["../../highlight.js/lib/languages/fsharp.js"], "sourcesContent": ["/*\nLanguage: F#\nAuthor: <PERSON> <jona<PERSON>@follesoe.no>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <hen<PERSON>@haf.se>\nWebsite: https://docs.microsoft.com/en-us/dotnet/fsharp/\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction fsharp(hljs) {\n  const TYPEPARAM = {\n    begin: '<',\n    end: '>',\n    contains: [\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: /'[a-zA-Z0-9_]+/\n      })\n    ]\n  };\n\n  return {\n    name: 'F#',\n    aliases: ['fs'],\n    keywords:\n      'abstract and as assert base begin class default delegate do done ' +\n      'downcast downto elif else end exception extern false finally for ' +\n      'fun function global if in inherit inline interface internal lazy let ' +\n      'match member module mutable namespace new null of open or ' +\n      'override private public rec return sig static struct then to ' +\n      'true try type upcast use val void when while with yield',\n    illegal: /\\/\\*/,\n    contains: [\n      {\n        // monad builder keywords (matches before non-bang kws)\n        className: 'keyword',\n        begin: /\\b(yield|return|let|do)!/\n      },\n      {\n        className: 'string',\n        begin: '@\"',\n        end: '\"',\n        contains: [\n          {\n            begin: '\"\"'\n          }\n        ]\n      },\n      {\n        className: 'string',\n        begin: '\"\"\"',\n        end: '\"\"\"'\n      },\n      hljs.COMMENT('\\\\(\\\\*(\\\\s)', '\\\\*\\\\)', {\n        contains: [\"self\"]\n      }),\n      {\n        className: 'class',\n        beginKeywords: 'type',\n        end: '\\\\(|=|$',\n        excludeEnd: true,\n        contains: [\n          hljs.UNDERSCORE_TITLE_MODE,\n          TYPEPARAM\n        ]\n      },\n      {\n        className: 'meta',\n        begin: '\\\\[<',\n        end: '>\\\\]',\n        relevance: 10\n      },\n      {\n        className: 'symbol',\n        begin: '\\\\B(\\'[A-Za-z])\\\\b',\n        contains: [hljs.BACKSLASH_ESCAPE]\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        illegal: null\n      }),\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = fsharp;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,MAAM;AACpB,YAAM,YAAY;AAAA,QAChB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,KAAK,QAAQ,KAAK,YAAY;AAAA,YAC5B,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,IAAI;AAAA,QACd,UACE;AAAA,QAMF,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA,KAAK,QAAQ,eAAe,UAAU;AAAA,YACpC,UAAU,CAAC,MAAM;AAAA,UACnB,CAAC;AAAA,UACD;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,UAAU,CAAC,KAAK,gBAAgB;AAAA,UAClC;AAAA,UACA,KAAK;AAAA,UACL,KAAK,QAAQ,KAAK,mBAAmB;AAAA,YACnC,SAAS;AAAA,UACX,CAAC;AAAA,UACD,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}