{"version": 3, "sources": ["../../refractor/lang/twig.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = twig\ntwig.displayName = 'twig'\ntwig.aliases = []\nfunction twig(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  Prism.languages.twig = {\n    comment: /^\\{#[\\s\\S]*?#\\}$/,\n    'tag-name': {\n      pattern: /(^\\{%-?\\s*)\\w+/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    delimiter: {\n      pattern: /^\\{[{%]-?|-?[%}]\\}$/,\n      alias: 'punctuation'\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      inside: {\n        punctuation: /^['\"]|['\"]$/\n      }\n    },\n    keyword: /\\b(?:even|if|odd)\\b/,\n    boolean: /\\b(?:false|null|true)\\b/,\n    number: /\\b0x[\\dA-Fa-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][-+]?\\d+)?/,\n    operator: [\n      {\n        pattern:\n          /(\\s)(?:and|b-and|b-or|b-xor|ends with|in|is|matches|not|or|same as|starts with)(?=\\s)/,\n        lookbehind: true\n      },\n      /[=<>]=?|!=|\\*\\*?|\\/\\/?|\\?:?|[-+~%|]/\n    ],\n    punctuation: /[()\\[\\]{}:.,]/\n  }\n  Prism.hooks.add('before-tokenize', function (env) {\n    if (env.language !== 'twig') {\n      return\n    }\n    var pattern = /\\{(?:#[\\s\\S]*?#|%[\\s\\S]*?%|\\{[\\s\\S]*?\\})\\}/g\n    Prism.languages['markup-templating'].buildPlaceholders(env, 'twig', pattern)\n  })\n  Prism.hooks.add('after-tokenize', function (env) {\n    Prism.languages['markup-templating'].tokenizePlaceholders(env, 'twig')\n  })\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,SAAS,yBAAyB;AACxC,YAAM,UAAU,OAAO;AAAA,QACrB,SAAS;AAAA,QACT,YAAY;AAAA,UACV,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,UACR;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA;AAAA,QACF;AAAA,QACA,aAAa;AAAA,MACf;AACA,YAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,YAAI,IAAI,aAAa,QAAQ;AAC3B;AAAA,QACF;AACA,YAAI,UAAU;AACd,cAAM,UAAU,mBAAmB,EAAE,kBAAkB,KAAK,QAAQ,OAAO;AAAA,MAC7E,CAAC;AACD,YAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,cAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,MAAM;AAAA,MACvE,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}