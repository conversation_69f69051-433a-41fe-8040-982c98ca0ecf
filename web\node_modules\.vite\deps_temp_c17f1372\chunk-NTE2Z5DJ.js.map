{"version": 3, "sources": ["../../refractor/lang/yaml.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = yaml\nyaml.displayName = 'yaml'\nyaml.aliases = ['yml']\nfunction yaml(Prism) {\n  ;(function (Prism) {\n    // https://yaml.org/spec/1.2/spec.html#c-ns-anchor-property\n    // https://yaml.org/spec/1.2/spec.html#c-ns-alias-node\n    var anchorOrAlias = /[*&][^\\s[\\]{},]+/ // https://yaml.org/spec/1.2/spec.html#c-ns-tag-property\n    var tag =\n      /!(?:<[\\w\\-%#;/?:@&=+$,.!~*'()[\\]]+>|(?:[a-zA-Z\\d-]*!)?[\\w\\-%#;/?:@&=+$.~*'()]+)?/ // https://yaml.org/spec/1.2/spec.html#c-ns-properties(n,c)\n    var properties =\n      '(?:' +\n      tag.source +\n      '(?:[ \\t]+' +\n      anchorOrAlias.source +\n      ')?|' +\n      anchorOrAlias.source +\n      '(?:[ \\t]+' +\n      tag.source +\n      ')?)' // https://yaml.org/spec/1.2/spec.html#ns-plain(n,c)\n    // This is a simplified version that doesn't support \"#\" and multiline keys\n    // All these long scarry character classes are simplified versions of YAML's characters\n    var plainKey =\n      /(?:[^\\s\\x00-\\x08\\x0e-\\x1f!\"#%&'*,\\-:>?@[\\]`{|}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]|[?:-]<PLAIN>)(?:[ \\t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(\n        /<PLAIN>/g,\n        function () {\n          return /[^\\s\\x00-\\x08\\x0e-\\x1f,[\\]{}\\x7f-\\x84\\x86-\\x9f\\ud800-\\udfff\\ufffe\\uffff]/\n            .source\n        }\n      )\n    var string = /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/.source\n    /**\n     *\n     * @param {string} value\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function createValuePattern(value, flags) {\n      flags = (flags || '').replace(/m/g, '') + 'm' // add m flag\n      var pattern =\n        /([:\\-,[{]\\s*(?:\\s<<prop>>[ \\t]+)?)(?:<<value>>)(?=[ \\t]*(?:$|,|\\]|\\}|(?:[\\r\\n]\\s*)?#))/.source\n          .replace(/<<prop>>/g, function () {\n            return properties\n          })\n          .replace(/<<value>>/g, function () {\n            return value\n          })\n      return RegExp(pattern, flags)\n    }\n    Prism.languages.yaml = {\n      scalar: {\n        pattern: RegExp(\n          /([\\-:]\\s*(?:\\s<<prop>>[ \\t]+)?[|>])[ \\t]*(?:((?:\\r?\\n|\\r)[ \\t]+)\\S[^\\r\\n]*(?:\\2[^\\r\\n]+)*)/.source.replace(\n            /<<prop>>/g,\n            function () {\n              return properties\n            }\n          )\n        ),\n        lookbehind: true,\n        alias: 'string'\n      },\n      comment: /#.*/,\n      key: {\n        pattern: RegExp(\n          /((?:^|[:\\-,[{\\r\\n?])[ \\t]*(?:<<prop>>[ \\t]+)?)<<key>>(?=\\s*:\\s)/.source\n            .replace(/<<prop>>/g, function () {\n              return properties\n            })\n            .replace(/<<key>>/g, function () {\n              return '(?:' + plainKey + '|' + string + ')'\n            })\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'atrule'\n      },\n      directive: {\n        pattern: /(^[ \\t]*)%.+/m,\n        lookbehind: true,\n        alias: 'important'\n      },\n      datetime: {\n        pattern: createValuePattern(\n          /\\d{4}-\\d\\d?-\\d\\d?(?:[tT]|[ \\t]+)\\d\\d?:\\d{2}:\\d{2}(?:\\.\\d*)?(?:[ \\t]*(?:Z|[-+]\\d\\d?(?::\\d{2})?))?|\\d{4}-\\d{2}-\\d{2}|\\d\\d?:\\d{2}(?::\\d{2}(?:\\.\\d*)?)?/\n            .source\n        ),\n        lookbehind: true,\n        alias: 'number'\n      },\n      boolean: {\n        pattern: createValuePattern(/false|true/.source, 'i'),\n        lookbehind: true,\n        alias: 'important'\n      },\n      null: {\n        pattern: createValuePattern(/null|~/.source, 'i'),\n        lookbehind: true,\n        alias: 'important'\n      },\n      string: {\n        pattern: createValuePattern(string),\n        lookbehind: true,\n        greedy: true\n      },\n      number: {\n        pattern: createValuePattern(\n          /[+-]?(?:0x[\\da-f]+|0o[0-7]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|\\.inf|\\.nan)/\n            .source,\n          'i'\n        ),\n        lookbehind: true\n      },\n      tag: tag,\n      important: anchorOrAlias,\n      punctuation: /---|[:[\\]{}\\-,|>?]|\\.\\.\\./\n    }\n    Prism.languages.yml = Prism.languages.yaml\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC,KAAK;AACrB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AAGjB,YAAI,gBAAgB;AACpB,YAAI,MACF;AACF,YAAI,aACF,QACA,IAAI,SACJ,aACA,cAAc,SACd,QACA,cAAc,SACd,aACA,IAAI,SACJ;AAGF,YAAI,WACF,kJAAkJ,OAAO;AAAA,UACvJ;AAAA,UACA,WAAY;AACV,mBAAO,2EACJ;AAAA,UACL;AAAA,QACF;AACF,YAAI,SAAS,8CAA8C;AAO3D,iBAAS,mBAAmB,OAAO,OAAO;AACxC,mBAAS,SAAS,IAAI,QAAQ,MAAM,EAAE,IAAI;AAC1C,cAAI,UACF,yFAAyF,OACtF,QAAQ,aAAa,WAAY;AAChC,mBAAO;AAAA,UACT,CAAC,EACA,QAAQ,cAAc,WAAY;AACjC,mBAAO;AAAA,UACT,CAAC;AACL,iBAAO,OAAO,SAAS,KAAK;AAAA,QAC9B;AACA,QAAAA,OAAM,UAAU,OAAO;AAAA,UACrB,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,6FAA6F,OAAO;AAAA,gBAClG;AAAA,gBACA,WAAY;AACV,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,UACT,KAAK;AAAA,YACH,SAAS;AAAA,cACP,kEAAkE,OAC/D,QAAQ,aAAa,WAAY;AAChC,uBAAO;AAAA,cACT,CAAC,EACA,QAAQ,YAAY,WAAY;AAC/B,uBAAO,QAAQ,WAAW,MAAM,SAAS;AAAA,cAC3C,CAAC;AAAA,YACL;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,WAAW;AAAA,YACT,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,cACP,sJACG;AAAA,YACL;AAAA,YACA,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACP,SAAS,mBAAmB,aAAa,QAAQ,GAAG;AAAA,YACpD,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,YACJ,SAAS,mBAAmB,SAAS,QAAQ,GAAG;AAAA,YAChD,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,YACN,SAAS,mBAAmB,MAAM;AAAA,YAClC,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,iFACG;AAAA,cACH;AAAA,YACF;AAAA,YACA,YAAY;AAAA,UACd;AAAA,UACA;AAAA,UACA,WAAW;AAAA,UACX,aAAa;AAAA,QACf;AACA,QAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AAAA,MACxC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}