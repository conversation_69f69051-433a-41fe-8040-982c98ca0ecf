{"version": 3, "sources": ["../../refractor/lang/keyman.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = keyman\nkeyman.displayName = 'keyman'\nkeyman.aliases = []\nfunction keyman(Prism) {\n  Prism.languages.keyman = {\n    comment: {\n      pattern: /\\bc .*/i,\n      greedy: true\n    },\n    string: {\n      pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n      greedy: true\n    },\n    'virtual-key': {\n      pattern:\n        /\\[\\s*(?:(?:ALT|CAPS|CTRL|LALT|LCTRL|NCAPS|RALT|RCTRL|SHIFT)\\s+)*(?:[TKU]_[\\w?]+|[A-E]\\d\\d?|\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')\\s*\\]/i,\n      greedy: true,\n      alias: 'function' // alias for styles\n    },\n    // https://help.keyman.com/developer/language/guide/headers\n    'header-keyword': {\n      pattern: /&\\w+/,\n      alias: 'bold' // alias for styles\n    },\n    'header-statement': {\n      pattern:\n        /\\b(?:bitmap|bitmaps|caps always off|caps on only|copyright|hotkey|language|layout|message|name|shift frees caps|version)\\b/i,\n      alias: 'bold' // alias for styles\n    },\n    'rule-keyword': {\n      pattern:\n        /\\b(?:any|baselayout|beep|call|context|deadkey|dk|if|index|layer|notany|nul|outs|platform|reset|return|save|set|store|use)\\b/i,\n      alias: 'keyword'\n    },\n    'structural-keyword': {\n      pattern: /\\b(?:ansi|begin|group|match|nomatch|unicode|using keys)\\b/i,\n      alias: 'keyword'\n    },\n    'compile-target': {\n      pattern: /\\$(?:keyman|keymanonly|keymanweb|kmfl|weaver):/i,\n      alias: 'property'\n    },\n    // U+####, x###, d### characters and numbers\n    number: /\\b(?:U\\+[\\dA-F]+|d\\d+|x[\\da-f]+|\\d+)\\b/i,\n    operator: /[+>\\\\$]|\\.\\./,\n    punctuation: /[()=,]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS;AAAA,QACvB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,eAAe;AAAA,UACb,SACE;AAAA,UACF,QAAQ;AAAA,UACR,OAAO;AAAA;AAAA,QACT;AAAA;AAAA,QAEA,kBAAkB;AAAA,UAChB,SAAS;AAAA,UACT,OAAO;AAAA;AAAA,QACT;AAAA,QACA,oBAAoB;AAAA,UAClB,SACE;AAAA,UACF,OAAO;AAAA;AAAA,QACT;AAAA,QACA,gBAAgB;AAAA,UACd,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,sBAAsB;AAAA,UACpB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,UAChB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA;AAAA,QAEA,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}