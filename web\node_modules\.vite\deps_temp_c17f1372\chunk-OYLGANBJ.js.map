{"version": 3, "sources": ["../../highlight.js/lib/languages/1c.js"], "sourcesContent": ["/*\nLanguage: 1C:Enterprise\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: built-in language 1C:Enterprise (v7, v8)\nCategory: enterprise\n*/\n\nfunction _1c(hljs) {\n\n  // общий паттерн для определения идентификаторов\n  var UNDERSCORE_IDENT_RE = '[A-Za-zА-Яа-яёЁ_][A-Za-zА-Яа-яёЁ_0-9]+';\n\n  // v7 уникальные ключевые слова, отсутствующие в v8 ==> keyword\n  var v7_keywords =\n  'далее ';\n\n  // v8 ключевые слова ==> keyword\n  var v8_keywords =\n  'возврат вызватьисключение выполнить для если и из или иначе иначеесли исключение каждого конецесли ' +\n  'конецпопытки конеццикла не новый перейти перем по пока попытка прервать продолжить тогда цикл экспорт ';\n\n  // keyword : ключевые слова\n  var KEYWORD = v7_keywords + v8_keywords;\n\n  // v7 уникальные директивы, отсутствующие в v8 ==> meta-keyword\n  var v7_meta_keywords =\n  'загрузитьизфайла ';\n\n  // v8 ключевые слова в инструкциях препроцессора, директивах компиляции, аннотациях ==> meta-keyword\n  var v8_meta_keywords =\n  'вебклиент вместо внешнеесоединение клиент конецобласти мобильноеприложениеклиент мобильноеприложениесервер ' +\n  'наклиенте наклиентенасервере наклиентенасерверебезконтекста насервере насерверебезконтекста область перед ' +\n  'после сервер толстыйклиентобычноеприложение толстыйклиентуправляемоеприложение тонкийклиент ';\n\n  // meta-keyword : ключевые слова в инструкциях препроцессора, директивах компиляции, аннотациях\n  var METAKEYWORD = v7_meta_keywords + v8_meta_keywords;\n\n  // v7 системные константы ==> built_in\n  var v7_system_constants =\n  'разделительстраниц разделительстрок символтабуляции ';\n\n  // v7 уникальные методы глобального контекста, отсутствующие в v8 ==> built_in\n  var v7_global_context_methods =\n  'ansitooem oemtoansi ввестивидсубконто ввестиперечисление ввестипериод ввестиплансчетов выбранныйплансчетов ' +\n  'датагод датамесяц датачисло заголовоксистемы значениевстроку значениеизстроки каталогиб каталогпользователя ' +\n  'кодсимв конгода конецпериодаби конецрассчитанногопериодаби конецстандартногоинтервала конквартала конмесяца ' +\n  'коннедели лог лог10 максимальноеколичествосубконто названиеинтерфейса названиенабораправ назначитьвид ' +\n  'назначитьсчет найтиссылки началопериодаби началостандартногоинтервала начгода начквартала начмесяца ' +\n  'начнедели номерднягода номерднянедели номернеделигода обработкаожидания основнойжурналрасчетов ' +\n  'основнойплансчетов основнойязык очиститьокносообщений периодстр получитьвремята получитьдатута ' +\n  'получитьдокументта получитьзначенияотбора получитьпозициюта получитьпустоезначение получитьта ' +\n  'префиксавтонумерации пропись пустоезначение разм разобратьпозициюдокумента рассчитатьрегистрына ' +\n  'рассчитатьрегистрыпо симв создатьобъект статусвозврата стрколичествострок сформироватьпозициюдокумента ' +\n  'счетпокоду текущеевремя типзначения типзначениястр установитьтана установитьтапо фиксшаблон шаблон ';\n\n  // v8 методы глобального контекста ==> built_in\n  var v8_global_context_methods =\n  'acos asin atan base64значение base64строка cos exp log log10 pow sin sqrt tan xmlзначение xmlстрока ' +\n  'xmlтип xmlтипзнч активноеокно безопасныйрежим безопасныйрежимразделенияданных булево ввестидату ввестизначение ' +\n  'ввестистроку ввестичисло возможностьчтенияxml вопрос восстановитьзначение врег выгрузитьжурналрегистрации ' +\n  'выполнитьобработкуоповещения выполнитьпроверкуправдоступа вычислить год данныеформывзначение дата день деньгода ' +\n  'деньнедели добавитьмесяц заблокироватьданныедляредактирования заблокироватьработупользователя завершитьработусистемы ' +\n  'загрузитьвнешнююкомпоненту закрытьсправку записатьjson записатьxml записатьдатуjson записьжурналарегистрации ' +\n  'заполнитьзначениясвойств запроситьразрешениепользователя запуститьприложение запуститьсистему зафиксироватьтранзакцию ' +\n  'значениевданныеформы значениевстрокувнутр значениевфайл значениезаполнено значениеизстрокивнутр значениеизфайла ' +\n  'изxmlтипа импортмоделиxdto имякомпьютера имяпользователя инициализироватьпредопределенныеданные информацияобошибке ' +\n  'каталогбиблиотекимобильногоустройства каталогвременныхфайлов каталогдокументов каталогпрограммы кодироватьстроку ' +\n  'кодлокализацииинформационнойбазы кодсимвола командасистемы конецгода конецдня конецквартала конецмесяца конецминуты ' +\n  'конецнедели конецчаса конфигурациябазыданныхизмененадинамически конфигурацияизменена копироватьданныеформы ' +\n  'копироватьфайл краткоепредставлениеошибки лев макс местноевремя месяц мин минута монопольныйрежим найти ' +\n  'найтинедопустимыесимволыxml найтиокнопонавигационнойссылке найтипомеченныенаудаление найтипоссылкам найтифайлы ' +\n  'началогода началодня началоквартала началомесяца началоминуты началонедели началочаса начатьзапросразрешенияпользователя ' +\n  'начатьзапускприложения начатькопированиефайла начатьперемещениефайла начатьподключениевнешнейкомпоненты ' +\n  'начатьподключениерасширенияработыскриптографией начатьподключениерасширенияработысфайлами начатьпоискфайлов ' +\n  'начатьполучениекаталогавременныхфайлов начатьполучениекаталогадокументов начатьполучениерабочегокаталогаданныхпользователя ' +\n  'начатьполучениефайлов начатьпомещениефайла начатьпомещениефайлов начатьсозданиедвоичныхданныхизфайла начатьсозданиекаталога ' +\n  'начатьтранзакцию начатьудалениефайлов начатьустановкувнешнейкомпоненты начатьустановкурасширенияработыскриптографией ' +\n  'начатьустановкурасширенияработысфайлами неделягода необходимостьзавершениясоединения номерсеансаинформационнойбазы ' +\n  'номерсоединенияинформационнойбазы нрег нстр обновитьинтерфейс обновитьнумерациюобъектов обновитьповторноиспользуемыезначения ' +\n  'обработкапрерыванияпользователя объединитьфайлы окр описаниеошибки оповестить оповеститьобизменении ' +\n  'отключитьобработчикзапросанастроекклиенталицензирования отключитьобработчикожидания отключитьобработчикоповещения ' +\n  'открытьзначение открытьиндекссправки открытьсодержаниесправки открытьсправку открытьформу открытьформумодально ' +\n  'отменитьтранзакцию очиститьжурналрегистрации очиститьнастройкипользователя очиститьсообщения параметрыдоступа ' +\n  'перейтипонавигационнойссылке переместитьфайл подключитьвнешнююкомпоненту ' +\n  'подключитьобработчикзапросанастроекклиенталицензирования подключитьобработчикожидания подключитьобработчикоповещения ' +\n  'подключитьрасширениеработыскриптографией подключитьрасширениеработысфайлами подробноепредставлениеошибки ' +\n  'показатьвводдаты показатьвводзначения показатьвводстроки показатьвводчисла показатьвопрос показатьзначение ' +\n  'показатьинформациюобошибке показатьнакарте показатьоповещениепользователя показатьпредупреждение полноеимяпользователя ' +\n  'получитьcomобъект получитьxmlтип получитьадреспоместоположению получитьблокировкусеансов получитьвремязавершенияспящегосеанса ' +\n  'получитьвремязасыпанияпассивногосеанса получитьвремяожиданияблокировкиданных получитьданныевыбора ' +\n  'получитьдополнительныйпараметрклиенталицензирования получитьдопустимыекодылокализации получитьдопустимыечасовыепояса ' +\n  'получитьзаголовокклиентскогоприложения получитьзаголовоксистемы получитьзначенияотборажурналарегистрации ' +\n  'получитьидентификаторконфигурации получитьизвременногохранилища получитьимявременногофайла ' +\n  'получитьимяклиенталицензирования получитьинформациюэкрановклиента получитьиспользованиежурналарегистрации ' +\n  'получитьиспользованиесобытияжурналарегистрации получитькраткийзаголовокприложения получитьмакетоформления ' +\n  'получитьмаскувсефайлы получитьмаскувсефайлыклиента получитьмаскувсефайлысервера получитьместоположениепоадресу ' +\n  'получитьминимальнуюдлинупаролейпользователей получитьнавигационнуюссылку получитьнавигационнуюссылкуинформационнойбазы ' +\n  'получитьобновлениеконфигурациибазыданных получитьобновлениепредопределенныхданныхинформационнойбазы получитьобщиймакет ' +\n  'получитьобщуюформу получитьокна получитьоперативнуюотметкувремени получитьотключениебезопасногорежима ' +\n  'получитьпараметрыфункциональныхопцийинтерфейса получитьполноеимяпредопределенногозначения ' +\n  'получитьпредставлениянавигационныхссылок получитьпроверкусложностипаролейпользователей получитьразделительпути ' +\n  'получитьразделительпутиклиента получитьразделительпутисервера получитьсеансыинформационнойбазы ' +\n  'получитьскоростьклиентскогосоединения получитьсоединенияинформационнойбазы получитьсообщенияпользователю ' +\n  'получитьсоответствиеобъектаиформы получитьсоставстандартногоинтерфейсаodata получитьструктурухранениябазыданных ' +\n  'получитьтекущийсеансинформационнойбазы получитьфайл получитьфайлы получитьформу получитьфункциональнуюопцию ' +\n  'получитьфункциональнуюопциюинтерфейса получитьчасовойпоясинформационнойбазы пользователиос поместитьвовременноехранилище ' +\n  'поместитьфайл поместитьфайлы прав праводоступа предопределенноезначение представлениекодалокализации представлениепериода ' +\n  'представлениеправа представлениеприложения представлениесобытияжурналарегистрации представлениечасовогопояса предупреждение ' +\n  'прекратитьработусистемы привилегированныйрежим продолжитьвызов прочитатьjson прочитатьxml прочитатьдатуjson пустаястрока ' +\n  'рабочийкаталогданныхпользователя разблокироватьданныедляредактирования разделитьфайл разорватьсоединениесвнешнимисточникомданных ' +\n  'раскодироватьстроку рольдоступна секунда сигнал символ скопироватьжурналрегистрации смещениелетнеговремени ' +\n  'смещениестандартноговремени соединитьбуферыдвоичныхданных создатькаталог создатьфабрикуxdto сокрл сокрлп сокрп сообщить ' +\n  'состояние сохранитьзначение сохранитьнастройкипользователя сред стрдлина стрзаканчиваетсяна стрзаменить стрнайти стрначинаетсяс ' +\n  'строка строкасоединенияинформационнойбазы стрполучитьстроку стрразделить стрсоединить стрсравнить стрчисловхождений '+\n  'стрчислострок стршаблон текущаядата текущаядатасеанса текущаяуниверсальнаядата текущаяуниверсальнаядатавмиллисекундах ' +\n  'текущийвариантинтерфейсаклиентскогоприложения текущийвариантосновногошрифтаклиентскогоприложения текущийкодлокализации ' +\n  'текущийрежимзапуска текущийязык текущийязыксистемы тип типзнч транзакцияактивна трег удалитьданныеинформационнойбазы ' +\n  'удалитьизвременногохранилища удалитьобъекты удалитьфайлы универсальноевремя установитьбезопасныйрежим ' +\n  'установитьбезопасныйрежимразделенияданных установитьблокировкусеансов установитьвнешнююкомпоненту ' +\n  'установитьвремязавершенияспящегосеанса установитьвремязасыпанияпассивногосеанса установитьвремяожиданияблокировкиданных ' +\n  'установитьзаголовокклиентскогоприложения установитьзаголовоксистемы установитьиспользованиежурналарегистрации ' +\n  'установитьиспользованиесобытияжурналарегистрации установитькраткийзаголовокприложения ' +\n  'установитьминимальнуюдлинупаролейпользователей установитьмонопольныйрежим установитьнастройкиклиенталицензирования ' +\n  'установитьобновлениепредопределенныхданныхинформационнойбазы установитьотключениебезопасногорежима ' +\n  'установитьпараметрыфункциональныхопцийинтерфейса установитьпривилегированныйрежим ' +\n  'установитьпроверкусложностипаролейпользователей установитьрасширениеработыскриптографией ' +\n  'установитьрасширениеработысфайлами установитьсоединениесвнешнимисточникомданных установитьсоответствиеобъектаиформы ' +\n  'установитьсоставстандартногоинтерфейсаodata установитьчасовойпоясинформационнойбазы установитьчасовойпояссеанса ' +\n  'формат цел час часовойпояс часовойпояссеанса число числопрописью этоадресвременногохранилища ';\n\n  // v8 свойства глобального контекста ==> built_in\n  var v8_global_context_property =\n  'wsссылки библиотекакартинок библиотекамакетовоформлениякомпоновкиданных библиотекастилей бизнеспроцессы ' +\n  'внешниеисточникиданных внешниеобработки внешниеотчеты встроенныепокупки главныйинтерфейс главныйстиль ' +\n  'документы доставляемыеуведомления журналыдокументов задачи информацияобинтернетсоединении использованиерабочейдаты ' +\n  'историяработыпользователя константы критерииотбора метаданные обработки отображениерекламы отправкадоставляемыхуведомлений ' +\n  'отчеты панельзадачос параметрзапуска параметрысеанса перечисления планывидоврасчета планывидовхарактеристик ' +\n  'планыобмена планысчетов полнотекстовыйпоиск пользователиинформационнойбазы последовательности проверкавстроенныхпокупок ' +\n  'рабочаядата расширенияконфигурации регистрыбухгалтерии регистрынакопления регистрырасчета регистрысведений ' +\n  'регламентныезадания сериализаторxdto справочники средствагеопозиционирования средствакриптографии средствамультимедиа ' +\n  'средстваотображениярекламы средствапочты средствателефонии фабрикаxdto файловыепотоки фоновыезадания хранилищанастроек ' +\n  'хранилищевариантовотчетов хранилищенастроекданныхформ хранилищеобщихнастроек хранилищепользовательскихнастроекдинамическихсписков ' +\n  'хранилищепользовательскихнастроекотчетов хранилищесистемныхнастроек ';\n\n  // built_in : встроенные или библиотечные объекты (константы, классы, функции)\n  var BUILTIN =\n  v7_system_constants +\n  v7_global_context_methods + v8_global_context_methods +\n  v8_global_context_property;\n\n  // v8 системные наборы значений ==> class\n  var v8_system_sets_of_values =\n  'webцвета windowsцвета windowsшрифты библиотекакартинок рамкистиля символы цветастиля шрифтыстиля ';\n\n  // v8 системные перечисления - интерфейсные ==> class\n  var v8_system_enums_interface =\n  'автоматическоесохранениеданныхформывнастройках автонумерациявформе автораздвижениесерий ' +\n  'анимациядиаграммы вариантвыравниванияэлементовизаголовков вариантуправлениявысотойтаблицы ' +\n  'вертикальнаяпрокруткаформы вертикальноеположение вертикальноеположениеэлемента видгруппыформы ' +\n  'виддекорацииформы виддополненияэлементаформы видизмененияданных видкнопкиформы видпереключателя ' +\n  'видподписейкдиаграмме видполяформы видфлажка влияниеразмеранапузырекдиаграммы горизонтальноеположение ' +\n  'горизонтальноеположениеэлемента группировкаколонок группировкаподчиненныхэлементовформы ' +\n  'группыиэлементы действиеперетаскивания дополнительныйрежимотображения допустимыедействияперетаскивания ' +\n  'интервалмеждуэлементамиформы использованиевывода использованиеполосыпрокрутки ' +\n  'используемоезначениеточкибиржевойдиаграммы историявыборапривводе источникзначенийоситочекдиаграммы ' +\n  'источникзначенияразмерапузырькадиаграммы категориягруппыкоманд максимумсерий начальноеотображениедерева ' +\n  'начальноеотображениесписка обновлениетекстаредактирования ориентациядендрограммы ориентациядиаграммы ' +\n  'ориентацияметокдиаграммы ориентацияметоксводнойдиаграммы ориентацияэлементаформы отображениевдиаграмме ' +\n  'отображениевлегендедиаграммы отображениегруппыкнопок отображениезаголовкашкалыдиаграммы ' +\n  'отображениезначенийсводнойдиаграммы отображениезначенияизмерительнойдиаграммы ' +\n  'отображениеинтерваладиаграммыганта отображениекнопки отображениекнопкивыбора отображениеобсужденийформы ' +\n  'отображениеобычнойгруппы отображениеотрицательныхзначенийпузырьковойдиаграммы отображениепанелипоиска ' +\n  'отображениеподсказки отображениепредупрежденияприредактировании отображениеразметкиполосырегулирования ' +\n  'отображениестраницформы отображениетаблицы отображениетекстазначениядиаграммыганта ' +\n  'отображениеуправленияобычнойгруппы отображениефигурыкнопки палитрацветовдиаграммы поведениеобычнойгруппы ' +\n  'поддержкамасштабадендрограммы поддержкамасштабадиаграммыганта поддержкамасштабасводнойдиаграммы ' +\n  'поисквтаблицепривводе положениезаголовкаэлементаформы положениекартинкикнопкиформы ' +\n  'положениекартинкиэлементаграфическойсхемы положениекоманднойпанелиформы положениекоманднойпанелиэлементаформы ' +\n  'положениеопорнойточкиотрисовки положениеподписейкдиаграмме положениеподписейшкалызначенийизмерительнойдиаграммы ' +\n  'положениесостоянияпросмотра положениестрокипоиска положениетекстасоединительнойлинии положениеуправленияпоиском ' +\n  'положениешкалывремени порядокотображенияточекгоризонтальнойгистограммы порядоксерийвлегендедиаграммы ' +\n  'размеркартинки расположениезаголовкашкалыдиаграммы растягиваниеповертикалидиаграммыганта ' +\n  'режимавтоотображениясостояния режимвводастроктаблицы режимвыборанезаполненного режимвыделениядаты ' +\n  'режимвыделениястрокитаблицы режимвыделениятаблицы режимизмененияразмера режимизменениясвязанногозначения ' +\n  'режимиспользованиядиалогапечати режимиспользованияпараметракоманды режиммасштабированияпросмотра ' +\n  'режимосновногоокнаклиентскогоприложения режимоткрытияокнаформы режимотображениявыделения ' +\n  'режимотображениягеографическойсхемы режимотображениязначенийсерии режимотрисовкисеткиграфическойсхемы ' +\n  'режимполупрозрачностидиаграммы режимпробеловдиаграммы режимразмещениянастранице режимредактированияколонки ' +\n  'режимсглаживаниядиаграммы режимсглаживанияиндикатора режимсписказадач сквозноевыравнивание ' +\n  'сохранениеданныхформывнастройках способзаполнениятекстазаголовкашкалыдиаграммы ' +\n  'способопределенияограничивающегозначениядиаграммы стандартнаягруппакоманд стандартноеоформление ' +\n  'статусоповещенияпользователя стильстрелки типаппроксимациилиниитрендадиаграммы типдиаграммы ' +\n  'типединицышкалывремени типимпортасерийслоягеографическойсхемы типлиниигеографическойсхемы типлиниидиаграммы ' +\n  'типмаркерагеографическойсхемы типмаркерадиаграммы типобластиоформления ' +\n  'типорганизацииисточникаданныхгеографическойсхемы типотображениясериислоягеографическойсхемы ' +\n  'типотображенияточечногообъектагеографическойсхемы типотображенияшкалыэлементалегендыгеографическойсхемы ' +\n  'типпоискаобъектовгеографическойсхемы типпроекциигеографическойсхемы типразмещенияизмерений ' +\n  'типразмещенияреквизитовизмерений типрамкиэлементауправления типсводнойдиаграммы ' +\n  'типсвязидиаграммыганта типсоединениязначенийпосериямдиаграммы типсоединенияточекдиаграммы ' +\n  'типсоединительнойлинии типстороныэлементаграфическойсхемы типформыотчета типшкалырадарнойдиаграммы ' +\n  'факторлиниитрендадиаграммы фигуракнопки фигурыграфическойсхемы фиксациявтаблице форматдняшкалывремени ' +\n  'форматкартинки ширинаподчиненныхэлементовформы ';\n\n  // v8 системные перечисления - свойства прикладных объектов ==> class\n  var v8_system_enums_objects_properties =\n  'виддвижениябухгалтерии виддвижениянакопления видпериодарегистрарасчета видсчета видточкимаршрутабизнеспроцесса ' +\n  'использованиеагрегатарегистранакопления использованиегруппиэлементов использованиережимапроведения ' +\n  'использованиесреза периодичностьагрегатарегистранакопления режимавтовремя режимзаписидокумента режимпроведениядокумента ';\n\n  // v8 системные перечисления - планы обмена ==> class\n  var v8_system_enums_exchange_plans =\n  'авторегистрацияизменений допустимыйномерсообщения отправкаэлементаданных получениеэлементаданных ';\n\n  // v8 системные перечисления - табличный документ ==> class\n  var v8_system_enums_tabular_document =\n  'использованиерасшифровкитабличногодокумента ориентациястраницы положениеитоговколоноксводнойтаблицы ' +\n  'положениеитоговстроксводнойтаблицы положениетекстаотносительнокартинки расположениезаголовкагруппировкитабличногодокумента ' +\n  'способчтениязначенийтабличногодокумента типдвустороннейпечати типзаполненияобластитабличногодокумента ' +\n  'типкурсоровтабличногодокумента типлиниирисункатабличногодокумента типлинииячейкитабличногодокумента ' +\n  'типнаправленияпереходатабличногодокумента типотображениявыделениятабличногодокумента типотображениялинийсводнойтаблицы ' +\n  'типразмещениятекстатабличногодокумента типрисункатабличногодокумента типсмещениятабличногодокумента ' +\n  'типузоратабличногодокумента типфайлатабличногодокумента точностьпечати чередованиерасположениястраниц ';\n\n  // v8 системные перечисления - планировщик ==> class\n  var v8_system_enums_sheduler =\n  'отображениевремениэлементовпланировщика ';\n\n  // v8 системные перечисления - форматированный документ ==> class\n  var v8_system_enums_formatted_document =\n  'типфайлаформатированногодокумента ';\n\n  // v8 системные перечисления - запрос ==> class\n  var v8_system_enums_query =\n  'обходрезультатазапроса типзаписизапроса ';\n\n  // v8 системные перечисления - построитель отчета ==> class\n  var v8_system_enums_report_builder =\n  'видзаполнениярасшифровкипостроителяотчета типдобавленияпредставлений типизмеренияпостроителяотчета типразмещенияитогов ';\n\n  // v8 системные перечисления - работа с файлами ==> class\n  var v8_system_enums_files =\n  'доступкфайлу режимдиалогавыборафайла режимоткрытияфайла ';\n\n  // v8 системные перечисления - построитель запроса ==> class\n  var v8_system_enums_query_builder =\n  'типизмеренияпостроителязапроса ';\n\n  // v8 системные перечисления - анализ данных ==> class\n  var v8_system_enums_data_analysis =\n  'видданныханализа методкластеризации типединицыинтервалавременианализаданных типзаполнениятаблицырезультатаанализаданных ' +\n  'типиспользованиячисловыхзначенийанализаданных типисточникаданныхпоискаассоциаций типколонкианализаданныхдереворешений ' +\n  'типколонкианализаданныхкластеризация типколонкианализаданныхобщаястатистика типколонкианализаданныхпоискассоциаций ' +\n  'типколонкианализаданныхпоискпоследовательностей типколонкимоделипрогноза типмерырасстоянияанализаданных ' +\n  'типотсеченияправилассоциации типполяанализаданных типстандартизациианализаданных типупорядочиванияправилассоциациианализаданных ' +\n  'типупорядочиванияшаблоновпоследовательностейанализаданных типупрощениядереварешений ';\n\n  // v8 системные перечисления - xml, json, xs, dom, xdto, web-сервисы ==> class\n  var v8_system_enums_xml_json_xs_dom_xdto_ws =\n  'wsнаправлениепараметра вариантxpathxs вариантзаписидатыjson вариантпростоготипаxs видгруппымоделиxs видфасетаxdto ' +\n  'действиепостроителяdom завершенностьпростоготипаxs завершенностьсоставноготипаxs завершенностьсхемыxs запрещенныеподстановкиxs ' +\n  'исключениягруппподстановкиxs категорияиспользованияатрибутаxs категорияограниченияидентичностиxs категорияограниченияпространствименxs ' +\n  'методнаследованияxs модельсодержимогоxs назначениетипаxml недопустимыеподстановкиxs обработкапробельныхсимволовxs обработкасодержимогоxs ' +\n  'ограничениезначенияxs параметрыотбораузловdom переносстрокjson позициявдокументеdom пробельныесимволыxml типатрибутаxml типзначенияjson ' +\n  'типканоническогоxml типкомпонентыxs типпроверкиxml типрезультатаdomxpath типузлаdom типузлаxml формаxml формапредставленияxs ' +\n  'форматдатыjson экранированиесимволовjson ';\n\n  // v8 системные перечисления - система компоновки данных ==> class\n  var v8_system_enums_data_composition_system =\n  'видсравнениякомпоновкиданных действиеобработкирасшифровкикомпоновкиданных направлениесортировкикомпоновкиданных ' +\n  'расположениевложенныхэлементоврезультатакомпоновкиданных расположениеитоговкомпоновкиданных расположениегруппировкикомпоновкиданных ' +\n  'расположениеполейгруппировкикомпоновкиданных расположениеполякомпоновкиданных расположениереквизитовкомпоновкиданных ' +\n  'расположениересурсовкомпоновкиданных типбухгалтерскогоостаткакомпоновкиданных типвыводатекстакомпоновкиданных ' +\n  'типгруппировкикомпоновкиданных типгруппыэлементовотборакомпоновкиданных типдополненияпериодакомпоновкиданных ' +\n  'типзаголовкаполейкомпоновкиданных типмакетагруппировкикомпоновкиданных типмакетаобластикомпоновкиданных типостаткакомпоновкиданных ' +\n  'типпериодакомпоновкиданных типразмещениятекстакомпоновкиданных типсвязинаборовданныхкомпоновкиданных типэлементарезультатакомпоновкиданных ' +\n  'расположениелегендыдиаграммыкомпоновкиданных типпримененияотборакомпоновкиданных режимотображенияэлементанастройкикомпоновкиданных ' +\n  'режимотображениянастроеккомпоновкиданных состояниеэлементанастройкикомпоновкиданных способвосстановлениянастроеккомпоновкиданных ' +\n  'режимкомпоновкирезультата использованиепараметракомпоновкиданных автопозицияресурсовкомпоновкиданных '+\n  'вариантиспользованиягруппировкикомпоновкиданных расположениересурсоввдиаграммекомпоновкиданных фиксациякомпоновкиданных ' +\n  'использованиеусловногооформлениякомпоновкиданных ';\n\n  // v8 системные перечисления - почта ==> class\n  var v8_system_enums_email =\n  'важностьинтернетпочтовогосообщения обработкатекстаинтернетпочтовогосообщения способкодированияинтернетпочтовоговложения ' +\n  'способкодированиянеasciiсимволовинтернетпочтовогосообщения типтекстапочтовогосообщения протоколинтернетпочты ' +\n  'статусразборапочтовогосообщения ';\n\n  // v8 системные перечисления - журнал регистрации ==> class\n  var v8_system_enums_logbook =\n  'режимтранзакциизаписижурналарегистрации статустранзакциизаписижурналарегистрации уровеньжурналарегистрации ';\n\n  // v8 системные перечисления - криптография ==> class\n  var v8_system_enums_cryptography =\n  'расположениехранилищасертификатовкриптографии режимвключениясертификатовкриптографии режимпроверкисертификатакриптографии ' +\n  'типхранилищасертификатовкриптографии ';\n\n  // v8 системные перечисления - ZIP ==> class\n  var v8_system_enums_zip =\n  'кодировкаименфайловвzipфайле методсжатияzip методшифрованияzip режимвосстановленияпутейфайловzip режимобработкиподкаталоговzip ' +\n  'режимсохраненияпутейzip уровеньсжатияzip ';\n\n  // v8 системные перечисления -\n  // Блокировка данных, Фоновые задания, Автоматизированное тестирование,\n  // Доставляемые уведомления, Встроенные покупки, Интернет, Работа с двоичными данными ==> class\n  var v8_system_enums_other =\n  'звуковоеоповещение направлениепереходакстроке позициявпотоке порядокбайтов режимблокировкиданных режимуправленияблокировкойданных ' +\n  'сервисвстроенныхпокупок состояниефоновогозадания типподписчикадоставляемыхуведомлений уровеньиспользованиязащищенногосоединенияftp ';\n\n  // v8 системные перечисления - схема запроса ==> class\n  var v8_system_enums_request_schema =\n  'направлениепорядкасхемызапроса типдополненияпериодамисхемызапроса типконтрольнойточкисхемызапроса типобъединениясхемызапроса ' +\n  'типпараметрадоступнойтаблицысхемызапроса типсоединениясхемызапроса ';\n\n  // v8 системные перечисления - свойства объектов метаданных ==> class\n  var v8_system_enums_properties_of_metadata_objects =\n  'httpметод автоиспользованиеобщегореквизита автопрефиксномеразадачи вариантвстроенногоязыка видиерархии видрегистранакопления ' +\n  'видтаблицывнешнегоисточникаданных записьдвиженийприпроведении заполнениепоследовательностей индексирование ' +\n  'использованиебазыпланавидоврасчета использованиебыстроговыбора использованиеобщегореквизита использованиеподчинения ' +\n  'использованиеполнотекстовогопоиска использованиеразделяемыхданныхобщегореквизита использованиереквизита ' +\n  'назначениеиспользованияприложения назначениерасширенияконфигурации направлениепередачи обновлениепредопределенныхданных ' +\n  'оперативноепроведение основноепредставлениевидарасчета основноепредставлениевидахарактеристики основноепредставлениезадачи ' +\n  'основноепредставлениепланаобмена основноепредставлениесправочника основноепредставлениесчета перемещениеграницыприпроведении ' +\n  'периодичностьномерабизнеспроцесса периодичностьномерадокумента периодичностьрегистрарасчета периодичностьрегистрасведений ' +\n  'повторноеиспользованиевозвращаемыхзначений полнотекстовыйпоискпривводепостроке принадлежностьобъекта проведение ' +\n  'разделениеаутентификацииобщегореквизита разделениеданныхобщегореквизита разделениерасширенийконфигурацииобщегореквизита '+\n  'режимавтонумерацииобъектов режимзаписирегистра режимиспользованиямодальности ' +\n  'режимиспользованиясинхронныхвызововрасширенийплатформыивнешнихкомпонент режимповторногоиспользованиясеансов ' +\n  'режимполученияданныхвыборапривводепостроке режимсовместимости режимсовместимостиинтерфейса ' +\n  'режимуправленияблокировкойданныхпоумолчанию сериикодовпланавидовхарактеристик сериикодовпланасчетов ' +\n  'сериикодовсправочника созданиепривводе способвыбора способпоискастрокипривводепостроке способредактирования ' +\n  'типданныхтаблицывнешнегоисточникаданных типкодапланавидоврасчета типкодасправочника типмакета типномерабизнеспроцесса ' +\n  'типномерадокумента типномеразадачи типформы удалениедвижений ';\n\n  // v8 системные перечисления - разные ==> class\n  var v8_system_enums_differents =\n  'важностьпроблемыприменениярасширенияконфигурации вариантинтерфейсаклиентскогоприложения вариантмасштабаформклиентскогоприложения ' +\n  'вариантосновногошрифтаклиентскогоприложения вариантстандартногопериода вариантстандартнойдатыначала видграницы видкартинки ' +\n  'видотображенияполнотекстовогопоиска видрамки видсравнения видцвета видчисловогозначения видшрифта допустимаядлина допустимыйзнак ' +\n  'использованиеbyteordermark использованиеметаданныхполнотекстовогопоиска источникрасширенийконфигурации клавиша кодвозвратадиалога ' +\n  'кодировкаxbase кодировкатекста направлениепоиска направлениесортировки обновлениепредопределенныхданных обновлениеприизмененииданных ' +\n  'отображениепанелиразделов проверказаполнения режимдиалогавопрос режимзапускаклиентскогоприложения режимокругления режимоткрытияформприложения ' +\n  'режимполнотекстовогопоиска скоростьклиентскогосоединения состояниевнешнегоисточникаданных состояниеобновленияконфигурациибазыданных ' +\n  'способвыборасертификатаwindows способкодированиястроки статуссообщения типвнешнейкомпоненты типплатформы типповеденияклавишиenter ' +\n  'типэлементаинформацииовыполненииобновленияконфигурациибазыданных уровеньизоляциитранзакций хешфункция частидаты';\n\n  // class: встроенные наборы значений, системные перечисления (содержат дочерние значения, обращения к которым через разыменование)\n  var CLASS =\n  v8_system_sets_of_values +\n  v8_system_enums_interface +\n  v8_system_enums_objects_properties +\n  v8_system_enums_exchange_plans +\n  v8_system_enums_tabular_document +\n  v8_system_enums_sheduler +\n  v8_system_enums_formatted_document +\n  v8_system_enums_query +\n  v8_system_enums_report_builder +\n  v8_system_enums_files +\n  v8_system_enums_query_builder +\n  v8_system_enums_data_analysis +\n  v8_system_enums_xml_json_xs_dom_xdto_ws +\n  v8_system_enums_data_composition_system +\n  v8_system_enums_email +\n  v8_system_enums_logbook +\n  v8_system_enums_cryptography +\n  v8_system_enums_zip +\n  v8_system_enums_other +\n  v8_system_enums_request_schema +\n  v8_system_enums_properties_of_metadata_objects +\n  v8_system_enums_differents;\n\n  // v8 общие объекты (у объектов есть конструктор, экземпляры создаются методом НОВЫЙ) ==> type\n  var v8_shared_object =\n  'comобъект ftpсоединение httpзапрос httpсервисответ httpсоединение wsопределения wsпрокси xbase анализданных аннотацияxs ' +\n  'блокировкаданных буфердвоичныхданных включениеxs выражениекомпоновкиданных генераторслучайныхчисел географическаясхема ' +\n  'географическиекоординаты графическаясхема группамоделиxs данныерасшифровкикомпоновкиданных двоичныеданные дендрограмма ' +\n  'диаграмма диаграммаганта диалогвыборафайла диалогвыборацвета диалогвыборашрифта диалограсписаниярегламентногозадания ' +\n  'диалогредактированиястандартногопериода диапазон документdom документhtml документацияxs доставляемоеуведомление ' +\n  'записьdom записьfastinfoset записьhtml записьjson записьxml записьzipфайла записьданных записьтекста записьузловdom ' +\n  'запрос защищенноесоединениеopenssl значенияполейрасшифровкикомпоновкиданных извлечениетекста импортxs интернетпочта ' +\n  'интернетпочтовоесообщение интернетпочтовыйпрофиль интернетпрокси интернетсоединение информациядляприложенияxs ' +\n  'использованиеатрибутаxs использованиесобытияжурналарегистрации источникдоступныхнастроеккомпоновкиданных ' +\n  'итераторузловdom картинка квалификаторыдаты квалификаторыдвоичныхданных квалификаторыстроки квалификаторычисла ' +\n  'компоновщикмакетакомпоновкиданных компоновщикнастроеккомпоновкиданных конструктормакетаоформлениякомпоновкиданных ' +\n  'конструкторнастроеккомпоновкиданных конструкторформатнойстроки линия макеткомпоновкиданных макетобластикомпоновкиданных ' +\n  'макетоформлениякомпоновкиданных маскаxs менеджеркриптографии наборсхемxml настройкикомпоновкиданных настройкисериализацииjson ' +\n  'обработкакартинок обработкарасшифровкикомпоновкиданных обходдереваdom объявлениеатрибутаxs объявлениенотацииxs ' +\n  'объявлениеэлементаxs описаниеиспользованиясобытиядоступжурналарегистрации ' +\n  'описаниеиспользованиясобытияотказвдоступежурналарегистрации описаниеобработкирасшифровкикомпоновкиданных ' +\n  'описаниепередаваемогофайла описаниетипов определениегруппыатрибутовxs определениегруппымоделиxs ' +\n  'определениеограниченияидентичностиxs определениепростоготипаxs определениесоставноготипаxs определениетипадокументаdom ' +\n  'определенияxpathxs отборкомпоновкиданных пакетотображаемыхдокументов параметрвыбора параметркомпоновкиданных ' +\n  'параметрызаписиjson параметрызаписиxml параметрычтенияxml переопределениеxs планировщик полеанализаданных ' +\n  'полекомпоновкиданных построительdom построительзапроса построительотчета построительотчетаанализаданных ' +\n  'построительсхемxml поток потоквпамяти почта почтовоесообщение преобразованиеxsl преобразованиекканоническомуxml ' +\n  'процессорвыводарезультатакомпоновкиданныхвколлекциюзначений процессорвыводарезультатакомпоновкиданныхвтабличныйдокумент ' +\n  'процессоркомпоновкиданных разыменовательпространствименdom рамка расписаниерегламентногозадания расширенноеимяxml ' +\n  'результатчтенияданных своднаядиаграмма связьпараметравыбора связьпотипу связьпотипукомпоновкиданных сериализаторxdto ' +\n  'сертификатклиентаwindows сертификатклиентафайл сертификаткриптографии сертификатыудостоверяющихцентровwindows ' +\n  'сертификатыудостоверяющихцентровфайл сжатиеданных системнаяинформация сообщениепользователю сочетаниеклавиш ' +\n  'сравнениезначений стандартнаядатаначала стандартныйпериод схемаxml схемакомпоновкиданных табличныйдокумент ' +\n  'текстовыйдокумент тестируемоеприложение типданныхxml уникальныйидентификатор фабрикаxdto файл файловыйпоток ' +\n  'фасетдлиныxs фасетколичестваразрядовдробнойчастиxs фасетмаксимальноговключающегозначенияxs ' +\n  'фасетмаксимальногоисключающегозначенияxs фасетмаксимальнойдлиныxs фасетминимальноговключающегозначенияxs ' +\n  'фасетминимальногоисключающегозначенияxs фасетминимальнойдлиныxs фасетобразцаxs фасетобщегоколичестваразрядовxs ' +\n  'фасетперечисленияxs фасетпробельныхсимволовxs фильтрузловdom форматированнаястрока форматированныйдокумент ' +\n  'фрагментxs хешированиеданных хранилищезначения цвет чтениеfastinfoset чтениеhtml чтениеjson чтениеxml чтениеzipфайла ' +\n  'чтениеданных чтениетекста чтениеузловdom шрифт элементрезультатакомпоновкиданных ';\n\n  // v8 универсальные коллекции значений ==> type\n  var v8_universal_collection =\n  'comsafearray деревозначений массив соответствие списокзначений структура таблицазначений фиксированнаяструктура ' +\n  'фиксированноесоответствие фиксированныймассив ';\n\n  // type : встроенные типы\n  var TYPE =\n  v8_shared_object +\n  v8_universal_collection;\n\n  // literal : примитивные типы\n  var LITERAL = 'null истина ложь неопределено';\n\n  // number : числа\n  var NUMBERS = hljs.inherit(hljs.NUMBER_MODE);\n\n  // string : строки\n  var STRINGS = {\n    className: 'string',\n    begin: '\"|\\\\|', end: '\"|$',\n    contains: [{begin: '\"\"'}]\n  };\n\n  // number : даты\n  var DATE = {\n    begin: \"'\", end: \"'\", excludeBegin: true, excludeEnd: true,\n    contains: [\n      {\n        className: 'number',\n        begin: '\\\\d{4}([\\\\.\\\\\\\\/:-]?\\\\d{2}){0,5}'\n      }\n    ]\n  };\n\n  // comment : комментарии\n  var COMMENTS = hljs.inherit(hljs.C_LINE_COMMENT_MODE);\n\n  // meta : инструкции препроцессора, директивы компиляции\n  var META = {\n    className: 'meta',\n\n    begin: '#|&', end: '$',\n    keywords: {\n      $pattern: UNDERSCORE_IDENT_RE,\n      'meta-keyword': KEYWORD + METAKEYWORD\n    },\n    contains: [\n      COMMENTS\n    ]\n  };\n\n  // symbol : метка goto\n  var SYMBOL = {\n    className: 'symbol',\n    begin: '~', end: ';|:', excludeEnd: true\n  };\n\n  // function : объявление процедур и функций\n  var FUNCTION = {\n    className: 'function',\n    variants: [\n      {begin: 'процедура|функция', end: '\\\\)', keywords: 'процедура функция'},\n      {begin: 'конецпроцедуры|конецфункции', keywords: 'конецпроцедуры конецфункции'}\n    ],\n    contains: [\n      {\n        begin: '\\\\(', end: '\\\\)', endsParent : true,\n        contains: [\n          {\n            className: 'params',\n            begin: UNDERSCORE_IDENT_RE, end: ',', excludeEnd: true, endsWithParent: true,\n            keywords: {\n              $pattern: UNDERSCORE_IDENT_RE,\n              keyword: 'знач',\n              literal: LITERAL\n            },\n            contains: [\n              NUMBERS,\n              STRINGS,\n              DATE\n            ]\n          },\n          COMMENTS\n        ]\n      },\n      hljs.inherit(hljs.TITLE_MODE, {begin: UNDERSCORE_IDENT_RE})\n    ]\n  };\n\n  return {\n    name: '1C:Enterprise',\n    case_insensitive: true,\n    keywords: {\n      $pattern: UNDERSCORE_IDENT_RE,\n      keyword: KEYWORD,\n      built_in: BUILTIN,\n      class: CLASS,\n      type: TYPE,\n      literal: LITERAL\n    },\n    contains: [\n      META,\n      FUNCTION,\n      COMMENTS,\n      SYMBOL,\n      NUMBERS,\n      STRINGS,\n      DATE\n    ]\n  };\n}\n\nmodule.exports = _1c;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,IAAI,MAAM;AAGjB,UAAI,sBAAsB;AAG1B,UAAI,cACJ;AAGA,UAAI,cACJ;AAIA,UAAI,UAAU,cAAc;AAG5B,UAAI,mBACJ;AAGA,UAAI,mBACJ;AAKA,UAAI,cAAc,mBAAmB;AAGrC,UAAI,sBACJ;AAGA,UAAI,4BACJ;AAaA,UAAI,4BACJ;AA0EA,UAAI,6BACJ;AAaA,UAAI,UACJ,sBACA,4BAA4B,4BAC5B;AAGA,UAAI,2BACJ;AAGA,UAAI,4BACJ;AAgDA,UAAI,qCACJ;AAKA,UAAI,iCACJ;AAGA,UAAI,mCACJ;AASA,UAAI,2BACJ;AAGA,UAAI,qCACJ;AAGA,UAAI,wBACJ;AAGA,UAAI,iCACJ;AAGA,UAAI,wBACJ;AAGA,UAAI,gCACJ;AAGA,UAAI,gCACJ;AAQA,UAAI,0CACJ;AASA,UAAI,0CACJ;AAcA,UAAI,wBACJ;AAKA,UAAI,0BACJ;AAGA,UAAI,+BACJ;AAIA,UAAI,sBACJ;AAMA,UAAI,wBACJ;AAIA,UAAI,iCACJ;AAIA,UAAI,iDACJ;AAmBA,UAAI,6BACJ;AAWA,UAAI,QACJ,2BACA,4BACA,qCACA,iCACA,mCACA,2BACA,qCACA,wBACA,iCACA,wBACA,gCACA,gCACA,0CACA,0CACA,wBACA,0BACA,+BACA,sBACA,wBACA,iCACA,iDACA;AAGA,UAAI,mBACJ;AAqCA,UAAI,0BACJ;AAIA,UAAI,OACJ,mBACA;AAGA,UAAI,UAAU;AAGd,UAAI,UAAU,KAAK,QAAQ,KAAK,WAAW;AAG3C,UAAI,UAAU;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QAAS,KAAK;AAAA,QACrB,UAAU,CAAC,EAAC,OAAO,KAAI,CAAC;AAAA,MAC1B;AAGA,UAAI,OAAO;AAAA,QACT,OAAO;AAAA,QAAK,KAAK;AAAA,QAAK,cAAc;AAAA,QAAM,YAAY;AAAA,QACtD,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAGA,UAAI,WAAW,KAAK,QAAQ,KAAK,mBAAmB;AAGpD,UAAI,OAAO;AAAA,QACT,WAAW;AAAA,QAEX,OAAO;AAAA,QAAO,KAAK;AAAA,QACnB,UAAU;AAAA,UACR,UAAU;AAAA,UACV,gBAAgB,UAAU;AAAA,QAC5B;AAAA,QACA,UAAU;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAGA,UAAI,SAAS;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QAAK,KAAK;AAAA,QAAO,YAAY;AAAA,MACtC;AAGA,UAAI,WAAW;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAC,OAAO,qBAAqB,KAAK,OAAO,UAAU,oBAAmB;AAAA,UACtE,EAAC,OAAO,+BAA+B,UAAU,8BAA6B;AAAA,QAChF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YAAO,KAAK;AAAA,YAAO,YAAa;AAAA,YACvC,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBAAqB,KAAK;AAAA,gBAAK,YAAY;AAAA,gBAAM,gBAAgB;AAAA,gBACxE,UAAU;AAAA,kBACR,UAAU;AAAA,kBACV,SAAS;AAAA,kBACT,SAAS;AAAA,gBACX;AAAA,gBACA,UAAU;AAAA,kBACR;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK,QAAQ,KAAK,YAAY,EAAC,OAAO,oBAAmB,CAAC;AAAA,QAC5D;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,UACV,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}