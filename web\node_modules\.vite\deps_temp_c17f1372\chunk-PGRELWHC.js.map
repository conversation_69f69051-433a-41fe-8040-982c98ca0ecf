{"version": 3, "sources": ["../../refractor/lang/promql.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = promql\npromql.displayName = 'promql'\npromql.aliases = []\nfunction promql(Prism) {\n  // Thanks to: https://github.com/prometheus-community/monaco-promql/blob/master/src/promql/promql.ts\n  // As well as: https://kausal.co/blog/slate-prism-add-new-syntax-promql/\n  ;(function (Prism) {\n    // PromQL Aggregation Operators\n    // (https://prometheus.io/docs/prometheus/latest/querying/operators/#aggregation-operators)\n    var aggregations = [\n      'sum',\n      'min',\n      'max',\n      'avg',\n      'group',\n      'stddev',\n      'stdvar',\n      'count',\n      'count_values',\n      'bottomk',\n      'topk',\n      'quantile'\n    ] // PromQL vector matching + the by and without clauses\n    // (https://prometheus.io/docs/prometheus/latest/querying/operators/#vector-matching)\n    var vectorMatching = [\n      'on',\n      'ignoring',\n      'group_right',\n      'group_left',\n      'by',\n      'without'\n    ] // PromQL offset modifier\n    // (https://prometheus.io/docs/prometheus/latest/querying/basics/#offset-modifier)\n    var offsetModifier = ['offset']\n    var keywords = aggregations.concat(vectorMatching, offsetModifier)\n    Prism.languages.promql = {\n      comment: {\n        pattern: /(^[ \\t]*)#.*/m,\n        lookbehind: true\n      },\n      'vector-match': {\n        // Match the comma-separated label lists inside vector matching:\n        pattern: new RegExp(\n          '((?:' + vectorMatching.join('|') + ')\\\\s*)\\\\([^)]*\\\\)'\n        ),\n        lookbehind: true,\n        inside: {\n          'label-key': {\n            pattern: /\\b[^,]+\\b/,\n            alias: 'attr-name'\n          },\n          punctuation: /[(),]/\n        }\n      },\n      'context-labels': {\n        pattern: /\\{[^{}]*\\}/,\n        inside: {\n          'label-key': {\n            pattern: /\\b[a-z_]\\w*(?=\\s*(?:=|![=~]))/,\n            alias: 'attr-name'\n          },\n          'label-value': {\n            pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1/,\n            greedy: true,\n            alias: 'attr-value'\n          },\n          punctuation: /\\{|\\}|=~?|![=~]|,/\n        }\n      },\n      'context-range': [\n        {\n          pattern: /\\[[\\w\\s:]+\\]/,\n          // [1m]\n          inside: {\n            punctuation: /\\[|\\]|:/,\n            'range-duration': {\n              pattern: /\\b(?:\\d+(?:[smhdwy]|ms))+\\b/i,\n              alias: 'number'\n            }\n          }\n        },\n        {\n          pattern: /(\\boffset\\s+)\\w+/,\n          // offset 1m\n          lookbehind: true,\n          inside: {\n            'range-duration': {\n              pattern: /\\b(?:\\d+(?:[smhdwy]|ms))+\\b/i,\n              alias: 'number'\n            }\n          }\n        }\n      ],\n      keyword: new RegExp('\\\\b(?:' + keywords.join('|') + ')\\\\b', 'i'),\n      function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n      number:\n        /[-+]?(?:(?:\\b\\d+(?:\\.\\d+)?|\\B\\.\\d+)(?:e[-+]?\\d+)?\\b|\\b(?:0x[0-9a-f]+|nan|inf)\\b)/i,\n      operator: /[\\^*/%+-]|==|!=|<=|<|>=|>|\\b(?:and|or|unless)\\b/i,\n      punctuation: /[{};()`,.[\\]]/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AAGrB;AAAC,OAAC,SAAUA,QAAO;AAGjB,YAAI,eAAe;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,YAAI,iBAAiB;AAAA,UACnB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,YAAI,iBAAiB,CAAC,QAAQ;AAC9B,YAAI,WAAW,aAAa,OAAO,gBAAgB,cAAc;AACjE,QAAAA,OAAM,UAAU,SAAS;AAAA,UACvB,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,gBAAgB;AAAA;AAAA,YAEd,SAAS,IAAI;AAAA,cACX,SAAS,eAAe,KAAK,GAAG,IAAI;AAAA,YACtC;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,kBAAkB;AAAA,YAChB,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,eAAe;AAAA,gBACb,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,OAAO;AAAA,cACT;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,iBAAiB;AAAA,YACf;AAAA,cACE,SAAS;AAAA;AAAA,cAET,QAAQ;AAAA,gBACN,aAAa;AAAA,gBACb,kBAAkB;AAAA,kBAChB,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA;AAAA,cAET,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,kBAAkB;AAAA,kBAChB,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,SAAS,IAAI,OAAO,WAAW,SAAS,KAAK,GAAG,IAAI,QAAQ,GAAG;AAAA,UAC/D,UAAU;AAAA,UACV,QACE;AAAA,UACF,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}