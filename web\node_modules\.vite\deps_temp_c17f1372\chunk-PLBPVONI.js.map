{"version": 3, "sources": ["../../highlight.js/lib/languages/java.js"], "sourcesContent": ["// https://docs.oracle.com/javase/specs/jls/se15/html/jls-3.html#jls-3.10\nvar decimalDigits = '[0-9](_*[0-9])*';\nvar frac = `\\\\.(${decimalDigits})`;\nvar hexDigits = '[0-9a-fA-F](_*[0-9a-fA-F])*';\nvar NUMERIC = {\n  className: 'number',\n  variants: [\n    // DecimalFloatingPointLiteral\n    // including ExponentPart\n    { begin: `(\\\\b(${decimalDigits})((${frac})|\\\\.)?|(${frac}))` +\n      `[eE][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n    // excluding ExponentPart\n    { begin: `\\\\b(${decimalDigits})((${frac})[fFdD]?\\\\b|\\\\.([fFdD]\\\\b)?)` },\n    { begin: `(${frac})[fFdD]?\\\\b` },\n    { begin: `\\\\b(${decimalDigits})[fFdD]\\\\b` },\n\n    // HexadecimalFloatingPointLiteral\n    { begin: `\\\\b0[xX]((${hexDigits})\\\\.?|(${hexDigits})?\\\\.(${hexDigits}))` +\n      `[pP][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n\n    // DecimalIntegerLiteral\n    { begin: '\\\\b(0|[1-9](_*[0-9])*)[lL]?\\\\b' },\n\n    // HexIntegerLiteral\n    { begin: `\\\\b0[xX](${hexDigits})[lL]?\\\\b` },\n\n    // OctalIntegerLiteral\n    { begin: '\\\\b0(_*[0-7])*[lL]?\\\\b' },\n\n    // BinaryIntegerLiteral\n    { begin: '\\\\b0[bB][01](_*[01])*[lL]?\\\\b' },\n  ],\n  relevance: 0\n};\n\n/*\nLanguage: Java\nAuthor: Vsevolod Solovyov <<EMAIL>>\nCategory: common, enterprise\nWebsite: https://www.java.com/\n*/\n\nfunction java(hljs) {\n  var JAVA_IDENT_RE = '[\\u00C0-\\u02B8a-zA-Z_$][\\u00C0-\\u02B8a-zA-Z_$0-9]*';\n  var GENERIC_IDENT_RE = JAVA_IDENT_RE + '(<' + JAVA_IDENT_RE + '(\\\\s*,\\\\s*' + JAVA_IDENT_RE + ')*>)?';\n  var KEYWORDS = 'false synchronized int abstract float private char boolean var static null if const ' +\n    'for true while long strictfp finally protected import native final void ' +\n    'enum else break transient catch instanceof byte super volatile case assert short ' +\n    'package default double public try this switch continue throws protected public private ' +\n    'module requires exports do';\n\n  var ANNOTATION = {\n    className: 'meta',\n    begin: '@' + JAVA_IDENT_RE,\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\"self\"] // allow nested () inside our annotation\n      },\n    ]\n  };\n  const NUMBER = NUMERIC;\n\n  return {\n    name: 'Java',\n    aliases: ['jsp'],\n    keywords: KEYWORDS,\n    illegal: /<\\/|#/,\n    contains: [\n      hljs.COMMENT(\n        '/\\\\*\\\\*',\n        '\\\\*/',\n        {\n          relevance: 0,\n          contains: [\n            {\n              // eat up @'s in emails to prevent them to be recognized as doctags\n              begin: /\\w+@/, relevance: 0\n            },\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      // relevance boost\n      {\n        begin: /import java\\.[a-z]+\\./,\n        keywords: \"import\",\n        relevance: 2\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'class interface enum', end: /[{;=]/, excludeEnd: true,\n        // TODO: can this be removed somehow?\n        // an extra boost because Java is more popular than other languages with\n        // this same syntax feature (this is just to preserve our tests passing\n        // for now)\n        relevance: 1,\n        keywords: 'class interface enum',\n        illegal: /[:\"\\[\\]]/,\n        contains: [\n          { beginKeywords: 'extends implements' },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new throw return else',\n        relevance: 0\n      },\n      {\n        className: 'class',\n        begin: 'record\\\\s+' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n        returnBegin: true,\n        excludeEnd: true,\n        end: /[{;=]/,\n        keywords: KEYWORDS,\n        contains: [\n          { beginKeywords: \"record\" },\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n            returnBegin: true,\n            relevance: 0,\n            contains: [hljs.UNDERSCORE_TITLE_MODE]\n          },\n          {\n            className: 'params',\n            begin: /\\(/, end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        className: 'function',\n        begin: '(' + GENERIC_IDENT_RE + '\\\\s+)+' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(', returnBegin: true, end: /[{;=]/,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(', returnBegin: true,\n            relevance: 0,\n            contains: [hljs.UNDERSCORE_TITLE_MODE]\n          },\n          {\n            className: 'params',\n            begin: /\\(/, end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              ANNOTATION,\n              hljs.APOS_STRING_MODE,\n              hljs.QUOTE_STRING_MODE,\n              NUMBER,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      NUMBER,\n      ANNOTATION\n    ]\n  };\n}\n\nmodule.exports = java;\n"], "mappings": ";;;;;AAAA;AAAA;AACA,QAAI,gBAAgB;AACpB,QAAI,OAAO,OAAO,aAAa;AAC/B,QAAI,YAAY;AAChB,QAAI,UAAU;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA;AAAA;AAAA,QAGR,EAAE,OAAO,QAAQ,aAAa,MAAM,IAAI,YAAY,IAAI,eACzC,aAAa,cAAc;AAAA;AAAA,QAE1C,EAAE,OAAO,OAAO,aAAa,MAAM,IAAI,+BAA+B;AAAA,QACtE,EAAE,OAAO,IAAI,IAAI,cAAc;AAAA,QAC/B,EAAE,OAAO,OAAO,aAAa,aAAa;AAAA;AAAA,QAG1C,EAAE,OAAO,aAAa,SAAS,UAAU,SAAS,SAAS,SAAS,eACrD,aAAa,cAAc;AAAA;AAAA,QAG1C,EAAE,OAAO,iCAAiC;AAAA;AAAA,QAG1C,EAAE,OAAO,YAAY,SAAS,YAAY;AAAA;AAAA,QAG1C,EAAE,OAAO,yBAAyB;AAAA;AAAA,QAGlC,EAAE,OAAO,gCAAgC;AAAA,MAC3C;AAAA,MACA,WAAW;AAAA,IACb;AASA,aAAS,KAAK,MAAM;AAClB,UAAI,gBAAgB;AACpB,UAAI,mBAAmB,gBAAgB,OAAO,gBAAgB,eAAe,gBAAgB;AAC7F,UAAI,WAAW;AAMf,UAAI,aAAa;AAAA,QACf,WAAW;AAAA,QACX,OAAO,MAAM;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC,MAAM;AAAA;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS;AAEf,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,KAAK;AAAA,QACf,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA,gBACR;AAAA;AAAA,kBAEE,OAAO;AAAA,kBAAQ,WAAW;AAAA,gBAC5B;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,YACV,WAAW;AAAA,UACb;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YAAwB,KAAK;AAAA,YAAS,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,YAKjE,WAAW;AAAA,YACX,UAAU;AAAA,YACV,SAAS;AAAA,YACT,UAAU;AAAA,cACR,EAAE,eAAe,qBAAqB;AAAA,cACtC,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,eAAe;AAAA,YACf,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,eAAe,KAAK,sBAAsB;AAAA,YACjD,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR,EAAE,eAAe,SAAS;AAAA,cAC1B;AAAA,gBACE,OAAO,KAAK,sBAAsB;AAAA,gBAClC,aAAa;AAAA,gBACb,WAAW;AAAA,gBACX,UAAU,CAAC,KAAK,qBAAqB;AAAA,cACvC;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBAAM,KAAK;AAAA,gBAClB,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,MAAM,mBAAmB,WAAW,KAAK,sBAAsB;AAAA,YAAW,aAAa;AAAA,YAAM,KAAK;AAAA,YACzG,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,KAAK,sBAAsB;AAAA,gBAAW,aAAa;AAAA,gBAC1D,WAAW;AAAA,gBACX,UAAU,CAAC,KAAK,qBAAqB;AAAA,cACvC;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBAAM,KAAK;AAAA,gBAClB,UAAU;AAAA,gBACV,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,kBACA,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL;AAAA,kBACA,KAAK;AAAA,gBACP;AAAA,cACF;AAAA,cACA,KAAK;AAAA,cACL,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}