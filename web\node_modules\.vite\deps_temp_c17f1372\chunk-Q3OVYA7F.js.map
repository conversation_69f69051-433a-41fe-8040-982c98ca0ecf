{"version": 3, "sources": ["../../highlight.js/lib/languages/avrasm.js"], "sourcesContent": ["/*\nLanguage: AVR Assembly\nAuthor: <PERSON> <<EMAIL>>\nCategory: assembler\nWebsite: https://www.microchip.com/webdoc/avrassembler/avrassembler.wb_instruction_list.html\n*/\n\n/** @type LanguageFn */\nfunction avrasm(hljs) {\n  return {\n    name: 'AVR Assembly',\n    case_insensitive: true,\n    keywords: {\n      $pattern: '\\\\.?' + hljs.IDENT_RE,\n      keyword:\n        /* mnemonic */\n        'adc add adiw and andi asr bclr bld brbc brbs brcc brcs break breq brge brhc brhs ' +\n        'brid brie brlo brlt brmi brne brpl brsh brtc brts brvc brvs bset bst call cbi cbr ' +\n        'clc clh cli cln clr cls clt clv clz com cp cpc cpi cpse dec eicall eijmp elpm eor ' +\n        'fmul fmuls fmulsu icall ijmp in inc jmp ld ldd ldi lds lpm lsl lsr mov movw mul ' +\n        'muls mulsu neg nop or ori out pop push rcall ret reti rjmp rol ror sbc sbr sbrc sbrs ' +\n        'sec seh sbi sbci sbic sbis sbiw sei sen ser ses set sev sez sleep spm st std sts sub ' +\n        'subi swap tst wdr',\n      built_in:\n        /* general purpose registers */\n        'r0 r1 r2 r3 r4 r5 r6 r7 r8 r9 r10 r11 r12 r13 r14 r15 r16 r17 r18 r19 r20 r21 r22 ' +\n        'r23 r24 r25 r26 r27 r28 r29 r30 r31 x|0 xh xl y|0 yh yl z|0 zh zl ' +\n        /* IO Registers (ATMega128) */\n        'ucsr1c udr1 ucsr1a ucsr1b ubrr1l ubrr1h ucsr0c ubrr0h tccr3c tccr3a tccr3b tcnt3h ' +\n        'tcnt3l ocr3ah ocr3al ocr3bh ocr3bl ocr3ch ocr3cl icr3h icr3l etimsk etifr tccr1c ' +\n        'ocr1ch ocr1cl twcr twdr twar twsr twbr osccal xmcra xmcrb eicra spmcsr spmcr portg ' +\n        'ddrg ping portf ddrf sreg sph spl xdiv rampz eicrb eimsk gimsk gicr eifr gifr timsk ' +\n        'tifr mcucr mcucsr tccr0 tcnt0 ocr0 assr tccr1a tccr1b tcnt1h tcnt1l ocr1ah ocr1al ' +\n        'ocr1bh ocr1bl icr1h icr1l tccr2 tcnt2 ocr2 ocdr wdtcr sfior eearh eearl eedr eecr ' +\n        'porta ddra pina portb ddrb pinb portc ddrc pinc portd ddrd pind spdr spsr spcr udr0 ' +\n        'ucsr0a ucsr0b ubrr0l acsr admux adcsr adch adcl porte ddre pine pinf',\n      meta:\n        '.byte .cseg .db .def .device .dseg .dw .endmacro .equ .eseg .exit .include .list ' +\n        '.listmac .macro .nolist .org .set'\n    },\n    contains: [\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.COMMENT(\n        ';',\n        '$',\n        {\n          relevance: 0\n        }\n      ),\n      hljs.C_NUMBER_MODE, // 0x..., decimal, float\n      hljs.BINARY_NUMBER_MODE, // 0b...\n      {\n        className: 'number',\n        begin: '\\\\b(\\\\$[a-zA-Z0-9]+|0o[0-7]+)' // $..., 0o...\n      },\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        begin: '\\'',\n        end: '[^\\\\\\\\]\\'',\n        illegal: '[^\\\\\\\\][^\\']'\n      },\n      {\n        className: 'symbol',\n        begin: '^[A-Za-z0-9_.$]+:'\n      },\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$'\n      },\n      { // substitution within a macro\n        className: 'subst',\n        begin: '@[0-9]+'\n      }\n    ]\n  };\n}\n\nmodule.exports = avrasm;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,OAAO,MAAM;AACpB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,UAAU,SAAS,KAAK;AAAA,UACxB;AAAA;AAAA,YAEE;AAAA;AAAA,UAOF;AAAA;AAAA,YAEE;AAAA;AAAA,UAWF,MACE;AAAA,QAEJ;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,YACb;AAAA,UACF;AAAA,UACA,KAAK;AAAA;AAAA,UACL,KAAK;AAAA;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA;AAAA,UACT;AAAA,UACA,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}