{"version": 3, "sources": ["../../refractor/lang/objectivec.js"], "sourcesContent": ["'use strict'\nvar refractorC = require('./c.js')\nmodule.exports = objectivec\nobjectivec.displayName = 'objectivec'\nobjectivec.aliases = ['objc']\nfunction objectivec(Prism) {\n  Prism.register(refractorC)\n  Prism.languages.objectivec = Prism.languages.extend('c', {\n    string: {\n      pattern: /@?\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|in|inline|int|long|register|return|self|short|signed|sizeof|static|struct|super|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b|(?:@interface|@end|@implementation|@protocol|@class|@public|@protected|@private|@property|@try|@catch|@finally|@throw|@synthesize|@dynamic|@selector)\\b/,\n    operator: /-[->]?|\\+\\+?|!=?|<<?=?|>>?=?|==?|&&?|\\|\\|?|[~^%?*\\/@]/\n  })\n  delete Prism.languages.objectivec['class-name']\n  Prism.languages.objc = Prism.languages.objectivec\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,aAAa;AACjB,WAAO,UAAU;AACjB,eAAW,cAAc;AACzB,eAAW,UAAU,CAAC,MAAM;AAC5B,aAAS,WAAW,OAAO;AACzB,YAAM,SAAS,UAAU;AACzB,YAAM,UAAU,aAAa,MAAM,UAAU,OAAO,KAAK;AAAA,QACvD,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,MACZ,CAAC;AACD,aAAO,MAAM,UAAU,WAAW,YAAY;AAC9C,YAAM,UAAU,OAAO,MAAM,UAAU;AAAA,IACzC;AAAA;AAAA;", "names": []}