{"version": 3, "sources": ["../../highlight.js/lib/languages/latex.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: LaTeX\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.latex-project.org\nCategory: markup\n*/\n\n/** @type LanguageFn */\nfunction latex(hljs) {\n  const KNOWN_CONTROL_WORDS = either(...[\n      '(?:NeedsTeXFormat|RequirePackage|GetIdInfo)',\n      'Provides(?:Expl)?(?:Package|Class|File)',\n      '(?:DeclareOption|ProcessOptions)',\n      '(?:documentclass|usepackage|input|include)',\n      'makeat(?:letter|other)',\n      'ExplSyntax(?:On|Off)',\n      '(?:new|renew|provide)?command',\n      '(?:re)newenvironment',\n      '(?:New|Renew|Provide|Declare)(?:Expandable)?DocumentCommand',\n      '(?:New|Renew|Provide|Declare)DocumentEnvironment',\n      '(?:(?:e|g|x)?def|let)',\n      '(?:begin|end)',\n      '(?:part|chapter|(?:sub){0,2}section|(?:sub)?paragraph)',\n      'caption',\n      '(?:label|(?:eq|page|name)?ref|(?:paren|foot|super)?cite)',\n      '(?:alpha|beta|[Gg]amma|[Dd]elta|(?:var)?epsilon|zeta|eta|[Tt]heta|vartheta)',\n      '(?:iota|(?:var)?kappa|[Ll]ambda|mu|nu|[Xx]i|[Pp]i|varpi|(?:var)rho)',\n      '(?:[Ss]igma|varsigma|tau|[Uu]psilon|[Pp]hi|varphi|chi|[Pp]si|[Oo]mega)',\n      '(?:frac|sum|prod|lim|infty|times|sqrt|leq|geq|left|right|middle|[bB]igg?)',\n      '(?:[lr]angle|q?quad|[lcvdi]?dots|d?dot|hat|tilde|bar)'\n    ].map(word => word + '(?![a-zA-Z@:_])'));\n  const L3_REGEX = new RegExp([\n      // A function \\module_function_name:signature or \\__module_function_name:signature,\n      // where both module and function_name need at least two characters and\n      // function_name may contain single underscores.\n      '(?:__)?[a-zA-Z]{2,}_[a-zA-Z](?:_?[a-zA-Z])+:[a-zA-Z]*',\n      // A variable \\scope_module_and_name_type or \\scope__module_ane_name_type,\n      // where scope is one of l, g or c, type needs at least two characters\n      // and module_and_name may contain single underscores.\n      '[lgc]__?[a-zA-Z](?:_?[a-zA-Z])*_[a-zA-Z]{2,}',\n      // A quark \\q_the_name or \\q__the_name or\n      // scan mark \\s_the_name or \\s__vthe_name,\n      // where variable_name needs at least two characters and\n      // may contain single underscores.\n      '[qs]__?[a-zA-Z](?:_?[a-zA-Z])+',\n      // Other LaTeX3 macro names that are not covered by the three rules above.\n      'use(?:_i)?:[a-zA-Z]*',\n      '(?:else|fi|or):',\n      '(?:if|cs|exp):w',\n      '(?:hbox|vbox):n',\n      '::[a-zA-Z]_unbraced',\n      '::[a-zA-Z:]'\n    ].map(pattern => pattern + '(?![a-zA-Z:_])').join('|'));\n  const L2_VARIANTS = [\n    {begin: /[a-zA-Z@]+/}, // control word\n    {begin: /[^a-zA-Z@]?/} // control symbol\n  ];\n  const DOUBLE_CARET_VARIANTS = [\n    {begin: /\\^{6}[0-9a-f]{6}/},\n    {begin: /\\^{5}[0-9a-f]{5}/},\n    {begin: /\\^{4}[0-9a-f]{4}/},\n    {begin: /\\^{3}[0-9a-f]{3}/},\n    {begin: /\\^{2}[0-9a-f]{2}/},\n    {begin: /\\^{2}[\\u0000-\\u007f]/}\n  ];\n  const CONTROL_SEQUENCE = {\n    className: 'keyword',\n    begin: /\\\\/,\n    relevance: 0,\n    contains: [\n      {\n        endsParent: true,\n        begin: KNOWN_CONTROL_WORDS\n      },\n      {\n        endsParent: true,\n        begin: L3_REGEX\n      },\n      {\n        endsParent: true,\n        variants: DOUBLE_CARET_VARIANTS\n      },\n      {\n        endsParent: true,\n        relevance: 0,\n        variants: L2_VARIANTS\n      }\n    ]\n  };\n  const MACRO_PARAM = {\n    className: 'params',\n    relevance: 0,\n    begin: /#+\\d?/\n  };\n  const DOUBLE_CARET_CHAR = {\n    // relevance: 1\n    variants: DOUBLE_CARET_VARIANTS\n  };\n  const SPECIAL_CATCODE = {\n    className: 'built_in',\n    relevance: 0,\n    begin: /[$&^_]/\n  };\n  const MAGIC_COMMENT = {\n    className: 'meta',\n    begin: '% !TeX',\n    end: '$',\n    relevance: 10\n  };\n  const COMMENT = hljs.COMMENT(\n    '%',\n    '$',\n    {\n      relevance: 0\n    }\n  );\n  const EVERYTHING_BUT_VERBATIM = [\n    CONTROL_SEQUENCE,\n    MACRO_PARAM,\n    DOUBLE_CARET_CHAR,\n    SPECIAL_CATCODE,\n    MAGIC_COMMENT,\n    COMMENT\n  ];\n  const BRACE_GROUP_NO_VERBATIM = {\n    begin: /\\{/, end: /\\}/,\n    relevance: 0,\n    contains: ['self', ...EVERYTHING_BUT_VERBATIM]\n  };\n  const ARGUMENT_BRACES = hljs.inherit(\n    BRACE_GROUP_NO_VERBATIM,\n    {\n      relevance: 0,\n      endsParent: true,\n      contains: [BRACE_GROUP_NO_VERBATIM, ...EVERYTHING_BUT_VERBATIM]\n    }\n  );\n  const ARGUMENT_BRACKETS = {\n    begin: /\\[/,\n      end: /\\]/,\n    endsParent: true,\n    relevance: 0,\n    contains: [BRACE_GROUP_NO_VERBATIM, ...EVERYTHING_BUT_VERBATIM]\n  };\n  const SPACE_GOBBLER = {\n    begin: /\\s+/,\n    relevance: 0\n  };\n  const ARGUMENT_M = [ARGUMENT_BRACES];\n  const ARGUMENT_O = [ARGUMENT_BRACKETS];\n  const ARGUMENT_AND_THEN = function(arg, starts_mode) {\n    return {\n      contains: [SPACE_GOBBLER],\n      starts: {\n        relevance: 0,\n        contains: arg,\n        starts: starts_mode\n      }\n    };\n  };\n  const CSNAME = function(csname, starts_mode) {\n    return {\n        begin: '\\\\\\\\' + csname + '(?![a-zA-Z@:_])',\n        keywords: {$pattern: /\\\\[a-zA-Z]+/, keyword: '\\\\' + csname},\n        relevance: 0,\n        contains: [SPACE_GOBBLER],\n        starts: starts_mode\n      };\n  };\n  const BEGIN_ENV = function(envname, starts_mode) {\n    return hljs.inherit(\n      {\n        begin: '\\\\\\\\begin(?=[ \\t]*(\\\\r?\\\\n[ \\t]*)?\\\\{' + envname + '\\\\})',\n        keywords: {$pattern: /\\\\[a-zA-Z]+/, keyword: '\\\\begin'},\n        relevance: 0,\n      },\n      ARGUMENT_AND_THEN(ARGUMENT_M, starts_mode)\n    );\n  };\n  const VERBATIM_DELIMITED_EQUAL = (innerName = \"string\") => {\n    return hljs.END_SAME_AS_BEGIN({\n      className: innerName,\n      begin: /(.|\\r?\\n)/,\n      end: /(.|\\r?\\n)/,\n      excludeBegin: true,\n      excludeEnd: true,\n      endsParent: true\n    });\n  };\n  const VERBATIM_DELIMITED_ENV = function(envname) {\n    return {\n      className: 'string',\n      end: '(?=\\\\\\\\end\\\\{' + envname + '\\\\})'\n    };\n  };\n\n  const VERBATIM_DELIMITED_BRACES = (innerName = \"string\") => {\n    return {\n      relevance: 0,\n      begin: /\\{/,\n      starts: {\n        endsParent: true,\n        contains: [\n          {\n            className: innerName,\n            end: /(?=\\})/,\n            endsParent:true,\n            contains: [\n              {\n                begin: /\\{/,\n                end: /\\}/,\n                relevance: 0,\n                contains: [\"self\"]\n              }\n            ],\n          }\n        ]\n      }\n    };\n  };\n  const VERBATIM = [\n    ...['verb', 'lstinline'].map(csname => CSNAME(csname, {contains: [VERBATIM_DELIMITED_EQUAL()]})),\n    CSNAME('mint', ARGUMENT_AND_THEN(ARGUMENT_M, {contains: [VERBATIM_DELIMITED_EQUAL()]})),\n    CSNAME('mintinline', ARGUMENT_AND_THEN(ARGUMENT_M, {contains: [VERBATIM_DELIMITED_BRACES(), VERBATIM_DELIMITED_EQUAL()]})),\n    CSNAME('url', {contains: [VERBATIM_DELIMITED_BRACES(\"link\"), VERBATIM_DELIMITED_BRACES(\"link\")]}),\n    CSNAME('hyperref', {contains: [VERBATIM_DELIMITED_BRACES(\"link\")]}),\n    CSNAME('href', ARGUMENT_AND_THEN(ARGUMENT_O, {contains: [VERBATIM_DELIMITED_BRACES(\"link\")]})),\n    ...[].concat(...['', '\\\\*'].map(suffix => [\n      BEGIN_ENV('verbatim' + suffix, VERBATIM_DELIMITED_ENV('verbatim' + suffix)),\n      BEGIN_ENV('filecontents' + suffix,  ARGUMENT_AND_THEN(ARGUMENT_M, VERBATIM_DELIMITED_ENV('filecontents' + suffix))),\n      ...['', 'B', 'L'].map(prefix =>\n        BEGIN_ENV(prefix + 'Verbatim' + suffix, ARGUMENT_AND_THEN(ARGUMENT_O, VERBATIM_DELIMITED_ENV(prefix + 'Verbatim' + suffix)))\n      )\n    ])),\n    BEGIN_ENV('minted', ARGUMENT_AND_THEN(ARGUMENT_O, ARGUMENT_AND_THEN(ARGUMENT_M, VERBATIM_DELIMITED_ENV('minted')))),\n  ];\n\n  return {\n    name: 'LaTeX',\n    aliases: ['tex'],\n    contains: [\n      ...VERBATIM,\n      ...EVERYTHING_BUT_VERBATIM\n    ]\n  };\n}\n\nmodule.exports = latex;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AASA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC5D,aAAO;AAAA,IACT;AAUA,aAAS,MAAM,MAAM;AACnB,YAAM,sBAAsB,OAAO,GAAG;AAAA,QAClC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAE,IAAI,UAAQ,OAAO,iBAAiB,CAAC;AACzC,YAAM,WAAW,IAAI,OAAO;AAAA;AAAA;AAAA;AAAA,QAIxB;AAAA;AAAA;AAAA;AAAA,QAIA;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAE,IAAI,aAAW,UAAU,gBAAgB,EAAE,KAAK,GAAG,CAAC;AACxD,YAAM,cAAc;AAAA,QAClB,EAAC,OAAO,aAAY;AAAA;AAAA,QACpB,EAAC,OAAO,cAAa;AAAA;AAAA,MACvB;AACA,YAAM,wBAAwB;AAAA,QAC5B,EAAC,OAAO,mBAAkB;AAAA,QAC1B,EAAC,OAAO,mBAAkB;AAAA,QAC1B,EAAC,OAAO,mBAAkB;AAAA,QAC1B,EAAC,OAAO,mBAAkB;AAAA,QAC1B,EAAC,OAAO,mBAAkB;AAAA,QAC1B,EAAC,OAAO,uBAAsB;AAAA,MAChC;AACA,YAAM,mBAAmB;AAAA,QACvB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,UAAU;AAAA,UACZ;AAAA,UACA;AAAA,YACE,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,oBAAoB;AAAA;AAAA,QAExB,UAAU;AAAA,MACZ;AACA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,gBAAgB;AAAA,QACpB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MACb;AACA,YAAM,UAAU,KAAK;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW;AAAA,QACb;AAAA,MACF;AACA,YAAM,0BAA0B;AAAA,QAC9B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,YAAM,0BAA0B;AAAA,QAC9B,OAAO;AAAA,QAAM,KAAK;AAAA,QAClB,WAAW;AAAA,QACX,UAAU,CAAC,QAAQ,GAAG,uBAAuB;AAAA,MAC/C;AACA,YAAM,kBAAkB,KAAK;AAAA,QAC3B;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,UAAU,CAAC,yBAAyB,GAAG,uBAAuB;AAAA,QAChE;AAAA,MACF;AACA,YAAM,oBAAoB;AAAA,QACxB,OAAO;AAAA,QACL,KAAK;AAAA,QACP,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU,CAAC,yBAAyB,GAAG,uBAAuB;AAAA,MAChE;AACA,YAAM,gBAAgB;AAAA,QACpB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,aAAa,CAAC,eAAe;AACnC,YAAM,aAAa,CAAC,iBAAiB;AACrC,YAAM,oBAAoB,SAAS,KAAK,aAAa;AACnD,eAAO;AAAA,UACL,UAAU,CAAC,aAAa;AAAA,UACxB,QAAQ;AAAA,YACN,WAAW;AAAA,YACX,UAAU;AAAA,YACV,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS,SAAS,QAAQ,aAAa;AAC3C,eAAO;AAAA,UACH,OAAO,SAAS,SAAS;AAAA,UACzB,UAAU,EAAC,UAAU,eAAe,SAAS,OAAO,OAAM;AAAA,UAC1D,WAAW;AAAA,UACX,UAAU,CAAC,aAAa;AAAA,UACxB,QAAQ;AAAA,QACV;AAAA,MACJ;AACA,YAAM,YAAY,SAAS,SAAS,aAAa;AAC/C,eAAO,KAAK;AAAA,UACV;AAAA,YACE,OAAO,wCAA0C,UAAU;AAAA,YAC3D,UAAU,EAAC,UAAU,eAAe,SAAS,UAAS;AAAA,YACtD,WAAW;AAAA,UACb;AAAA,UACA,kBAAkB,YAAY,WAAW;AAAA,QAC3C;AAAA,MACF;AACA,YAAM,2BAA2B,CAAC,YAAY,aAAa;AACzD,eAAO,KAAK,kBAAkB;AAAA,UAC5B,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AACA,YAAM,yBAAyB,SAAS,SAAS;AAC/C,eAAO;AAAA,UACL,WAAW;AAAA,UACX,KAAK,kBAAkB,UAAU;AAAA,QACnC;AAAA,MACF;AAEA,YAAM,4BAA4B,CAAC,YAAY,aAAa;AAC1D,eAAO;AAAA,UACL,WAAW;AAAA,UACX,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,YAAY;AAAA,YACZ,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,KAAK;AAAA,gBACL,YAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,oBACL,WAAW;AAAA,oBACX,UAAU,CAAC,MAAM;AAAA,kBACnB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf,GAAG,CAAC,QAAQ,WAAW,EAAE,IAAI,YAAU,OAAO,QAAQ,EAAC,UAAU,CAAC,yBAAyB,CAAC,EAAC,CAAC,CAAC;AAAA,QAC/F,OAAO,QAAQ,kBAAkB,YAAY,EAAC,UAAU,CAAC,yBAAyB,CAAC,EAAC,CAAC,CAAC;AAAA,QACtF,OAAO,cAAc,kBAAkB,YAAY,EAAC,UAAU,CAAC,0BAA0B,GAAG,yBAAyB,CAAC,EAAC,CAAC,CAAC;AAAA,QACzH,OAAO,OAAO,EAAC,UAAU,CAAC,0BAA0B,MAAM,GAAG,0BAA0B,MAAM,CAAC,EAAC,CAAC;AAAA,QAChG,OAAO,YAAY,EAAC,UAAU,CAAC,0BAA0B,MAAM,CAAC,EAAC,CAAC;AAAA,QAClE,OAAO,QAAQ,kBAAkB,YAAY,EAAC,UAAU,CAAC,0BAA0B,MAAM,CAAC,EAAC,CAAC,CAAC;AAAA,QAC7F,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,IAAI,KAAK,EAAE,IAAI,YAAU;AAAA,UACxC,UAAU,aAAa,QAAQ,uBAAuB,aAAa,MAAM,CAAC;AAAA,UAC1E,UAAU,iBAAiB,QAAS,kBAAkB,YAAY,uBAAuB,iBAAiB,MAAM,CAAC,CAAC;AAAA,UAClH,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE;AAAA,YAAI,YACpB,UAAU,SAAS,aAAa,QAAQ,kBAAkB,YAAY,uBAAuB,SAAS,aAAa,MAAM,CAAC,CAAC;AAAA,UAC7H;AAAA,QACF,CAAC,CAAC;AAAA,QACF,UAAU,UAAU,kBAAkB,YAAY,kBAAkB,YAAY,uBAAuB,QAAQ,CAAC,CAAC,CAAC;AAAA,MACpH;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,KAAK;AAAA,QACf,UAAU;AAAA,UACR,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}