{"version": 3, "sources": ["../../refractor/lang/verilog.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = verilog\nverilog.displayName = 'verilog'\nverilog.aliases = []\nfunction verilog(Prism) {\n  Prism.languages.verilog = {\n    comment: {\n      pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n      greedy: true\n    },\n    string: {\n      pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    'kernel-function': {\n      // support for any kernel function (ex: $display())\n      pattern: /\\B\\$\\w+\\b/,\n      alias: 'property'\n    },\n    // support for user defined constants (ex: `define)\n    constant: /\\B`\\w+\\b/,\n    function: /\\b\\w+(?=\\()/,\n    // support for verilog and system verilog keywords\n    keyword:\n      /\\b(?:alias|and|assert|assign|assume|automatic|before|begin|bind|bins|binsof|bit|break|buf|bufif0|bufif1|byte|case|casex|casez|cell|chandle|class|clocking|cmos|config|const|constraint|context|continue|cover|covergroup|coverpoint|cross|deassign|default|defparam|design|disable|dist|do|edge|else|end|endcase|endclass|endclocking|endconfig|endfunction|endgenerate|endgroup|endinterface|endmodule|endpackage|endprimitive|endprogram|endproperty|endsequence|endspecify|endtable|endtask|enum|event|expect|export|extends|extern|final|first_match|for|force|foreach|forever|fork|forkjoin|function|generate|genvar|highz0|highz1|if|iff|ifnone|ignore_bins|illegal_bins|import|incdir|include|initial|inout|input|inside|instance|int|integer|interface|intersect|join|join_any|join_none|large|liblist|library|local|localparam|logic|longint|macromodule|matches|medium|modport|module|nand|negedge|new|nmos|nor|noshowcancelled|not|notif0|notif1|null|or|output|package|packed|parameter|pmos|posedge|primitive|priority|program|property|protected|pull0|pull1|pulldown|pullup|pulsestyle_ondetect|pulsestyle_onevent|pure|rand|randc|randcase|randsequence|rcmos|real|realtime|ref|reg|release|repeat|return|rnmos|rpmos|rtran|rtranif0|rtranif1|scalared|sequence|shortint|shortreal|showcancelled|signed|small|solve|specify|specparam|static|string|strong0|strong1|struct|super|supply0|supply1|table|tagged|task|this|throughout|time|timeprecision|timeunit|tran|tranif0|tranif1|tri|tri0|tri1|triand|trior|trireg|type|typedef|union|unique|unsigned|use|uwire|var|vectored|virtual|void|wait|wait_order|wand|weak0|weak1|while|wildcard|wire|with|within|wor|xnor|xor)\\b/,\n    // bold highlighting for all verilog and system verilog logic blocks\n    important: /\\b(?:always|always_comb|always_ff|always_latch)\\b(?: *@)?/,\n    // support for time ticks, vectors, and real numbers\n    number:\n      /\\B##?\\d+|(?:\\b\\d+)?'[odbh] ?[\\da-fzx_?]+|\\b(?:\\d*[._])?\\d+(?:e[-+]?\\d+)?/i,\n    operator: /[-+{}^~%*\\/?=!<>&|]+/,\n    punctuation: /[[\\];(),.:]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AACtB,YAAM,UAAU,UAAU;AAAA,QACxB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,mBAAmB;AAAA;AAAA,UAEjB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA;AAAA,QAEA,UAAU;AAAA,QACV,UAAU;AAAA;AAAA,QAEV,SACE;AAAA;AAAA,QAEF,WAAW;AAAA;AAAA,QAEX,QACE;AAAA,QACF,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}