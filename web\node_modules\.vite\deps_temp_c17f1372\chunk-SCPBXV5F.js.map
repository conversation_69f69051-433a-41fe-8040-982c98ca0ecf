{"version": 3, "sources": ["../../highlight.js/lib/languages/mel.js"], "sourcesContent": ["/*\nLanguage: MEL\nDescription: Maya Embedded Language\nAuthor: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: http://www.autodesk.com/products/autodesk-maya/overview\nCategory: graphics\n*/\n\nfunction mel(hljs) {\n  return {\n    name: 'MEL',\n    keywords:\n      'int float string vector matrix if else switch case default while do for in break ' +\n      'continue global proc return about abs addAttr addAttributeEditorNodeHelp addDynamic ' +\n      'addNewShelfTab addPP addPanelCategory addPrefixToName advanceToNextDrivenKey ' +\n      'affectedNet affects aimConstraint air alias aliasAttr align alignCtx alignCurve ' +\n      'alignSurface allViewFit ambientLight angle angleBetween animCone animCurveEditor ' +\n      'animDisplay animView annotate appendStringArray applicationName applyAttrPreset ' +\n      'applyTake arcLenDimContext arcLengthDimension arclen arrayMapper art3dPaintCtx ' +\n      'artAttrCtx artAttrPaintVertexCtx artAttrSkinPaintCtx artAttrTool artBuildPaintMenu ' +\n      'artFluidAttrCtx artPuttyCtx artSelectCtx artSetPaintCtx artUserPaintCtx assignCommand ' +\n      'assignInputDevice assignViewportFactories attachCurve attachDeviceAttr attachSurface ' +\n      'attrColorSliderGrp attrCompatibility attrControlGrp attrEnumOptionMenu ' +\n      'attrEnumOptionMenuGrp attrFieldGrp attrFieldSliderGrp attrNavigationControlGrp ' +\n      'attrPresetEditWin attributeExists attributeInfo attributeMenu attributeQuery ' +\n      'autoKeyframe autoPlace bakeClip bakeFluidShading bakePartialHistory bakeResults ' +\n      'bakeSimulation basename basenameEx batchRender bessel bevel bevelPlus binMembership ' +\n      'bindSkin blend2 blendShape blendShapeEditor blendShapePanel blendTwoAttr blindDataType ' +\n      'boneLattice boundary boxDollyCtx boxZoomCtx bufferCurve buildBookmarkMenu ' +\n      'buildKeyframeMenu button buttonManip CBG cacheFile cacheFileCombine cacheFileMerge ' +\n      'cacheFileTrack camera cameraView canCreateManip canvas capitalizeString catch ' +\n      'catchQuiet ceil changeSubdivComponentDisplayLevel changeSubdivRegion channelBox ' +\n      'character characterMap characterOutlineEditor characterize chdir checkBox checkBoxGrp ' +\n      'checkDefaultRenderGlobals choice circle circularFillet clamp clear clearCache clip ' +\n      'clipEditor clipEditorCurrentTimeCtx clipSchedule clipSchedulerOutliner clipTrimBefore ' +\n      'closeCurve closeSurface cluster cmdFileOutput cmdScrollFieldExecuter ' +\n      'cmdScrollFieldReporter cmdShell coarsenSubdivSelectionList collision color ' +\n      'colorAtPoint colorEditor colorIndex colorIndexSliderGrp colorSliderButtonGrp ' +\n      'colorSliderGrp columnLayout commandEcho commandLine commandPort compactHairSystem ' +\n      'componentEditor compositingInterop computePolysetVolume condition cone confirmDialog ' +\n      'connectAttr connectControl connectDynamic connectJoint connectionInfo constrain ' +\n      'constrainValue constructionHistory container containsMultibyte contextInfo control ' +\n      'convertFromOldLayers convertIffToPsd convertLightmap convertSolidTx convertTessellation ' +\n      'convertUnit copyArray copyFlexor copyKey copySkinWeights cos cpButton cpCache ' +\n      'cpClothSet cpCollision cpConstraint cpConvClothToMesh cpForces cpGetSolverAttr cpPanel ' +\n      'cpProperty cpRigidCollisionFilter cpSeam cpSetEdit cpSetSolverAttr cpSolver ' +\n      'cpSolverTypes cpTool cpUpdateClothUVs createDisplayLayer createDrawCtx createEditor ' +\n      'createLayeredPsdFile createMotionField createNewShelf createNode createRenderLayer ' +\n      'createSubdivRegion cross crossProduct ctxAbort ctxCompletion ctxEditMode ctxTraverse ' +\n      'currentCtx currentTime currentTimeCtx currentUnit curve curveAddPtCtx ' +\n      'curveCVCtx curveEPCtx curveEditorCtx curveIntersect curveMoveEPCtx curveOnSurface ' +\n      'curveSketchCtx cutKey cycleCheck cylinder dagPose date defaultLightListCheckBox ' +\n      'defaultNavigation defineDataServer defineVirtualDevice deformer deg_to_rad delete ' +\n      'deleteAttr deleteShadingGroupsAndMaterials deleteShelfTab deleteUI deleteUnusedBrushes ' +\n      'delrandstr detachCurve detachDeviceAttr detachSurface deviceEditor devicePanel dgInfo ' +\n      'dgdirty dgeval dgtimer dimWhen directKeyCtx directionalLight dirmap dirname disable ' +\n      'disconnectAttr disconnectJoint diskCache displacementToPoly displayAffected ' +\n      'displayColor displayCull displayLevelOfDetail displayPref displayRGBColor ' +\n      'displaySmoothness displayStats displayString displaySurface distanceDimContext ' +\n      'distanceDimension doBlur dolly dollyCtx dopeSheetEditor dot dotProduct ' +\n      'doubleProfileBirailSurface drag dragAttrContext draggerContext dropoffLocator ' +\n      'duplicate duplicateCurve duplicateSurface dynCache dynControl dynExport dynExpression ' +\n      'dynGlobals dynPaintEditor dynParticleCtx dynPref dynRelEdPanel dynRelEditor ' +\n      'dynamicLoad editAttrLimits editDisplayLayerGlobals editDisplayLayerMembers ' +\n      'editRenderLayerAdjustment editRenderLayerGlobals editRenderLayerMembers editor ' +\n      'editorTemplate effector emit emitter enableDevice encodeString endString endsWith env ' +\n      'equivalent equivalentTol erf error eval evalDeferred evalEcho event ' +\n      'exactWorldBoundingBox exclusiveLightCheckBox exec executeForEachObject exists exp ' +\n      'expression expressionEditorListen extendCurve extendSurface extrude fcheck fclose feof ' +\n      'fflush fgetline fgetword file fileBrowserDialog fileDialog fileExtension fileInfo ' +\n      'filetest filletCurve filter filterCurve filterExpand filterStudioImport ' +\n      'findAllIntersections findAnimCurves findKeyframe findMenuItem findRelatedSkinCluster ' +\n      'finder firstParentOf fitBspline flexor floatEq floatField floatFieldGrp floatScrollBar ' +\n      'floatSlider floatSlider2 floatSliderButtonGrp floatSliderGrp floor flow fluidCacheInfo ' +\n      'fluidEmitter fluidVoxelInfo flushUndo fmod fontDialog fopen formLayout format fprint ' +\n      'frameLayout fread freeFormFillet frewind fromNativePath fwrite gamma gauss ' +\n      'geometryConstraint getApplicationVersionAsFloat getAttr getClassification ' +\n      'getDefaultBrush getFileList getFluidAttr getInputDeviceRange getMayaPanelTypes ' +\n      'getModifiers getPanel getParticleAttr getPluginResource getenv getpid glRender ' +\n      'glRenderEditor globalStitch gmatch goal gotoBindPose grabColor gradientControl ' +\n      'gradientControlNoAttr graphDollyCtx graphSelectContext graphTrackCtx gravity grid ' +\n      'gridLayout group groupObjectsByName HfAddAttractorToAS HfAssignAS HfBuildEqualMap ' +\n      'HfBuildFurFiles HfBuildFurImages HfCancelAFR HfConnectASToHF HfCreateAttractor ' +\n      'HfDeleteAS HfEditAS HfPerformCreateAS HfRemoveAttractorFromAS HfSelectAttached ' +\n      'HfSelectAttractors HfUnAssignAS hardenPointCurve hardware hardwareRenderPanel ' +\n      'headsUpDisplay headsUpMessage help helpLine hermite hide hilite hitTest hotBox hotkey ' +\n      'hotkeyCheck hsv_to_rgb hudButton hudSlider hudSliderButton hwReflectionMap hwRender ' +\n      'hwRenderLoad hyperGraph hyperPanel hyperShade hypot iconTextButton iconTextCheckBox ' +\n      'iconTextRadioButton iconTextRadioCollection iconTextScrollList iconTextStaticLabel ' +\n      'ikHandle ikHandleCtx ikHandleDisplayScale ikSolver ikSplineHandleCtx ikSystem ' +\n      'ikSystemInfo ikfkDisplayMethod illustratorCurves image imfPlugins inheritTransform ' +\n      'insertJoint insertJointCtx insertKeyCtx insertKnotCurve insertKnotSurface instance ' +\n      'instanceable instancer intField intFieldGrp intScrollBar intSlider intSliderGrp ' +\n      'interToUI internalVar intersect iprEngine isAnimCurve isConnected isDirty isParentOf ' +\n      'isSameObject isTrue isValidObjectName isValidString isValidUiName isolateSelect ' +\n      'itemFilter itemFilterAttr itemFilterRender itemFilterType joint jointCluster jointCtx ' +\n      'jointDisplayScale jointLattice keyTangent keyframe keyframeOutliner ' +\n      'keyframeRegionCurrentTimeCtx keyframeRegionDirectKeyCtx keyframeRegionDollyCtx ' +\n      'keyframeRegionInsertKeyCtx keyframeRegionMoveKeyCtx keyframeRegionScaleKeyCtx ' +\n      'keyframeRegionSelectKeyCtx keyframeRegionSetKeyCtx keyframeRegionTrackCtx ' +\n      'keyframeStats lassoContext lattice latticeDeformKeyCtx launch launchImageEditor ' +\n      'layerButton layeredShaderPort layeredTexturePort layout layoutDialog lightList ' +\n      'lightListEditor lightListPanel lightlink lineIntersection linearPrecision linstep ' +\n      'listAnimatable listAttr listCameras listConnections listDeviceAttachments listHistory ' +\n      'listInputDeviceAxes listInputDeviceButtons listInputDevices listMenuAnnotation ' +\n      'listNodeTypes listPanelCategories listRelatives listSets listTransforms ' +\n      'listUnselected listerEditor loadFluid loadNewShelf loadPlugin ' +\n      'loadPluginLanguageResources loadPrefObjects localizedPanelLabel lockNode loft log ' +\n      'longNameOf lookThru ls lsThroughFilter lsType lsUI Mayatomr mag makeIdentity makeLive ' +\n      'makePaintable makeRoll makeSingleSurface makeTubeOn makebot manipMoveContext ' +\n      'manipMoveLimitsCtx manipOptions manipRotateContext manipRotateLimitsCtx ' +\n      'manipScaleContext manipScaleLimitsCtx marker match max memory menu menuBarLayout ' +\n      'menuEditor menuItem menuItemToShelf menuSet menuSetPref messageLine min minimizeApp ' +\n      'mirrorJoint modelCurrentTimeCtx modelEditor modelPanel mouse movIn movOut move ' +\n      'moveIKtoFK moveKeyCtx moveVertexAlongDirection multiProfileBirailSurface mute ' +\n      'nParticle nameCommand nameField namespace namespaceInfo newPanelItems newton nodeCast ' +\n      'nodeIconButton nodeOutliner nodePreset nodeType noise nonLinear normalConstraint ' +\n      'normalize nurbsBoolean nurbsCopyUVSet nurbsCube nurbsEditUV nurbsPlane nurbsSelect ' +\n      'nurbsSquare nurbsToPoly nurbsToPolygonsPref nurbsToSubdiv nurbsToSubdivPref ' +\n      'nurbsUVSet nurbsViewDirectionVector objExists objectCenter objectLayer objectType ' +\n      'objectTypeUI obsoleteProc oceanNurbsPreviewPlane offsetCurve offsetCurveOnSurface ' +\n      'offsetSurface openGLExtension openMayaPref optionMenu optionMenuGrp optionVar orbit ' +\n      'orbitCtx orientConstraint outlinerEditor outlinerPanel overrideModifier ' +\n      'paintEffectsDisplay pairBlend palettePort paneLayout panel panelConfiguration ' +\n      'panelHistory paramDimContext paramDimension paramLocator parent parentConstraint ' +\n      'particle particleExists particleInstancer particleRenderInfo partition pasteKey ' +\n      'pathAnimation pause pclose percent performanceOptions pfxstrokes pickWalk picture ' +\n      'pixelMove planarSrf plane play playbackOptions playblast plugAttr plugNode pluginInfo ' +\n      'pluginResourceUtil pointConstraint pointCurveConstraint pointLight pointMatrixMult ' +\n      'pointOnCurve pointOnSurface pointPosition poleVectorConstraint polyAppend ' +\n      'polyAppendFacetCtx polyAppendVertex polyAutoProjection polyAverageNormal ' +\n      'polyAverageVertex polyBevel polyBlendColor polyBlindData polyBoolOp polyBridgeEdge ' +\n      'polyCacheMonitor polyCheck polyChipOff polyClipboard polyCloseBorder polyCollapseEdge ' +\n      'polyCollapseFacet polyColorBlindData polyColorDel polyColorPerVertex polyColorSet ' +\n      'polyCompare polyCone polyCopyUV polyCrease polyCreaseCtx polyCreateFacet ' +\n      'polyCreateFacetCtx polyCube polyCut polyCutCtx polyCylinder polyCylindricalProjection ' +\n      'polyDelEdge polyDelFacet polyDelVertex polyDuplicateAndConnect polyDuplicateEdge ' +\n      'polyEditUV polyEditUVShell polyEvaluate polyExtrudeEdge polyExtrudeFacet ' +\n      'polyExtrudeVertex polyFlipEdge polyFlipUV polyForceUV polyGeoSampler polyHelix ' +\n      'polyInfo polyInstallAction polyLayoutUV polyListComponentConversion polyMapCut ' +\n      'polyMapDel polyMapSew polyMapSewMove polyMergeEdge polyMergeEdgeCtx polyMergeFacet ' +\n      'polyMergeFacetCtx polyMergeUV polyMergeVertex polyMirrorFace polyMoveEdge ' +\n      'polyMoveFacet polyMoveFacetUV polyMoveUV polyMoveVertex polyNormal polyNormalPerVertex ' +\n      'polyNormalizeUV polyOptUvs polyOptions polyOutput polyPipe polyPlanarProjection ' +\n      'polyPlane polyPlatonicSolid polyPoke polyPrimitive polyPrism polyProjection ' +\n      'polyPyramid polyQuad polyQueryBlindData polyReduce polySelect polySelectConstraint ' +\n      'polySelectConstraintMonitor polySelectCtx polySelectEditCtx polySeparate ' +\n      'polySetToFaceNormal polySewEdge polyShortestPathCtx polySmooth polySoftEdge ' +\n      'polySphere polySphericalProjection polySplit polySplitCtx polySplitEdge polySplitRing ' +\n      'polySplitVertex polyStraightenUVBorder polySubdivideEdge polySubdivideFacet ' +\n      'polyToSubdiv polyTorus polyTransfer polyTriangulate polyUVSet polyUnite polyWedgeFace ' +\n      'popen popupMenu pose pow preloadRefEd print progressBar progressWindow projFileViewer ' +\n      'projectCurve projectTangent projectionContext projectionManip promptDialog propModCtx ' +\n      'propMove psdChannelOutliner psdEditTextureFile psdExport psdTextureFile putenv pwd ' +\n      'python querySubdiv quit rad_to_deg radial radioButton radioButtonGrp radioCollection ' +\n      'radioMenuItemCollection rampColorPort rand randomizeFollicles randstate rangeControl ' +\n      'readTake rebuildCurve rebuildSurface recordAttr recordDevice redo reference ' +\n      'referenceEdit referenceQuery refineSubdivSelectionList refresh refreshAE ' +\n      'registerPluginResource rehash reloadImage removeJoint removeMultiInstance ' +\n      'removePanelCategory rename renameAttr renameSelectionList renameUI render ' +\n      'renderGlobalsNode renderInfo renderLayerButton renderLayerParent ' +\n      'renderLayerPostProcess renderLayerUnparent renderManip renderPartition ' +\n      'renderQualityNode renderSettings renderThumbnailUpdate renderWindowEditor ' +\n      'renderWindowSelectContext renderer reorder reorderDeformers requires reroot ' +\n      'resampleFluid resetAE resetPfxToPolyCamera resetTool resolutionNode retarget ' +\n      'reverseCurve reverseSurface revolve rgb_to_hsv rigidBody rigidSolver roll rollCtx ' +\n      'rootOf rot rotate rotationInterpolation roundConstantRadius rowColumnLayout rowLayout ' +\n      'runTimeCommand runup sampleImage saveAllShelves saveAttrPreset saveFluid saveImage ' +\n      'saveInitialState saveMenu savePrefObjects savePrefs saveShelf saveToolSettings scale ' +\n      'scaleBrushBrightness scaleComponents scaleConstraint scaleKey scaleKeyCtx sceneEditor ' +\n      'sceneUIReplacement scmh scriptCtx scriptEditorInfo scriptJob scriptNode scriptTable ' +\n      'scriptToShelf scriptedPanel scriptedPanelType scrollField scrollLayout sculpt ' +\n      'searchPathArray seed selLoadSettings select selectContext selectCurveCV selectKey ' +\n      'selectKeyCtx selectKeyframeRegionCtx selectMode selectPref selectPriority selectType ' +\n      'selectedNodes selectionConnection separator setAttr setAttrEnumResource ' +\n      'setAttrMapping setAttrNiceNameResource setConstraintRestPosition ' +\n      'setDefaultShadingGroup setDrivenKeyframe setDynamic setEditCtx setEditor setFluidAttr ' +\n      'setFocus setInfinity setInputDeviceMapping setKeyCtx setKeyPath setKeyframe ' +\n      'setKeyframeBlendshapeTargetWts setMenuMode setNodeNiceNameResource setNodeTypeFlag ' +\n      'setParent setParticleAttr setPfxToPolyCamera setPluginResource setProject ' +\n      'setStampDensity setStartupMessage setState setToolTo setUITemplate setXformManip sets ' +\n      'shadingConnection shadingGeometryRelCtx shadingLightRelCtx shadingNetworkCompare ' +\n      'shadingNode shapeCompare shelfButton shelfLayout shelfTabLayout shellField ' +\n      'shortNameOf showHelp showHidden showManipCtx showSelectionInTitle ' +\n      'showShadingGroupAttrEditor showWindow sign simplify sin singleProfileBirailSurface ' +\n      'size sizeBytes skinCluster skinPercent smoothCurve smoothTangentSurface smoothstep ' +\n      'snap2to2 snapKey snapMode snapTogetherCtx snapshot soft softMod softModCtx sort sound ' +\n      'soundControl source spaceLocator sphere sphrand spotLight spotLightPreviewPort ' +\n      'spreadSheetEditor spring sqrt squareSurface srtContext stackTrace startString ' +\n      'startsWith stitchAndExplodeShell stitchSurface stitchSurfacePoints strcmp ' +\n      'stringArrayCatenate stringArrayContains stringArrayCount stringArrayInsertAtIndex ' +\n      'stringArrayIntersector stringArrayRemove stringArrayRemoveAtIndex ' +\n      'stringArrayRemoveDuplicates stringArrayRemoveExact stringArrayToString ' +\n      'stringToStringArray strip stripPrefixFromName stroke subdAutoProjection ' +\n      'subdCleanTopology subdCollapse subdDuplicateAndConnect subdEditUV ' +\n      'subdListComponentConversion subdMapCut subdMapSewMove subdMatchTopology subdMirror ' +\n      'subdToBlind subdToPoly subdTransferUVsToCache subdiv subdivCrease ' +\n      'subdivDisplaySmoothness substitute substituteAllString substituteGeometry substring ' +\n      'surface surfaceSampler surfaceShaderList swatchDisplayPort switchTable symbolButton ' +\n      'symbolCheckBox sysFile system tabLayout tan tangentConstraint texLatticeDeformContext ' +\n      'texManipContext texMoveContext texMoveUVShellContext texRotateContext texScaleContext ' +\n      'texSelectContext texSelectShortestPathCtx texSmudgeUVContext texWinToolCtx text ' +\n      'textCurves textField textFieldButtonGrp textFieldGrp textManip textScrollList ' +\n      'textToShelf textureDisplacePlane textureHairColor texturePlacementContext ' +\n      'textureWindow threadCount threePointArcCtx timeControl timePort timerX toNativePath ' +\n      'toggle toggleAxis toggleWindowVisibility tokenize tokenizeList tolerance tolower ' +\n      'toolButton toolCollection toolDropped toolHasOptions toolPropertyWindow torus toupper ' +\n      'trace track trackCtx transferAttributes transformCompare transformLimits translator ' +\n      'trim trunc truncateFluidCache truncateHairCache tumble tumbleCtx turbulence ' +\n      'twoPointArcCtx uiRes uiTemplate unassignInputDevice undo undoInfo ungroup uniform unit ' +\n      'unloadPlugin untangleUV untitledFileName untrim upAxis updateAE userCtx uvLink ' +\n      'uvSnapshot validateShelfName vectorize view2dToolCtx viewCamera viewClipPlane ' +\n      'viewFit viewHeadOn viewLookAt viewManip viewPlace viewSet visor volumeAxis vortex ' +\n      'waitCursor warning webBrowser webBrowserPrefs whatIs window windowPref wire ' +\n      'wireContext workspace wrinkle wrinkleContext writeTake xbmLangPathList xform',\n    illegal: '</',\n    contains: [\n      hljs.C_NUMBER_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        begin: '`',\n        end: '`',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      { // eats variables\n        begin: /[$%@](\\^\\w\\b|#\\w+|[^\\s\\w{]|\\{\\w+\\}|\\w+)/\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = mel;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UACE;AAAA,QA2MF,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,UACpC;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}