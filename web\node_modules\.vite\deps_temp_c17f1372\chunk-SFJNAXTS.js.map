{"version": 3, "sources": ["../../refractor/lang/docker.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = docker\ndocker.displayName = 'docker'\ndocker.aliases = ['dockerfile']\nfunction docker(Prism) {\n  ;(function (Prism) {\n    // Many of the following regexes will contain negated lookaheads like `[ \\t]+(?![ \\t])`. This is a trick to ensure\n    // that quantifiers behave *atomically*. Atomic quantifiers are necessary to prevent exponential backtracking.\n    var spaceAfterBackSlash =\n      /\\\\[\\r\\n](?:\\s|\\\\[\\r\\n]|#.*(?!.))*(?![\\s#]|\\\\[\\r\\n])/.source // At least one space, comment, or line break\n    var space = /(?:[ \\t]+(?![ \\t])(?:<SP_BS>)?|<SP_BS>)/.source.replace(\n      /<SP_BS>/g,\n      function () {\n        return spaceAfterBackSlash\n      }\n    )\n    var string =\n      /\"(?:[^\"\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*\"|'(?:[^'\\\\\\r\\n]|\\\\(?:\\r\\n|[\\s\\S]))*'/\n        .source\n    var option = /--[\\w-]+=(?:<STR>|(?![\"'])(?:[^\\s\\\\]|\\\\.)+)/.source.replace(\n      /<STR>/g,\n      function () {\n        return string\n      }\n    )\n    var stringRule = {\n      pattern: RegExp(string),\n      greedy: true\n    }\n    var commentRule = {\n      pattern: /(^[ \\t]*)#.*/m,\n      lookbehind: true,\n      greedy: true\n    }\n    /**\n     * @param {string} source\n     * @param {string} flags\n     * @returns {RegExp}\n     */\n    function re(source, flags) {\n      source = source\n        .replace(/<OPT>/g, function () {\n          return option\n        })\n        .replace(/<SP>/g, function () {\n          return space\n        })\n      return RegExp(source, flags)\n    }\n    Prism.languages.docker = {\n      instruction: {\n        pattern:\n          /(^[ \\t]*)(?:ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|ONBUILD|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR)(?=\\s)(?:\\\\.|[^\\r\\n\\\\])*(?:\\\\$(?:\\s|#.*$)*(?![\\s#])(?:\\\\.|[^\\r\\n\\\\])*)*/im,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          options: {\n            pattern: re(\n              /(^(?:ONBUILD<SP>)?\\w+<SP>)<OPT>(?:<SP><OPT>)*/.source,\n              'i'\n            ),\n            lookbehind: true,\n            greedy: true,\n            inside: {\n              property: {\n                pattern: /(^|\\s)--[\\w-]+/,\n                lookbehind: true\n              },\n              string: [\n                stringRule,\n                {\n                  pattern: /(=)(?![\"'])(?:[^\\s\\\\]|\\\\.)+/,\n                  lookbehind: true\n                }\n              ],\n              operator: /\\\\$/m,\n              punctuation: /=/\n            }\n          },\n          keyword: [\n            {\n              // https://docs.docker.com/engine/reference/builder/#healthcheck\n              pattern: re(\n                /(^(?:ONBUILD<SP>)?HEALTHCHECK<SP>(?:<OPT><SP>)*)(?:CMD|NONE)\\b/\n                  .source,\n                'i'\n              ),\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              // https://docs.docker.com/engine/reference/builder/#from\n              pattern: re(\n                /(^(?:ONBUILD<SP>)?FROM<SP>(?:<OPT><SP>)*(?!--)[^ \\t\\\\]+<SP>)AS/\n                  .source,\n                'i'\n              ),\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              // https://docs.docker.com/engine/reference/builder/#onbuild\n              pattern: re(/(^ONBUILD<SP>)\\w+/.source, 'i'),\n              lookbehind: true,\n              greedy: true\n            },\n            {\n              pattern: /^\\w+/,\n              greedy: true\n            }\n          ],\n          comment: commentRule,\n          string: stringRule,\n          variable: /\\$(?:\\w+|\\{[^{}\"'\\\\]*\\})/,\n          operator: /\\\\$/m\n        }\n      },\n      comment: commentRule\n    }\n    Prism.languages.dockerfile = Prism.languages.docker\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC,YAAY;AAC9B,aAAS,OAAO,OAAO;AACrB;AAAC,OAAC,SAAUA,QAAO;AAGjB,YAAI,sBACF,sDAAsD;AACxD,YAAI,QAAQ,0CAA0C,OAAO;AAAA,UAC3D;AAAA,UACA,WAAY;AACV,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,SACF,0EACG;AACL,YAAI,SAAS,8CAA8C,OAAO;AAAA,UAChE;AAAA,UACA,WAAY;AACV,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,aAAa;AAAA,UACf,SAAS,OAAO,MAAM;AAAA,UACtB,QAAQ;AAAA,QACV;AACA,YAAI,cAAc;AAAA,UAChB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAMA,iBAAS,GAAG,QAAQ,OAAO;AACzB,mBAAS,OACN,QAAQ,UAAU,WAAY;AAC7B,mBAAO;AAAA,UACT,CAAC,EACA,QAAQ,SAAS,WAAY;AAC5B,mBAAO;AAAA,UACT,CAAC;AACH,iBAAO,OAAO,QAAQ,KAAK;AAAA,QAC7B;AACA,QAAAA,OAAM,UAAU,SAAS;AAAA,UACvB,aAAa;AAAA,YACX,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,SAAS;AAAA,gBACP,SAAS;AAAA,kBACP,gDAAgD;AAAA,kBAChD;AAAA,gBACF;AAAA,gBACA,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,QAAQ;AAAA,kBACN,UAAU;AAAA,oBACR,SAAS;AAAA,oBACT,YAAY;AAAA,kBACd;AAAA,kBACA,QAAQ;AAAA,oBACN;AAAA,oBACA;AAAA,sBACE,SAAS;AAAA,sBACT,YAAY;AAAA,oBACd;AAAA,kBACF;AAAA,kBACA,UAAU;AAAA,kBACV,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,SAAS;AAAA,gBACP;AAAA;AAAA,kBAEE,SAAS;AAAA,oBACP,iEACG;AAAA,oBACH;AAAA,kBACF;AAAA,kBACA,YAAY;AAAA,kBACZ,QAAQ;AAAA,gBACV;AAAA,gBACA;AAAA;AAAA,kBAEE,SAAS;AAAA,oBACP,iEACG;AAAA,oBACH;AAAA,kBACF;AAAA,kBACA,YAAY;AAAA,kBACZ,QAAQ;AAAA,gBACV;AAAA,gBACA;AAAA;AAAA,kBAEE,SAAS,GAAG,oBAAoB,QAAQ,GAAG;AAAA,kBAC3C,YAAY;AAAA,kBACZ,QAAQ;AAAA,gBACV;AAAA,gBACA;AAAA,kBACE,SAAS;AAAA,kBACT,QAAQ;AAAA,gBACV;AAAA,cACF;AAAA,cACA,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,UAAU;AAAA,cACV,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,SAAS;AAAA,QACX;AACA,QAAAA,OAAM,UAAU,aAAaA,OAAM,UAAU;AAAA,MAC/C,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}