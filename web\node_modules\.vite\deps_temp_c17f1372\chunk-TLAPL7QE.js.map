{"version": 3, "sources": ["../../refractor/lang/latte.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nvar refractorPhp = require('./php.js')\nmodule.exports = latte\nlatte.displayName = 'latte'\nlatte.aliases = []\nfunction latte(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  Prism.register(refractorPhp)\n  ;(function (Prism) {\n    Prism.languages.latte = {\n      comment: /^\\{\\*[\\s\\S]*/,\n      'latte-tag': {\n        // https://latte.nette.org/en/tags\n        pattern: /(^\\{(?:\\/(?=[a-z]))?)(?:[=_]|[a-z]\\w*\\b(?!\\())/i,\n        lookbehind: true,\n        alias: 'important'\n      },\n      delimiter: {\n        pattern: /^\\{\\/?|\\}$/,\n        alias: 'punctuation'\n      },\n      php: {\n        pattern: /\\S(?:[\\s\\S]*\\S)?/,\n        alias: 'language-php',\n        inside: Prism.languages.php\n      }\n    }\n    var markupLatte = Prism.languages.extend('markup', {})\n    Prism.languages.insertBefore(\n      'inside',\n      'attr-value',\n      {\n        'n-attr': {\n          pattern: /n:[\\w-]+(?:\\s*=\\s*(?:\"[^\"]*\"|'[^']*'|[^\\s'\">=]+))?/,\n          inside: {\n            'attr-name': {\n              pattern: /^[^\\s=]+/,\n              alias: 'important'\n            },\n            'attr-value': {\n              pattern: /=[\\s\\S]+/,\n              inside: {\n                punctuation: [\n                  /^=/,\n                  {\n                    pattern: /^(\\s*)[\"']|[\"']$/,\n                    lookbehind: true\n                  }\n                ],\n                php: {\n                  pattern: /\\S(?:[\\s\\S]*\\S)?/,\n                  inside: Prism.languages.php\n                }\n              }\n            }\n          }\n        }\n      },\n      markupLatte.tag\n    )\n    Prism.hooks.add('before-tokenize', function (env) {\n      if (env.language !== 'latte') {\n        return\n      }\n      var lattePattern =\n        /\\{\\*[\\s\\S]*?\\*\\}|\\{[^'\"\\s{}*](?:[^\"'/{}]|\\/(?![*/])|(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*\\}/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'latte',\n        lattePattern\n      )\n      env.grammar = markupLatte\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'latte')\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AACA,QAAI,4BAA4B;AAChC,QAAI,eAAe;AACnB,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB,YAAM,SAAS,yBAAyB;AACxC,YAAM,SAAS,YAAY;AAC1B,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,QAAQ;AAAA,UACtB,SAAS;AAAA,UACT,aAAa;AAAA;AAAA,YAEX,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,WAAW;AAAA,YACT,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,KAAK;AAAA,YACH,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,QACF;AACA,YAAI,cAAcA,OAAM,UAAU,OAAO,UAAU,CAAC,CAAC;AACrD,QAAAA,OAAM,UAAU;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,YACE,UAAU;AAAA,cACR,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,aAAa;AAAA,kBACX,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,cAAc;AAAA,kBACZ,SAAS;AAAA,kBACT,QAAQ;AAAA,oBACN,aAAa;AAAA,sBACX;AAAA,sBACA;AAAA,wBACE,SAAS;AAAA,wBACT,YAAY;AAAA,sBACd;AAAA,oBACF;AAAA,oBACA,KAAK;AAAA,sBACH,SAAS;AAAA,sBACT,QAAQA,OAAM,UAAU;AAAA,oBAC1B;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,YAAY;AAAA,QACd;AACA,QAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,cAAI,IAAI,aAAa,SAAS;AAC5B;AAAA,UACF;AACA,cAAI,eACF;AACF,UAAAA,OAAM,UAAU,mBAAmB,EAAE;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,cAAI,UAAU;AAAA,QAChB,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,UAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,OAAO;AAAA,QACxE,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}