{"version": 3, "sources": ["../../highlight.js/lib/languages/delphi.js"], "sourcesContent": ["/*\nLanguage: Delphi\nWebsite: https://www.embarcadero.com/products/delphi\n*/\n\n/** @type LanguageFn */\nfunction delphi(hljs) {\n  const KEYWORDS =\n    'exports register file shl array record property for mod while set ally label uses raise not ' +\n    'stored class safecall var interface or private static exit index inherited to else stdcall ' +\n    'override shr asm far resourcestring finalization packed virtual out and protected library do ' +\n    'xorwrite goto near function end div overload object unit begin string on inline repeat until ' +\n    'destructor write message program with read initialization except default nil if case cdecl in ' +\n    'downto threadvar of try pascal const external constructor type public then implementation ' +\n    'finally published procedure absolute reintroduce operator as is abstract alias assembler ' +\n    'bitpacked break continue cppdecl cvar enumerator experimental platform deprecated ' +\n    'unimplemented dynamic export far16 forward generic helper implements interrupt iochecks ' +\n    'local name nodefault noreturn nostackframe oldfpccall otherwise saveregisters softfloat ' +\n    'specialize strict unaligned varargs ';\n  const COMMENT_MODES = [\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.COMMENT(/\\{/, /\\}/, {\n      relevance: 0\n    }),\n    hljs.COMMENT(/\\(\\*/, /\\*\\)/, {\n      relevance: 10\n    })\n  ];\n  const DIRECTIVE = {\n    className: 'meta',\n    variants: [\n      {\n        begin: /\\{\\$/,\n        end: /\\}/\n      },\n      {\n        begin: /\\(\\*\\$/,\n        end: /\\*\\)/\n      }\n    ]\n  };\n  const STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/,\n    contains: [{\n      begin: /''/\n    }]\n  };\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    // Source: https://www.freepascal.org/docs-html/ref/refse6.html\n    variants: [\n      {\n        // Hexadecimal notation, e.g., $7F.\n        begin: '\\\\$[0-9A-Fa-f]+'\n      },\n      {\n        // Octal notation, e.g., &42.\n        begin: '&[0-7]+'\n      },\n      {\n        // Binary notation, e.g., %1010.\n        begin: '%[01]+'\n      }\n    ]\n  };\n  const CHAR_STRING = {\n    className: 'string',\n    begin: /(#\\d+)+/\n  };\n  const CLASS = {\n    begin: hljs.IDENT_RE + '\\\\s*=\\\\s*class\\\\s*\\\\(',\n    returnBegin: true,\n    contains: [hljs.TITLE_MODE]\n  };\n  const FUNCTION = {\n    className: 'function',\n    beginKeywords: 'function constructor destructor procedure',\n    end: /[:;]/,\n    keywords: 'function constructor|10 destructor|10 procedure|10',\n    contains: [\n      hljs.TITLE_MODE,\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        contains: [\n          STRING,\n          CHAR_STRING,\n          DIRECTIVE\n        ].concat(COMMENT_MODES)\n      },\n      DIRECTIVE\n    ].concat(COMMENT_MODES)\n  };\n  return {\n    name: 'Delphi',\n    aliases: [\n      'dpr',\n      'dfm',\n      'pas',\n      'pascal',\n      'freepascal',\n      'lazarus',\n      'lpr',\n      'lfm'\n    ],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    illegal: /\"|\\$[G-Zg-z]|\\/\\*|<\\/|\\|/,\n    contains: [\n      STRING,\n      CHAR_STRING,\n      hljs.NUMBER_MODE,\n      NUMBER,\n      CLASS,\n      FUNCTION,\n      DIRECTIVE\n    ].concat(COMMENT_MODES)\n  };\n}\n\nmodule.exports = delphi;\n"], "mappings": ";;;;;AAAA;AAAA;AAMA,aAAS,OAAO,MAAM;AACpB,YAAM,WACJ;AAWF,YAAM,gBAAgB;AAAA,QACpB,KAAK;AAAA,QACL,KAAK,QAAQ,MAAM,MAAM;AAAA,UACvB,WAAW;AAAA,QACb,CAAC;AAAA,QACD,KAAK,QAAQ,QAAQ,QAAQ;AAAA,UAC3B,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AACA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC;AAAA,UACT,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA;AAAA,QAEX,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,QAAQ;AAAA,QACZ,OAAO,KAAK,WAAW;AAAA,QACvB,aAAa;AAAA,QACb,UAAU,CAAC,KAAK,UAAU;AAAA,MAC5B;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,YACF,EAAE,OAAO,aAAa;AAAA,UACxB;AAAA,UACA;AAAA,QACF,EAAE,OAAO,aAAa;AAAA,MACxB;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,EAAE,OAAO,aAAa;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}