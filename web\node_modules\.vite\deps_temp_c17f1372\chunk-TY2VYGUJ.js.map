{"version": 3, "sources": ["../../refractor/lang/smali.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = smali\nsmali.displayName = 'smali'\nsmali.aliases = []\nfunction smali(Prism) {\n  // Test files for the parser itself:\n  // https://github.com/JesusFreke/smali/tree/master/smali/src/test/resources/LexerTest\n  Prism.languages.smali = {\n    comment: /#.*/,\n    string: {\n      pattern: /\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"|'(?:[^\\r\\n\\\\']|\\\\(?:.|u[\\da-fA-F]{4}))'/,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /(^|[^L])L(?:(?:\\w+|`[^`\\r\\n]*`)\\/)*(?:[\\w$]+|`[^`\\r\\n]*`)(?=\\s*;)/,\n      lookbehind: true,\n      inside: {\n        'class-name': {\n          pattern: /(^L|\\/)(?:[\\w$]+|`[^`\\r\\n]*`)$/,\n          lookbehind: true\n        },\n        namespace: {\n          pattern: /^(L)(?:(?:\\w+|`[^`\\r\\n]*`)\\/)+/,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\//\n          }\n        },\n        builtin: /^L/\n      }\n    },\n    builtin: [\n      {\n        // Reference: https://github.com/JesusFreke/smali/wiki/TypesMethodsAndFields#types\n        pattern: /([();\\[])[BCDFIJSVZ]+/,\n        lookbehind: true\n      },\n      {\n        // e.g. .field mWifiOnUid:I\n        pattern: /([\\w$>]:)[BCDFIJSVZ]/,\n        lookbehind: true\n      }\n    ],\n    keyword: [\n      {\n        pattern: /(\\.end\\s+)[\\w-]+/,\n        lookbehind: true\n      },\n      {\n        pattern: /(^|[^\\w.-])\\.(?!\\d)[\\w-]+/,\n        lookbehind: true\n      },\n      {\n        pattern:\n          /(^|[^\\w.-])(?:abstract|annotation|bridge|constructor|enum|final|interface|private|protected|public|runtime|static|synthetic|system|transient)(?![\\w.-])/,\n        lookbehind: true\n      }\n    ],\n    function: {\n      pattern: /(^|[^\\w.-])(?:\\w+|<[\\w$-]+>)(?=\\()/,\n      lookbehind: true\n    },\n    field: {\n      pattern: /[\\w$]+(?=:)/,\n      alias: 'variable'\n    },\n    register: {\n      pattern: /(^|[^\\w.-])[vp]\\d(?![\\w.-])/,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    boolean: {\n      pattern: /(^|[^\\w.-])(?:false|true)(?![\\w.-])/,\n      lookbehind: true\n    },\n    number: {\n      pattern:\n        /(^|[^/\\w.-])-?(?:NAN|INFINITY|0x(?:[\\dA-F]+(?:\\.[\\dA-F]*)?|\\.[\\dA-F]+)(?:p[+-]?[\\dA-F]+)?|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?)[dflst]?(?![\\w.-])/i,\n      lookbehind: true\n    },\n    label: {\n      pattern: /(:)\\w+/,\n      lookbehind: true,\n      alias: 'property'\n    },\n    operator: /->|\\.\\.|[\\[=]/,\n    punctuation: /[{}(),;:]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AAGpB,YAAM,UAAU,QAAQ;AAAA,QACtB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,WAAW;AAAA,cACT,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,SAAS;AAAA,UACX;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}