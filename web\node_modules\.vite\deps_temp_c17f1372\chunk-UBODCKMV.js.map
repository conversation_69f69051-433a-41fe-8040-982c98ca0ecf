{"version": 3, "sources": ["../../refractor/lang/abnf.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = abnf\nabnf.displayName = 'abnf'\nabnf.aliases = []\nfunction abnf(Prism) {\n  ;(function (Prism) {\n    var coreRules =\n      '(?:ALPHA|BIT|CHAR|CR|CRLF|CTL|DIGIT|DQUOTE|HEXDIG|HTAB|LF|LWSP|OCTET|SP|VCHAR|WSP)'\n    Prism.languages.abnf = {\n      comment: /;.*/,\n      string: {\n        pattern: /(?:%[is])?\"[^\"\\n\\r]*\"/,\n        greedy: true,\n        inside: {\n          punctuation: /^%[is]/\n        }\n      },\n      range: {\n        pattern: /%(?:b[01]+-[01]+|d\\d+-\\d+|x[A-F\\d]+-[A-F\\d]+)/i,\n        alias: 'number'\n      },\n      terminal: {\n        pattern:\n          /%(?:b[01]+(?:\\.[01]+)*|d\\d+(?:\\.\\d+)*|x[A-F\\d]+(?:\\.[A-F\\d]+)*)/i,\n        alias: 'number'\n      },\n      repetition: {\n        pattern: /(^|[^\\w-])(?:\\d*\\*\\d*|\\d+)/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      definition: {\n        pattern: /(^[ \\t]*)(?:[a-z][\\w-]*|<[^<>\\r\\n]*>)(?=\\s*=)/m,\n        lookbehind: true,\n        alias: 'keyword',\n        inside: {\n          punctuation: /<|>/\n        }\n      },\n      'core-rule': {\n        pattern: RegExp(\n          '(?:(^|[^<\\\\w-])' + coreRules + '|<' + coreRules + '>)(?![\\\\w-])',\n          'i'\n        ),\n        lookbehind: true,\n        alias: ['rule', 'constant'],\n        inside: {\n          punctuation: /<|>/\n        }\n      },\n      rule: {\n        pattern: /(^|[^<\\w-])[a-z][\\w-]*|<[^<>\\r\\n]*>/i,\n        lookbehind: true,\n        inside: {\n          punctuation: /<|>/\n        }\n      },\n      operator: /=\\/?|\\//,\n      punctuation: /[()\\[\\]]/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,YACF;AACF,QAAAA,OAAM,UAAU,OAAO;AAAA,UACrB,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,OAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,YACR,SACE;AAAA,YACF,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA,YACV,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA,YACV,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,aAAa;AAAA,YACX,SAAS;AAAA,cACP,oBAAoB,YAAY,OAAO,YAAY;AAAA,cACnD;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,OAAO,CAAC,QAAQ,UAAU;AAAA,YAC1B,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,MAAM;AAAA,YACJ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}