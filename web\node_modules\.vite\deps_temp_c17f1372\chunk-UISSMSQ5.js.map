{"version": 3, "sources": ["../../highlight.js/lib/languages/php.js"], "sourcesContent": ["/*\nLanguage: PHP\nAuthor: <PERSON> <<PERSON>.<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\n/**\n * @param {H<PERSON><PERSON><PERSON><PERSON>} hljs\n * @returns {LanguageDetail}\n * */\nfunction php(hljs) {\n  const VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*' +\n      // negative look-ahead tries to avoid matching patterns that are not\n      // Perl at all like $ident$, @ident@, etc.\n      `(?![A-Za-z0-9])(?![$])`\n  };\n  const PREPROCESSOR = {\n    className: 'meta',\n    variants: [\n      { begin: /<\\?php/, relevance: 10 }, // boost for obvious PHP\n      { begin: /<\\?[=]?/ },\n      { begin: /\\?>/ } // end php tag\n    ]\n  };\n  const SUBST = {\n    className: 'subst',\n    variants: [\n      { begin: /\\$\\w+/ },\n      { begin: /\\{\\$/, end: /\\}/ }\n    ]\n  };\n  const SINGLE_QUOTED = hljs.inherit(hljs.APOS_STRING_MODE, {\n    illegal: null,\n  });\n  const DOUBLE_QUOTED = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n  });\n  const HEREDOC = hljs.END_SAME_AS_BEGIN({\n    begin: /<<<[ \\t]*(\\w+)\\n/,\n    end: /[ \\t]*(\\w+)\\b/,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n  });\n  const STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE, PREPROCESSOR],\n    variants: [\n      hljs.inherit(SINGLE_QUOTED, {\n        begin: \"b'\", end: \"'\",\n      }),\n      hljs.inherit(DOUBLE_QUOTED, {\n        begin: 'b\"', end: '\"',\n      }),\n      DOUBLE_QUOTED,\n      SINGLE_QUOTED,\n      HEREDOC\n    ]\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      { begin: `\\\\b0b[01]+(?:_[01]+)*\\\\b` }, // Binary w/ underscore support\n      { begin: `\\\\b0o[0-7]+(?:_[0-7]+)*\\\\b` }, // Octals w/ underscore support\n      { begin: `\\\\b0x[\\\\da-f]+(?:_[\\\\da-f]+)*\\\\b` }, // Hex w/ underscore support\n      // Decimals w/ underscore support, with optional fragments and scientific exponent (e) suffix.\n      { begin: `(?:\\\\b\\\\d+(?:_\\\\d+)*(\\\\.(?:\\\\d+(?:_\\\\d+)*))?|\\\\B\\\\.\\\\d+)(?:e[+-]?\\\\d+)?` }\n    ],\n    relevance: 0\n  };\n  const KEYWORDS = {\n    keyword:\n    // Magic constants:\n    // <https://www.php.net/manual/en/language.constants.predefined.php>\n    '__CLASS__ __DIR__ __FILE__ __FUNCTION__ __LINE__ __METHOD__ __NAMESPACE__ __TRAIT__ ' +\n    // Function that look like language construct or language construct that look like function:\n    // List of keywords that may not require parenthesis\n    'die echo exit include include_once print require require_once ' +\n    // These are not language construct (function) but operate on the currently-executing function and can access the current symbol table\n    // 'compact extract func_get_arg func_get_args func_num_args get_called_class get_parent_class ' +\n    // Other keywords:\n    // <https://www.php.net/manual/en/reserved.php>\n    // <https://www.php.net/manual/en/language.types.type-juggling.php>\n    'array abstract and as binary bool boolean break callable case catch class clone const continue declare ' +\n    'default do double else elseif empty enddeclare endfor endforeach endif endswitch endwhile enum eval extends ' +\n    'final finally float for foreach from global goto if implements instanceof insteadof int integer interface ' +\n    'isset iterable list match|0 mixed new object or private protected public real return string switch throw trait ' +\n    'try unset use var void while xor yield',\n    literal: 'false null true',\n    built_in:\n    // Standard PHP library:\n    // <https://www.php.net/manual/en/book.spl.php>\n    'Error|0 ' + // error is too common a name esp since PHP is case in-sensitive\n    'AppendIterator ArgumentCountError ArithmeticError ArrayIterator ArrayObject AssertionError BadFunctionCallException BadMethodCallException CachingIterator CallbackFilterIterator CompileError Countable DirectoryIterator DivisionByZeroError DomainException EmptyIterator ErrorException Exception FilesystemIterator FilterIterator GlobIterator InfiniteIterator InvalidArgumentException IteratorIterator LengthException LimitIterator LogicException MultipleIterator NoRewindIterator OutOfBoundsException OutOfRangeException OuterIterator OverflowException ParentIterator ParseError RangeException RecursiveArrayIterator RecursiveCachingIterator RecursiveCallbackFilterIterator RecursiveDirectoryIterator RecursiveFilterIterator RecursiveIterator RecursiveIteratorIterator RecursiveRegexIterator RecursiveTreeIterator RegexIterator RuntimeException SeekableIterator SplDoublyLinkedList SplFileInfo SplFileObject SplFixedArray SplHeap SplMaxHeap SplMinHeap SplObjectStorage SplObserver SplObserver SplPriorityQueue SplQueue SplStack SplSubject SplSubject SplTempFileObject TypeError UnderflowException UnexpectedValueException UnhandledMatchError ' +\n    // Reserved interfaces:\n    // <https://www.php.net/manual/en/reserved.interfaces.php>\n    'ArrayAccess Closure Generator Iterator IteratorAggregate Serializable Stringable Throwable Traversable WeakReference WeakMap ' +\n    // Reserved classes:\n    // <https://www.php.net/manual/en/reserved.classes.php>\n    'Directory __PHP_Incomplete_Class parent php_user_filter self static stdClass'\n  };\n  return {\n    aliases: ['php3', 'php4', 'php5', 'php6', 'php7', 'php8'],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.COMMENT('//', '$', {contains: [PREPROCESSOR]}),\n      hljs.COMMENT(\n        '/\\\\*',\n        '\\\\*/',\n        {\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      hljs.COMMENT(\n        '__halt_compiler.+?;',\n        false,\n        {\n          endsWithParent: true,\n          keywords: '__halt_compiler'\n        }\n      ),\n      PREPROCESSOR,\n      {\n        className: 'keyword', begin: /\\$this\\b/\n      },\n      VARIABLE,\n      {\n        // swallow composed identifiers to avoid parsing them as keywords\n        begin: /(::|->)+[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/\n      },\n      {\n        className: 'function',\n        relevance: 0,\n        beginKeywords: 'fn function', end: /[;{]/, excludeEnd: true,\n        illegal: '[$%\\\\[]',\n        contains: [\n          {\n            beginKeywords: 'use',\n          },\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            begin: '=>', // No markup, just a relevance booster\n            endsParent: true\n          },\n          {\n            className: 'params',\n            begin: '\\\\(', end: '\\\\)',\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            contains: [\n              'self',\n              VARIABLE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRING,\n              NUMBER\n            ]\n          }\n        ]\n      },\n      {\n        className: 'class',\n        variants: [\n          { beginKeywords: \"enum\", illegal: /[($\"]/ },\n          { beginKeywords: \"class interface trait\", illegal: /[:($\"]/ }\n        ],\n        relevance: 0,\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          {beginKeywords: 'extends implements'},\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: ';',\n        illegal: /[.']/,\n        contains: [hljs.UNDERSCORE_TITLE_MODE]\n      },\n      {\n        beginKeywords: 'use',\n        relevance: 0,\n        end: ';',\n        contains: [hljs.UNDERSCORE_TITLE_MODE]\n      },\n      STRING,\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = php;\n"], "mappings": ";;;;;AAAA;AAAA;AAYA,aAAS,IAAI,MAAM;AACjB,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,MAIT;AACA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,UAAU,WAAW,GAAG;AAAA;AAAA,UACjC,EAAE,OAAO,UAAU;AAAA,UACnB,EAAE,OAAO,MAAM;AAAA;AAAA,QACjB;AAAA,MACF;AACA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,QAAQ;AAAA,UACjB,EAAE,OAAO,QAAQ,KAAK,KAAK;AAAA,QAC7B;AAAA,MACF;AACA,YAAM,gBAAgB,KAAK,QAAQ,KAAK,kBAAkB;AAAA,QACxD,SAAS;AAAA,MACX,CAAC;AACD,YAAM,gBAAgB,KAAK,QAAQ,KAAK,mBAAmB;AAAA,QACzD,SAAS;AAAA,QACT,UAAU,KAAK,kBAAkB,SAAS,OAAO,KAAK;AAAA,MACxD,CAAC;AACD,YAAM,UAAU,KAAK,kBAAkB;AAAA,QACrC,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,KAAK,kBAAkB,SAAS,OAAO,KAAK;AAAA,MACxD,CAAC;AACD,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU,CAAC,KAAK,kBAAkB,YAAY;AAAA,QAC9C,UAAU;AAAA,UACR,KAAK,QAAQ,eAAe;AAAA,YAC1B,OAAO;AAAA,YAAM,KAAK;AAAA,UACpB,CAAC;AAAA,UACD,KAAK,QAAQ,eAAe;AAAA,YAC1B,OAAO;AAAA,YAAM,KAAK;AAAA,UACpB,CAAC;AAAA,UACD;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,2BAA2B;AAAA;AAAA,UACpC,EAAE,OAAO,6BAA6B;AAAA;AAAA,UACtC,EAAE,OAAO,mCAAmC;AAAA;AAAA;AAAA,UAE5C,EAAE,OAAO,0EAA0E;AAAA,QACrF;AAAA,QACA,WAAW;AAAA,MACb;AACA,YAAM,WAAW;AAAA,QACf;AAAA;AAAA;AAAA,UAGA;AAAA;AAAA,QAcA,SAAS;AAAA,QACT;AAAA;AAAA;AAAA,UAGA;AAAA;AAAA,MAQF;AACA,aAAO;AAAA,QACL,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACxD,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK,QAAQ,MAAM,KAAK,EAAC,UAAU,CAAC,YAAY,EAAC,CAAC;AAAA,UAClD,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,UAAU;AAAA,gBACR;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,gBAAgB;AAAA,cAChB,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YAAW,OAAO;AAAA,UAC/B;AAAA,UACA;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,WAAW;AAAA,YACX,eAAe;AAAA,YAAe,KAAK;AAAA,YAAQ,YAAY;AAAA,YACvD,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,cACjB;AAAA,cACA,KAAK;AAAA,cACL;AAAA,gBACE,OAAO;AAAA;AAAA,gBACP,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBAAO,KAAK;AAAA,gBACnB,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,UAAU;AAAA,gBACV,UAAU;AAAA,kBACR;AAAA,kBACA;AAAA,kBACA,KAAK;AAAA,kBACL;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR,EAAE,eAAe,QAAQ,SAAS,QAAQ;AAAA,cAC1C,EAAE,eAAe,yBAAyB,SAAS,SAAS;AAAA,YAC9D;AAAA,YACA,WAAW;AAAA,YACX,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,cACR,EAAC,eAAe,qBAAoB;AAAA,cACpC,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,WAAW;AAAA,YACX,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAC,KAAK,qBAAqB;AAAA,UACvC;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,WAAW;AAAA,YACX,KAAK;AAAA,YACL,UAAU,CAAC,KAAK,qBAAqB;AAAA,UACvC;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}