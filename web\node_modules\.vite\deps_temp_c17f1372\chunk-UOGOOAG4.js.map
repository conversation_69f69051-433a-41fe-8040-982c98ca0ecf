{"version": 3, "sources": ["../../refractor/lang/django.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = django\ndjango.displayName = 'django'\ndjango.aliases = ['jinja2']\nfunction django(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  // Django/Jinja2 syntax definition for Prism.js <http://prismjs.com> syntax highlighter.\n  // Mostly it works OK but can paint code incorrectly on complex html/template tag combinations.\n  ;(function (Prism) {\n    Prism.languages.django = {\n      comment: /^\\{#[\\s\\S]*?#\\}$/,\n      tag: {\n        pattern: /(^\\{%[+-]?\\s*)\\w+/,\n        lookbehind: true,\n        alias: 'keyword'\n      },\n      delimiter: {\n        pattern: /^\\{[{%][+-]?|[+-]?[}%]\\}$/,\n        alias: 'punctuation'\n      },\n      string: {\n        pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n        greedy: true\n      },\n      filter: {\n        pattern: /(\\|)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      test: {\n        pattern: /(\\bis\\s+(?:not\\s+)?)(?!not\\b)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      function: /\\b[a-z_]\\w+(?=\\s*\\()/i,\n      keyword:\n        /\\b(?:and|as|by|else|for|if|import|in|is|loop|not|or|recursive|with|without)\\b/,\n      operator: /[-+%=]=?|!=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n      number: /\\b\\d+(?:\\.\\d+)?\\b/,\n      boolean: /[Ff]alse|[Nn]one|[Tt]rue/,\n      variable: /\\b\\w+\\b/,\n      punctuation: /[{}[\\](),.:;]/\n    }\n    var pattern = /\\{\\{[\\s\\S]*?\\}\\}|\\{%[\\s\\S]*?%\\}|\\{#[\\s\\S]*?#\\}/g\n    var markupTemplating = Prism.languages['markup-templating']\n    Prism.hooks.add('before-tokenize', function (env) {\n      markupTemplating.buildPlaceholders(env, 'django', pattern)\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      markupTemplating.tokenizePlaceholders(env, 'django')\n    }) // Add an Jinja2 alias\n    Prism.languages.jinja2 = Prism.languages.django\n    Prism.hooks.add('before-tokenize', function (env) {\n      markupTemplating.buildPlaceholders(env, 'jinja2', pattern)\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      markupTemplating.tokenizePlaceholders(env, 'jinja2')\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC,QAAQ;AAC1B,aAAS,OAAO,OAAO;AACrB,YAAM,SAAS,yBAAyB;AAGvC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,SAAS;AAAA,UACvB,SAAS;AAAA,UACT,KAAK;AAAA,YACH,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,WAAW;AAAA,YACT,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,YACJ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,UACV,SACE;AAAA,UACF,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AACA,YAAI,UAAU;AACd,YAAI,mBAAmBA,OAAM,UAAU,mBAAmB;AAC1D,QAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,2BAAiB,kBAAkB,KAAK,UAAU,OAAO;AAAA,QAC3D,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,2BAAiB,qBAAqB,KAAK,QAAQ;AAAA,QACrD,CAAC;AACD,QAAAA,OAAM,UAAU,SAASA,OAAM,UAAU;AACzC,QAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,2BAAiB,kBAAkB,KAAK,UAAU,OAAO;AAAA,QAC3D,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,2BAAiB,qBAAqB,KAAK,QAAQ;AAAA,QACrD,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}