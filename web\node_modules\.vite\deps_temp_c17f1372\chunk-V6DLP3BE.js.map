{"version": 3, "sources": ["../../refractor/lang/phpdoc.js"], "sourcesContent": ["'use strict'\nvar refractorPhp = require('./php.js')\nvar refractorJavadoclike = require('./javadoclike.js')\nmodule.exports = phpdoc\nphpdoc.displayName = 'phpdoc'\nphpdoc.aliases = []\nfunction phpdoc(Prism) {\n  Prism.register(refractorPhp)\n  Prism.register(refractorJavadoclike)\n  ;(function (Prism) {\n    var typeExpression = /(?:\\b[a-zA-Z]\\w*|[|\\\\[\\]])+/.source\n    Prism.languages.phpdoc = Prism.languages.extend('javadoclike', {\n      parameter: {\n        pattern: RegExp(\n          '(@(?:global|param|property(?:-read|-write)?|var)\\\\s+(?:' +\n            typeExpression +\n            '\\\\s+)?)\\\\$\\\\w+'\n        ),\n        lookbehind: true\n      }\n    })\n    Prism.languages.insertBefore('phpdoc', 'keyword', {\n      'class-name': [\n        {\n          pattern: RegExp(\n            '(@(?:global|package|param|property(?:-read|-write)?|return|subpackage|throws|var)\\\\s+)' +\n              typeExpression\n          ),\n          lookbehind: true,\n          inside: {\n            keyword:\n              /\\b(?:array|bool|boolean|callback|double|false|float|int|integer|mixed|null|object|resource|self|string|true|void)\\b/,\n            punctuation: /[|\\\\[\\]()]/\n          }\n        }\n      ]\n    })\n    Prism.languages.javadoclike.addSupport('php', Prism.languages.phpdoc)\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AACA,QAAI,eAAe;AACnB,QAAI,uBAAuB;AAC3B,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,SAAS,YAAY;AAC3B,YAAM,SAAS,oBAAoB;AAClC,OAAC,SAAUA,QAAO;AACjB,YAAI,iBAAiB,8BAA8B;AACnD,QAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,OAAO,eAAe;AAAA,UAC7D,WAAW;AAAA,YACT,SAAS;AAAA,cACP,4DACE,iBACA;AAAA,YACJ;AAAA,YACA,YAAY;AAAA,UACd;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,UAAU,WAAW;AAAA,UAChD,cAAc;AAAA,YACZ;AAAA,cACE,SAAS;AAAA,gBACP,2FACE;AAAA,cACJ;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,SACE;AAAA,gBACF,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,YAAY,WAAW,OAAOA,OAAM,UAAU,MAAM;AAAA,MACtE,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}