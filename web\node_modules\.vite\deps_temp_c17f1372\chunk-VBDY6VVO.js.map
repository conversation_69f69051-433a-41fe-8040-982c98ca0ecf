{"version": 3, "sources": ["../../refractor/lang/inform7.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = inform7\ninform7.displayName = 'inform7'\ninform7.aliases = []\nfunction inform7(Prism) {\n  Prism.languages.inform7 = {\n    string: {\n      pattern: /\"[^\"]*\"/,\n      inside: {\n        substitution: {\n          pattern: /\\[[^\\[\\]]+\\]/,\n          inside: {\n            delimiter: {\n              pattern: /\\[|\\]/,\n              alias: 'punctuation'\n            } // See rest below\n          }\n        }\n      }\n    },\n    comment: {\n      pattern: /\\[[^\\[\\]]+\\]/,\n      greedy: true\n    },\n    title: {\n      pattern: /^[ \\t]*(?:book|chapter|part(?! of)|section|table|volume)\\b.+/im,\n      alias: 'important'\n    },\n    number: {\n      pattern:\n        /(^|[^-])(?:\\b\\d+(?:\\.\\d+)?(?:\\^\\d+)?(?:(?!\\d)\\w+)?|\\b(?:eight|eleven|five|four|nine|one|seven|six|ten|three|twelve|two))\\b(?!-)/i,\n      lookbehind: true\n    },\n    verb: {\n      pattern:\n        /(^|[^-])\\b(?:answering|applying to|are|asking|attacking|be(?:ing)?|burning|buying|called|carries|carry(?! out)|carrying|climbing|closing|conceal(?:ing|s)?|consulting|contain(?:ing|s)?|cutting|drinking|dropping|eating|enclos(?:es?|ing)|entering|examining|exiting|getting|giving|going|ha(?:s|ve|ving)|hold(?:ing|s)?|impl(?:ies|y)|incorporat(?:es?|ing)|inserting|is|jumping|kissing|listening|locking|looking|mean(?:ing|s)?|opening|provid(?:es?|ing)|pulling|pushing|putting|relat(?:es?|ing)|removing|searching|see(?:ing|s)?|setting|showing|singing|sleeping|smelling|squeezing|support(?:ing|s)?|swearing|switching|taking|tasting|telling|thinking|throwing|touching|turning|tying|unlock(?:ing|s)?|var(?:ies|y|ying)|waiting|waking|waving|wear(?:ing|s)?)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'operator'\n    },\n    keyword: {\n      pattern:\n        /(^|[^-])\\b(?:after|before|carry out|check|continue the action|definition(?= *:)|do nothing|else|end (?:if|the story|unless)|every turn|if|include|instead(?: of)?|let|move|no|now|otherwise|repeat|report|resume the story|rule for|running through|say(?:ing)?|stop the action|test|try(?:ing)?|understand|unless|use|when|while|yes)\\b(?!-)/i,\n      lookbehind: true\n    },\n    property: {\n      pattern:\n        /(^|[^-])\\b(?:adjacent(?! to)|carried|closed|concealed|contained|dark|described|edible|empty|enclosed|enterable|even|female|fixed in place|full|handled|held|improper-named|incorporated|inedible|invisible|lighted|lit|lock(?:able|ed)|male|marked for listing|mentioned|negative|neuter|non-(?:empty|full|recurring)|odd|opaque|open(?:able)?|plural-named|portable|positive|privately-named|proper-named|provided|publically-named|pushable between rooms|recurring|related|rubbing|scenery|seen|singular-named|supported|swinging|switch(?:able|ed(?: off| on)?)|touch(?:able|ed)|transparent|unconcealed|undescribed|unlit|unlocked|unmarked for listing|unmentioned|unopenable|untouchable|unvisited|variable|visible|visited|wearable|worn)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'symbol'\n    },\n    position: {\n      pattern:\n        /(^|[^-])\\b(?:above|adjacent to|back side of|below|between|down|east|everywhere|front side|here|in|inside(?: from)?|north(?:east|west)?|nowhere|on(?: top of)?|other side|outside(?: from)?|parts? of|regionally in|south(?:east|west)?|through|up|west|within)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    type: {\n      pattern:\n        /(^|[^-])\\b(?:actions?|activit(?:ies|y)|actors?|animals?|backdrops?|containers?|devices?|directions?|doors?|holders?|kinds?|lists?|m[ae]n|nobody|nothing|nouns?|numbers?|objects?|people|persons?|player(?:'s holdall)?|regions?|relations?|rooms?|rule(?:book)?s?|scenes?|someone|something|supporters?|tables?|texts?|things?|time|vehicles?|wom[ae]n)\\b(?!-)/i,\n      lookbehind: true,\n      alias: 'variable'\n    },\n    punctuation: /[.,:;(){}]/\n  }\n  Prism.languages.inform7['string'].inside['substitution'].inside.rest =\n    Prism.languages.inform7 // We don't want the remaining text in the substitution to be highlighted as the string.\n  Prism.languages.inform7['string'].inside['substitution'].inside.rest.text = {\n    pattern: /\\S(?:\\s*\\S)*/,\n    alias: 'comment'\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AACtB,YAAM,UAAU,UAAU;AAAA,QACxB,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,WAAW;AAAA,kBACT,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,MAAM;AAAA,UACJ,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,UACP,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,UACR,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,UACR,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,MAAM;AAAA,UACJ,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,aAAa;AAAA,MACf;AACA,YAAM,UAAU,QAAQ,QAAQ,EAAE,OAAO,cAAc,EAAE,OAAO,OAC9D,MAAM,UAAU;AAClB,YAAM,UAAU,QAAQ,QAAQ,EAAE,OAAO,cAAc,EAAE,OAAO,KAAK,OAAO;AAAA,QAC1E,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;", "names": []}