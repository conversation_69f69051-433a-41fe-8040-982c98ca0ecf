{"version": 3, "sources": ["../../refractor/lang/aql.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = aql\naql.displayName = 'aql'\naql.aliases = []\nfunction aql(Prism) {\n  Prism.languages.aql = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    property: {\n      pattern:\n        /([{,]\\s*)(?:(?!\\d)\\w+|([\"'´`])(?:(?!\\2)[^\\\\\\r\\n]|\\\\.)*\\2)(?=\\s*:)/,\n      lookbehind: true,\n      greedy: true\n    },\n    string: {\n      pattern: /([\"'])(?:(?!\\1)[^\\\\\\r\\n]|\\\\.)*\\1/,\n      greedy: true\n    },\n    identifier: {\n      pattern: /([´`])(?:(?!\\1)[^\\\\\\r\\n]|\\\\.)*\\1/,\n      greedy: true\n    },\n    variable: /@@?\\w+/,\n    keyword: [\n      {\n        pattern: /(\\bWITH\\s+)COUNT(?=\\s+INTO\\b)/i,\n        lookbehind: true\n      },\n      /\\b(?:AGGREGATE|ALL|AND|ANY|ASC|COLLECT|DESC|DISTINCT|FILTER|FOR|GRAPH|IN|INBOUND|INSERT|INTO|K_PATHS|K_SHORTEST_PATHS|LET|LIKE|LIMIT|NONE|NOT|NULL|OR|OUTBOUND|REMOVE|REPLACE|RETURN|SHORTEST_PATH|SORT|UPDATE|UPSERT|WINDOW|WITH)\\b/i, // pseudo keywords get a lookbehind to avoid false positives\n      {\n        pattern: /(^|[^\\w.[])(?:KEEP|PRUNE|SEARCH|TO)\\b/i,\n        lookbehind: true\n      },\n      {\n        pattern: /(^|[^\\w.[])(?:CURRENT|NEW|OLD)\\b/,\n        lookbehind: true\n      },\n      {\n        pattern: /\\bOPTIONS(?=\\s*\\{)/i\n      }\n    ],\n    function: /\\b(?!\\d)\\w+(?=\\s*\\()/,\n    boolean: /\\b(?:false|true)\\b/i,\n    range: {\n      pattern: /\\.\\./,\n      alias: 'operator'\n    },\n    number: [\n      /\\b0b[01]+/i,\n      /\\b0x[0-9a-f]+/i,\n      /(?:\\B\\.\\d+|\\b(?:0|[1-9]\\d*)(?:\\.\\d+)?)(?:e[+-]?\\d+)?/i\n    ],\n    operator: /\\*{2,}|[=!]~|[!=<>]=?|&&|\\|\\||[-+*/%]/,\n    punctuation: /::|[?.:,;()[\\]{}]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,UAAU,MAAM;AAAA,QACpB,SAAS;AAAA,QACT,UAAU;AAAA,UACR,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,YAAY;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,UACP;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SAAS;AAAA,UACX;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}