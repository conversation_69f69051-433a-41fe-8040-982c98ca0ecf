{"version": 3, "sources": ["../../refractor/lang/scss.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = scss\nscss.displayName = 'scss'\nscss.aliases = []\nfunction scss(Prism) {\n  Prism.languages.scss = Prism.languages.extend('css', {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*)/,\n      lookbehind: true\n    },\n    atrule: {\n      pattern: /@[\\w-](?:\\([^()]+\\)|[^()\\s]|\\s+(?!\\s))*?(?=\\s+[{;])/,\n      inside: {\n        rule: /@[\\w-]+/ // See rest below\n      }\n    },\n    // url, compassified\n    url: /(?:[-a-z]+-)?url(?=\\()/i,\n    // CSS selector regex is not appropriate for Sass\n    // since there can be lot more things (var, @ directive, nesting..)\n    // a selector must start at the end of a property or after a brace (end of other rules or nesting)\n    // it can contain some characters that aren't used for defining rules or end of selector, & (parent selector), or interpolated variable\n    // the end of a selector is found when there is no rules in it ( {} or {\\s}) or if there is a property (because an interpolated var\n    // can \"pass\" as a selector- e.g: proper#{$erty})\n    // this one was hard to do, so please be careful if you edit this one :)\n    selector: {\n      // Initial look-ahead is used to prevent matching of blank selectors\n      pattern:\n        /(?=\\S)[^@;{}()]?(?:[^@;{}()\\s]|\\s+(?!\\s)|#\\{\\$[-\\w]+\\})+(?=\\s*\\{(?:\\}|\\s|[^}][^:{}]*[:{][^}]))/,\n      inside: {\n        parent: {\n          pattern: /&/,\n          alias: 'important'\n        },\n        placeholder: /%[-\\w]+/,\n        variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n      }\n    },\n    property: {\n      pattern: /(?:[-\\w]|\\$[-\\w]|#\\{\\$[-\\w]+\\})+(?=\\s*:)/,\n      inside: {\n        variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n      }\n    }\n  })\n  Prism.languages.insertBefore('scss', 'atrule', {\n    keyword: [\n      /@(?:content|debug|each|else(?: if)?|extend|for|forward|function|if|import|include|mixin|return|use|warn|while)\\b/i,\n      {\n        pattern: /( )(?:from|through)(?= )/,\n        lookbehind: true\n      }\n    ]\n  })\n  Prism.languages.insertBefore('scss', 'important', {\n    // var and interpolated vars\n    variable: /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n  })\n  Prism.languages.insertBefore('scss', 'function', {\n    'module-modifier': {\n      pattern: /\\b(?:as|hide|show|with)\\b/i,\n      alias: 'keyword'\n    },\n    placeholder: {\n      pattern: /%[-\\w]+/,\n      alias: 'selector'\n    },\n    statement: {\n      pattern: /\\B!(?:default|optional)\\b/i,\n      alias: 'keyword'\n    },\n    boolean: /\\b(?:false|true)\\b/,\n    null: {\n      pattern: /\\bnull\\b/,\n      alias: 'keyword'\n    },\n    operator: {\n      pattern: /(\\s)(?:[-+*\\/%]|[=!]=|<=?|>=?|and|not|or)(?=\\s)/,\n      lookbehind: true\n    }\n  })\n  Prism.languages.scss['atrule'].inside.rest = Prism.languages.scss\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,UAAU,OAAO,MAAM,UAAU,OAAO,OAAO;AAAA,QACnD,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,MAAM;AAAA;AAAA,UACR;AAAA,QACF;AAAA;AAAA,QAEA,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQL,UAAU;AAAA;AAAA,UAER,SACE;AAAA,UACF,QAAQ;AAAA,YACN,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,aAAa;AAAA,YACb,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,QAC7C,SAAS;AAAA,UACP;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,QAAQ,aAAa;AAAA;AAAA,QAEhD,UAAU;AAAA,MACZ,CAAC;AACD,YAAM,UAAU,aAAa,QAAQ,YAAY;AAAA,QAC/C,mBAAmB;AAAA,UACjB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,aAAa;AAAA,UACX,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,QACT,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,MACF,CAAC;AACD,YAAM,UAAU,KAAK,QAAQ,EAAE,OAAO,OAAO,MAAM,UAAU;AAAA,IAC/D;AAAA;AAAA;", "names": []}