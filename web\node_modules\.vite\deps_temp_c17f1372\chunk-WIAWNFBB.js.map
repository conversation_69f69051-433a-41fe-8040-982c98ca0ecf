{"version": 3, "sources": ["../../refractor/lang/aspnet.js"], "sourcesContent": ["'use strict'\nvar refractorCsharp = require('./csharp.js')\nmodule.exports = aspnet\naspnet.displayName = 'aspnet'\naspnet.aliases = []\nfunction aspnet(Prism) {\n  Prism.register(refractorCsharp)\n  Prism.languages.aspnet = Prism.languages.extend('markup', {\n    'page-directive': {\n      pattern: /<%\\s*@.*%>/,\n      alias: 'tag',\n      inside: {\n        'page-directive': {\n          pattern:\n            /<%\\s*@\\s*(?:Assembly|Control|Implements|Import|Master(?:Type)?|OutputCache|Page|PreviousPageType|Reference|Register)?|%>/i,\n          alias: 'tag'\n        },\n        rest: Prism.languages.markup.tag.inside\n      }\n    },\n    directive: {\n      pattern: /<%.*%>/,\n      alias: 'tag',\n      inside: {\n        directive: {\n          pattern: /<%\\s*?[$=%#:]{0,2}|%>/,\n          alias: 'tag'\n        },\n        rest: Prism.languages.csharp\n      }\n    }\n  }) // Regexp copied from prism-markup, with a negative look-ahead added\n  Prism.languages.aspnet.tag.pattern =\n    /<(?!%)\\/?[^\\s>\\/]+(?:\\s+[^\\s>\\/=]+(?:=(?:(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+))?)*\\s*\\/?>/ // match directives of attribute value foo=\"<% Bar %>\"\n  Prism.languages.insertBefore(\n    'inside',\n    'punctuation',\n    {\n      directive: Prism.languages.aspnet['directive']\n    },\n    Prism.languages.aspnet.tag.inside['attr-value']\n  )\n  Prism.languages.insertBefore('aspnet', 'comment', {\n    'asp-comment': {\n      pattern: /<%--[\\s\\S]*?--%>/,\n      alias: ['asp', 'comment']\n    }\n  }) // script runat=\"server\" contains csharp, not javascript\n  Prism.languages.insertBefore(\n    'aspnet',\n    Prism.languages.javascript ? 'script' : 'tag',\n    {\n      'asp-script': {\n        pattern:\n          /(<script(?=.*runat=['\"]?server\\b)[^>]*>)[\\s\\S]*?(?=<\\/script>)/i,\n        lookbehind: true,\n        alias: ['asp', 'script'],\n        inside: Prism.languages.csharp || {}\n      }\n    }\n  )\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,kBAAkB;AACtB,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,SAAS,eAAe;AAC9B,YAAM,UAAU,SAAS,MAAM,UAAU,OAAO,UAAU;AAAA,QACxD,kBAAkB;AAAA,UAChB,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,kBAAkB;AAAA,cAChB,SACE;AAAA,cACF,OAAO;AAAA,YACT;AAAA,YACA,MAAM,MAAM,UAAU,OAAO,IAAI;AAAA,UACnC;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,WAAW;AAAA,cACT,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,MAAM,MAAM,UAAU;AAAA,UACxB;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,UAAU,OAAO,IAAI,UACzB;AACF,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW,MAAM,UAAU,OAAO,WAAW;AAAA,QAC/C;AAAA,QACA,MAAM,UAAU,OAAO,IAAI,OAAO,YAAY;AAAA,MAChD;AACA,YAAM,UAAU,aAAa,UAAU,WAAW;AAAA,QAChD,eAAe;AAAA,UACb,SAAS;AAAA,UACT,OAAO,CAAC,OAAO,SAAS;AAAA,QAC1B;AAAA,MACF,CAAC;AACD,YAAM,UAAU;AAAA,QACd;AAAA,QACA,MAAM,UAAU,aAAa,WAAW;AAAA,QACxC;AAAA,UACE,cAAc;AAAA,YACZ,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO,CAAC,OAAO,QAAQ;AAAA,YACvB,QAAQ,MAAM,UAAU,UAAU,CAAC;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}