{"version": 3, "sources": ["../../refractor/lang/autoit.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = autoit\nautoit.displayName = 'autoit'\nautoit.aliases = []\nfunction autoit(Prism) {\n  Prism.languages.autoit = {\n    comment: [\n      /;.*/,\n      {\n        // The multi-line comments delimiters can actually be commented out with \";\"\n        pattern:\n          /(^[\\t ]*)#(?:comments-start|cs)[\\s\\S]*?^[ \\t]*#(?:ce|comments-end)/m,\n        lookbehind: true\n      }\n    ],\n    url: {\n      pattern: /(^[\\t ]*#include\\s+)(?:<[^\\r\\n>]+>|\"[^\\r\\n\"]+\")/m,\n      lookbehind: true\n    },\n    string: {\n      pattern: /([\"'])(?:\\1\\1|(?!\\1)[^\\r\\n])*\\1/,\n      greedy: true,\n      inside: {\n        variable: /([%$@])\\w+\\1/\n      }\n    },\n    directive: {\n      pattern: /(^[\\t ]*)#[\\w-]+/m,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    function: /\\b\\w+(?=\\()/,\n    // Variables and macros\n    variable: /[$@]\\w+/,\n    keyword:\n      /\\b(?:Case|Const|Continue(?:Case|Loop)|Default|Dim|Do|Else(?:If)?|End(?:Func|If|Select|Switch|With)|Enum|Exit(?:Loop)?|For|Func|Global|If|In|Local|Next|Null|ReDim|Select|Static|Step|Switch|Then|To|Until|Volatile|WEnd|While|With)\\b/i,\n    number: /\\b(?:0x[\\da-f]+|\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?)\\b/i,\n    boolean: /\\b(?:False|True)\\b/i,\n    operator: /<[=>]?|[-+*\\/=&>]=?|[?^]|\\b(?:And|Not|Or)\\b/i,\n    punctuation: /[\\[\\]().,:]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS;AAAA,QACvB,SAAS;AAAA,UACP;AAAA,UACA;AAAA;AAAA,YAEE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,KAAK;AAAA,UACH,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA;AAAA,QAEV,UAAU;AAAA,QACV,SACE;AAAA,QACF,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}