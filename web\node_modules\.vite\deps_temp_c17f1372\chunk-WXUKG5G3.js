import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// node_modules/highlight.js/lib/languages/mizar.js
var require_mizar = __commonJS({
  "node_modules/highlight.js/lib/languages/mizar.js"(exports, module) {
    function mizar(hljs) {
      return {
        name: "Mizar",
        keywords: "environ vocabularies notations constructors definitions registrations theorems schemes requirements begin end definition registration cluster existence pred func defpred deffunc theorem proof let take assume then thus hence ex for st holds consider reconsider such that and in provided of as from be being by means equals implies iff redefine define now not or attr is mode suppose per cases set thesis contradiction scheme reserve struct correctness compatibility coherence symmetry assymetry reflexivity irreflexivity connectedness uniqueness commutativity idempotence involutiveness projectivity",
        contains: [
          hljs.COMMENT("::", "$")
        ]
      };
    }
    module.exports = mizar;
  }
});

export {
  require_mizar
};
//# sourceMappingURL=chunk-WXUKG5G3.js.map
