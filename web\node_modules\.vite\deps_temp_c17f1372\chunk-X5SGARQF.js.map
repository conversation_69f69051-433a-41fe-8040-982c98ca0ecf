{"version": 3, "sources": ["../../highlight.js/lib/languages/haml.js"], "sourcesContent": ["/*\nLanguage: HAML\nRequires: ruby.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://haml.info\nCategory: template\n*/\n\n// TODO support filter tags like :javascript, support inline HTML\nfunction haml(hljs) {\n  return {\n    name: 'HAML',\n    case_insensitive: true,\n    contains: [\n      {\n        className: 'meta',\n        begin: '^!!!( (5|1\\\\.1|Strict|Frameset|Basic|Mobile|RDFa|XML\\\\b.*))?$',\n        relevance: 10\n      },\n      // FIXME these comments should be allowed to span indented lines\n      hljs.COMMENT(\n        '^\\\\s*(!=#|=#|-#|/).*$',\n        false,\n        {\n          relevance: 0\n        }\n      ),\n      {\n        begin: '^\\\\s*(-|=|!=)(?!#)',\n        starts: {\n          end: '\\\\n',\n          subLanguage: 'ruby'\n        }\n      },\n      {\n        className: 'tag',\n        begin: '^\\\\s*%',\n        contains: [\n          {\n            className: 'selector-tag',\n            begin: '\\\\w+'\n          },\n          {\n            className: 'selector-id',\n            begin: '#[\\\\w-]+'\n          },\n          {\n            className: 'selector-class',\n            begin: '\\\\.[\\\\w-]+'\n          },\n          {\n            begin: /\\{\\s*/,\n            end: /\\s*\\}/,\n            contains: [\n              {\n                begin: ':\\\\w+\\\\s*=>',\n                end: ',\\\\s+',\n                returnBegin: true,\n                endsWithParent: true,\n                contains: [\n                  {\n                    className: 'attr',\n                    begin: ':\\\\w+'\n                  },\n                  hljs.APOS_STRING_MODE,\n                  hljs.QUOTE_STRING_MODE,\n                  {\n                    begin: '\\\\w+',\n                    relevance: 0\n                  }\n                ]\n              }\n            ]\n          },\n          {\n            begin: '\\\\(\\\\s*',\n            end: '\\\\s*\\\\)',\n            excludeEnd: true,\n            contains: [\n              {\n                begin: '\\\\w+\\\\s*=',\n                end: '\\\\s+',\n                returnBegin: true,\n                endsWithParent: true,\n                contains: [\n                  {\n                    className: 'attr',\n                    begin: '\\\\w+',\n                    relevance: 0\n                  },\n                  hljs.APOS_STRING_MODE,\n                  hljs.QUOTE_STRING_MODE,\n                  {\n                    begin: '\\\\w+',\n                    relevance: 0\n                  }\n                ]\n              }\n            ]\n          }\n        ]\n      },\n      {\n        begin: '^\\\\s*[=~]\\\\s*'\n      },\n      {\n        begin: /#\\{/,\n        starts: {\n          end: /\\}/,\n          subLanguage: 'ruby'\n        }\n      }\n    ]\n  };\n}\n\nmodule.exports = haml;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,KAAK,MAAM;AAClB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA;AAAA,UAEA,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,YACb;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,oBACL,aAAa;AAAA,oBACb,gBAAgB;AAAA,oBAChB,UAAU;AAAA,sBACR;AAAA,wBACE,WAAW;AAAA,wBACX,OAAO;AAAA,sBACT;AAAA,sBACA,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL;AAAA,wBACE,OAAO;AAAA,wBACP,WAAW;AAAA,sBACb;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,YAAY;AAAA,gBACZ,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,oBACL,aAAa;AAAA,oBACb,gBAAgB;AAAA,oBAChB,UAAU;AAAA,sBACR;AAAA,wBACE,WAAW;AAAA,wBACX,OAAO;AAAA,wBACP,WAAW;AAAA,sBACb;AAAA,sBACA,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL;AAAA,wBACE,OAAO;AAAA,wBACP,WAAW;AAAA,sBACb;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}