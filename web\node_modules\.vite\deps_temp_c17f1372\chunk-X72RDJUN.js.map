{"version": 3, "sources": ["../../highlight.js/lib/languages/erlang.js"], "sourcesContent": ["/*\nLanguage: Erlang\nDescription: Erlang is a general-purpose functional language, with strict evaluation, single assignment, and dynamic typing.\nAuthor: <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://www.erlang.org\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction erlang(hljs) {\n  const BASIC_ATOM_RE = '[a-z\\'][a-zA-Z0-9_\\']*';\n  const FUNCTION_NAME_RE = '(' + BASIC_ATOM_RE + ':' + BASIC_ATOM_RE + '|' + BASIC_ATOM_RE + ')';\n  const ERLANG_RESERVED = {\n    keyword:\n      'after and andalso|10 band begin bnot bor bsl bzr bxor case catch cond div end fun if ' +\n      'let not of orelse|10 query receive rem try when xor',\n    literal:\n      'false true'\n  };\n\n  const COMMENT = hljs.COMMENT('%', '$');\n  const NUMBER = {\n    className: 'number',\n    begin: '\\\\b(\\\\d+(_\\\\d+)*#[a-fA-F0-9]+(_[a-fA-F0-9]+)*|\\\\d+(_\\\\d+)*(\\\\.\\\\d+(_\\\\d+)*)?([eE][-+]?\\\\d+)?)',\n    relevance: 0\n  };\n  const NAMED_FUN = {\n    begin: 'fun\\\\s+' + BASIC_ATOM_RE + '/\\\\d+'\n  };\n  const FUNCTION_CALL = {\n    begin: FUNCTION_NAME_RE + '\\\\(',\n    end: '\\\\)',\n    returnBegin: true,\n    relevance: 0,\n    contains: [\n      {\n        begin: FUNCTION_NAME_RE,\n        relevance: 0\n      },\n      {\n        begin: '\\\\(',\n        end: '\\\\)',\n        endsWithParent: true,\n        returnEnd: true,\n        relevance: 0\n        // \"contains\" defined later\n      }\n    ]\n  };\n  const TUPLE = {\n    begin: /\\{/,\n    end: /\\}/,\n    relevance: 0\n    // \"contains\" defined later\n  };\n  const VAR1 = {\n    begin: '\\\\b_([A-Z][A-Za-z0-9_]*)?',\n    relevance: 0\n  };\n  const VAR2 = {\n    begin: '[A-Z][a-zA-Z0-9_]*',\n    relevance: 0\n  };\n  const RECORD_ACCESS = {\n    begin: '#' + hljs.UNDERSCORE_IDENT_RE,\n    relevance: 0,\n    returnBegin: true,\n    contains: [\n      {\n        begin: '#' + hljs.UNDERSCORE_IDENT_RE,\n        relevance: 0\n      },\n      {\n        begin: /\\{/,\n        end: /\\}/,\n        relevance: 0\n        // \"contains\" defined later\n      }\n    ]\n  };\n\n  const BLOCK_STATEMENTS = {\n    beginKeywords: 'fun receive if try case',\n    end: 'end',\n    keywords: ERLANG_RESERVED\n  };\n  BLOCK_STATEMENTS.contains = [\n    COMMENT,\n    NAMED_FUN,\n    hljs.inherit(hljs.APOS_STRING_MODE, {\n      className: ''\n    }),\n    BLOCK_STATEMENTS,\n    FUNCTION_CALL,\n    hljs.QUOTE_STRING_MODE,\n    NUMBER,\n    TUPLE,\n    VAR1,\n    VAR2,\n    RECORD_ACCESS\n  ];\n\n  const BASIC_MODES = [\n    COMMENT,\n    NAMED_FUN,\n    BLOCK_STATEMENTS,\n    FUNCTION_CALL,\n    hljs.QUOTE_STRING_MODE,\n    NUMBER,\n    TUPLE,\n    VAR1,\n    VAR2,\n    RECORD_ACCESS\n  ];\n  FUNCTION_CALL.contains[1].contains = BASIC_MODES;\n  TUPLE.contains = BASIC_MODES;\n  RECORD_ACCESS.contains[1].contains = BASIC_MODES;\n\n  const DIRECTIVES = [\n    \"-module\",\n    \"-record\",\n    \"-undef\",\n    \"-export\",\n    \"-ifdef\",\n    \"-ifndef\",\n    \"-author\",\n    \"-copyright\",\n    \"-doc\",\n    \"-vsn\",\n    \"-import\",\n    \"-include\",\n    \"-include_lib\",\n    \"-compile\",\n    \"-define\",\n    \"-else\",\n    \"-endif\",\n    \"-file\",\n    \"-behaviour\",\n    \"-behavior\",\n    \"-spec\"\n  ];\n\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: BASIC_MODES\n  };\n  return {\n    name: 'Erlang',\n    aliases: ['erl'],\n    keywords: ERLANG_RESERVED,\n    illegal: '(</|\\\\*=|\\\\+=|-=|/\\\\*|\\\\*/|\\\\(\\\\*|\\\\*\\\\))',\n    contains: [\n      {\n        className: 'function',\n        begin: '^' + BASIC_ATOM_RE + '\\\\s*\\\\(',\n        end: '->',\n        returnBegin: true,\n        illegal: '\\\\(|#|//|/\\\\*|\\\\\\\\|:|;',\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: BASIC_ATOM_RE\n          })\n        ],\n        starts: {\n          end: ';|\\\\.',\n          keywords: ERLANG_RESERVED,\n          contains: BASIC_MODES\n        }\n      },\n      COMMENT,\n      {\n        begin: '^-',\n        end: '\\\\.',\n        relevance: 0,\n        excludeEnd: true,\n        returnBegin: true,\n        keywords: {\n          $pattern: '-' + hljs.IDENT_RE,\n          keyword: DIRECTIVES.map(x => `${x}|1.5`).join(\" \")\n        },\n        contains: [PARAMS]\n      },\n      NUMBER,\n      hljs.QUOTE_STRING_MODE,\n      RECORD_ACCESS,\n      VAR1,\n      VAR2,\n      TUPLE,\n      {\n        begin: /\\.$/\n      } // relevance booster\n    ]\n  };\n}\n\nmodule.exports = erlang;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,MAAM;AACpB,YAAM,gBAAgB;AACtB,YAAM,mBAAmB,MAAM,gBAAgB,MAAM,gBAAgB,MAAM,gBAAgB;AAC3F,YAAM,kBAAkB;AAAA,QACtB,SACE;AAAA,QAEF,SACE;AAAA,MACJ;AAEA,YAAM,UAAU,KAAK,QAAQ,KAAK,GAAG;AACrC,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,YAAY;AAAA,QAChB,OAAO,YAAY,gBAAgB;AAAA,MACrC;AACA,YAAM,gBAAgB;AAAA,QACpB,OAAO,mBAAmB;AAAA,QAC1B,KAAK;AAAA,QACL,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,gBAAgB;AAAA,YAChB,WAAW;AAAA,YACX,WAAW;AAAA;AAAA,UAEb;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA;AAAA,MAEb;AACA,YAAM,OAAO;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,OAAO;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,gBAAgB;AAAA,QACpB,OAAO,MAAM,KAAK;AAAA,QAClB,WAAW;AAAA,QACX,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,OAAO,MAAM,KAAK;AAAA,YAClB,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA;AAAA,UAEb;AAAA,QACF;AAAA,MACF;AAEA,YAAM,mBAAmB;AAAA,QACvB,eAAe;AAAA,QACf,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,uBAAiB,WAAW;AAAA,QAC1B;AAAA,QACA;AAAA,QACA,KAAK,QAAQ,KAAK,kBAAkB;AAAA,UAClC,WAAW;AAAA,QACb,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,cAAc;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,oBAAc,SAAS,CAAC,EAAE,WAAW;AACrC,YAAM,WAAW;AACjB,oBAAc,SAAS,CAAC,EAAE,WAAW;AAErC,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,KAAK;AAAA,QACf,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO,MAAM,gBAAgB;AAAA,YAC7B,KAAK;AAAA,YACL,aAAa;AAAA,YACb,SAAS;AAAA,YACT,UAAU;AAAA,cACR;AAAA,cACA,KAAK,QAAQ,KAAK,YAAY;AAAA,gBAC5B,OAAO;AAAA,cACT,CAAC;AAAA,YACH;AAAA,YACA,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,UAAU;AAAA,cACV,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,UAAU;AAAA,cACR,UAAU,MAAM,KAAK;AAAA,cACrB,SAAS,WAAW,IAAI,OAAK,GAAG,CAAC,MAAM,EAAE,KAAK,GAAG;AAAA,YACnD;AAAA,YACA,UAAU,CAAC,MAAM;AAAA,UACnB;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}