{"version": 3, "sources": ["../../refractor/lang/q.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = q\nq.displayName = 'q'\nq.aliases = []\nfunction q(Prism) {\n  Prism.languages.q = {\n    string: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n    comment: [\n      // From http://code.kx.com/wiki/Reference/Slash:\n      // When / is following a space (or a right parenthesis, bracket, or brace), it is ignored with the rest of the line.\n      {\n        pattern: /([\\t )\\]}])\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }, // From http://code.kx.com/wiki/Reference/Slash:\n      // A line which has / as its first character and contains at least one other non-whitespace character is a whole-line comment and is ignored entirely.\n      // A / on a line by itself begins a multiline comment which is terminated by the next \\ on a line by itself.\n      // If a / is not matched by a \\, the multiline comment is unterminated and continues to end of file.\n      // The / and \\ must be the first char on the line, but may be followed by any amount of whitespace.\n      {\n        pattern:\n          /(^|\\r?\\n|\\r)\\/[\\t ]*(?:(?:\\r?\\n|\\r)(?:.*(?:\\r?\\n|\\r(?!\\n)))*?(?:\\\\(?=[\\t ]*(?:\\r?\\n|\\r))|$)|\\S.*)/,\n        lookbehind: true,\n        greedy: true\n      }, // From http://code.kx.com/wiki/Reference/Slash:\n      // A \\ on a line by itself with no preceding matching / will comment to end of file.\n      {\n        pattern: /^\\\\[\\t ]*(?:\\r?\\n|\\r)[\\s\\S]+/m,\n        greedy: true\n      },\n      {\n        pattern: /^#!.+/m,\n        greedy: true\n      }\n    ],\n    symbol: /`(?::\\S+|[\\w.]*)/,\n    datetime: {\n      pattern:\n        /0N[mdzuvt]|0W[dtz]|\\d{4}\\.\\d\\d(?:m|\\.\\d\\d(?:T(?:\\d\\d(?::\\d\\d(?::\\d\\d(?:[.:]\\d\\d\\d)?)?)?)?)?[dz]?)|\\d\\d:\\d\\d(?::\\d\\d(?:[.:]\\d\\d\\d)?)?[uvt]?/,\n      alias: 'number'\n    },\n    // The negative look-ahead prevents bad highlighting\n    // of verbs 0: and 1:\n    number:\n      /\\b(?![01]:)(?:0N[hje]?|0W[hj]?|0[wn]|0x[\\da-fA-F]+|\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?[hjfeb]?)/,\n    keyword:\n      /\\\\\\w+\\b|\\b(?:abs|acos|aj0?|all|and|any|asc|asin|asof|atan|attr|avgs?|binr?|by|ceiling|cols|cor|cos|count|cov|cross|csv|cut|delete|deltas|desc|dev|differ|distinct|div|do|dsave|ej|enlist|eval|except|exec|exit|exp|fby|fills|first|fkeys|flip|floor|from|get|getenv|group|gtime|hclose|hcount|hdel|hopen|hsym|iasc|identity|idesc|if|ij|in|insert|inter|inv|keys?|last|like|list|ljf?|load|log|lower|lsq|ltime|ltrim|mavg|maxs?|mcount|md5|mdev|med|meta|mins?|mmax|mmin|mmu|mod|msum|neg|next|not|null|or|over|parse|peach|pj|plist|prds?|prev|prior|rand|rank|ratios|raze|read0|read1|reciprocal|reval|reverse|rload|rotate|rsave|rtrim|save|scan|scov|sdev|select|set|setenv|show|signum|sin|sqrt|ssr?|string|sublist|sums?|sv|svar|system|tables|tan|til|trim|txf|type|uj|ungroup|union|update|upper|upsert|value|var|views?|vs|wavg|where|while|within|wj1?|wsum|ww|xasc|xbar|xcols?|xdesc|xexp|xgroup|xkey|xlog|xprev|xrank)\\b/,\n    adverb: {\n      pattern: /['\\/\\\\]:?|\\beach\\b/,\n      alias: 'function'\n    },\n    verb: {\n      pattern: /(?:\\B\\.\\B|\\b[01]:|<[=>]?|>=?|[:+\\-*%,!?~=|$&#@^]):?|\\b_\\b:?/,\n      alias: 'operator'\n    },\n    punctuation: /[(){}\\[\\];.]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,MAAE,cAAc;AAChB,MAAE,UAAU,CAAC;AACb,aAAS,EAAE,OAAO;AAChB,YAAM,UAAU,IAAI;AAAA,QAClB,QAAQ;AAAA,QACR,SAAS;AAAA;AAAA;AAAA,UAGP;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAKA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA;AAAA;AAAA,UAEA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,QACR,UAAU;AAAA,UACR,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA;AAAA;AAAA,QAGA,QACE;AAAA,QACF,SACE;AAAA,QACF,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}