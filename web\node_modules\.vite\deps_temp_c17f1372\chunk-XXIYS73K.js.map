{"version": 3, "sources": ["../../refractor/lang/xeora.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = xeora\nxeora.displayName = 'xeora'\nxeora.aliases = ['xeoracube']\nfunction xeora(Prism) {\n  ;(function (Prism) {\n    Prism.languages.xeora = Prism.languages.extend('markup', {\n      constant: {\n        pattern: /\\$(?:DomainContents|PageRenderDuration)\\$/,\n        inside: {\n          punctuation: {\n            pattern: /\\$/\n          }\n        }\n      },\n      variable: {\n        pattern: /\\$@?(?:#+|[-+*~=^])?[\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[$.]/\n          },\n          operator: {\n            pattern: /#+|[-+*~=^@]/\n          }\n        }\n      },\n      'function-inline': {\n        pattern:\n          /\\$F:[-\\w.]+\\?[-\\w.]+(?:,(?:(?:@[-#]*\\w+\\.[\\w+.]\\.*)*\\|)*(?:(?:[\\w+]|[-#*.~^]+[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*|(?:@[-#]*\\w+\\.[\\w+.]\\.*)+(?:(?:[\\w+]|[-#*~^][-#*.~^]*[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*)?)?)?\\$/,\n        inside: {\n          variable: {\n            pattern: /(?:[,|])@?(?:#+|[-+*~=^])?[\\w.]+/,\n            inside: {\n              punctuation: {\n                pattern: /[,.|]/\n              },\n              operator: {\n                pattern: /#+|[-+*~=^@]/\n              }\n            }\n          },\n          punctuation: {\n            pattern: /\\$\\w:|[$:?.,|]/\n          }\n        },\n        alias: 'function'\n      },\n      'function-block': {\n        pattern:\n          /\\$XF:\\{[-\\w.]+\\?[-\\w.]+(?:,(?:(?:@[-#]*\\w+\\.[\\w+.]\\.*)*\\|)*(?:(?:[\\w+]|[-#*.~^]+[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*|(?:@[-#]*\\w+\\.[\\w+.]\\.*)+(?:(?:[\\w+]|[-#*~^][-#*.~^]*[\\w+]|=\\S)(?:[^$=]|=+[^=])*=*)?)?)?\\}:XF\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[$:{}?.,|]/\n          }\n        },\n        alias: 'function'\n      },\n      'directive-inline': {\n        pattern: /\\$\\w(?:#\\d+\\+?)?(?:\\[[-\\w.]+\\])?:[-\\/\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /\\$(?:\\w:|C(?:\\[|#\\d))?|[:{[\\]]/,\n            inside: {\n              tag: {\n                pattern: /#\\d/\n              }\n            }\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-open': {\n        pattern:\n          /\\$\\w+:\\{|\\$\\w(?:#\\d+\\+?)?(?:\\[[-\\w.]+\\])?:[-\\w.]+:\\{(?:![A-Z]+)?/,\n        inside: {\n          punctuation: {\n            pattern: /\\$(?:\\w:|C(?:\\[|#\\d))?|[:{[\\]]/,\n            inside: {\n              tag: {\n                pattern: /#\\d/\n              }\n            }\n          },\n          attribute: {\n            pattern: /![A-Z]+$/,\n            inside: {\n              punctuation: {\n                pattern: /!/\n              }\n            },\n            alias: 'keyword'\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-separator': {\n        pattern: /\\}:[-\\w.]+:\\{/,\n        inside: {\n          punctuation: {\n            pattern: /[:{}]/\n          }\n        },\n        alias: 'function'\n      },\n      'directive-block-close': {\n        pattern: /\\}:[-\\w.]+\\$/,\n        inside: {\n          punctuation: {\n            pattern: /[:{}$]/\n          }\n        },\n        alias: 'function'\n      }\n    })\n    Prism.languages.insertBefore(\n      'inside',\n      'punctuation',\n      {\n        variable: Prism.languages.xeora['function-inline'].inside['variable']\n      },\n      Prism.languages.xeora['function-block']\n    )\n    Prism.languages.xeoracube = Prism.languages.xeora\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC,WAAW;AAC5B,aAAS,MAAM,OAAO;AACpB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,QAAQA,OAAM,UAAU,OAAO,UAAU;AAAA,UACvD,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,gBACX,SAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,gBACX,SAAS;AAAA,cACX;AAAA,cACA,UAAU;AAAA,gBACR,SAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,UACA,mBAAmB;AAAA,YACjB,SACE;AAAA,YACF,QAAQ;AAAA,cACN,UAAU;AAAA,gBACR,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,aAAa;AAAA,oBACX,SAAS;AAAA,kBACX;AAAA,kBACA,UAAU;AAAA,oBACR,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,cACA,aAAa;AAAA,gBACX,SAAS;AAAA,cACX;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT;AAAA,UACA,kBAAkB;AAAA,YAChB,SACE;AAAA,YACF,QAAQ;AAAA,cACN,aAAa;AAAA,gBACX,SAAS;AAAA,cACX;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT;AAAA,UACA,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,KAAK;AAAA,oBACH,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT;AAAA,UACA,wBAAwB;AAAA,YACtB,SACE;AAAA,YACF,QAAQ;AAAA,cACN,aAAa;AAAA,gBACX,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,KAAK;AAAA,oBACH,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,cACF;AAAA,cACA,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,aAAa;AAAA,oBACX,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,gBACA,OAAO;AAAA,cACT;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT;AAAA,UACA,6BAA6B;AAAA,YAC3B,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,gBACX,SAAS;AAAA,cACX;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT;AAAA,UACA,yBAAyB;AAAA,YACvB,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,gBACX,SAAS;AAAA,cACX;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,YACE,UAAUA,OAAM,UAAU,MAAM,iBAAiB,EAAE,OAAO,UAAU;AAAA,UACtE;AAAA,UACAA,OAAM,UAAU,MAAM,gBAAgB;AAAA,QACxC;AACA,QAAAA,OAAM,UAAU,YAAYA,OAAM,UAAU;AAAA,MAC9C,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}