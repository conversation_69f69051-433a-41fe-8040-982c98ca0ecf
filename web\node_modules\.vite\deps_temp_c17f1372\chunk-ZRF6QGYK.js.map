{"version": 3, "sources": ["../../refractor/lang/bsl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = bsl\nbsl.displayName = 'bsl'\nbsl.aliases = []\nfunction bsl(Prism) {\n  /* eslint-disable no-misleading-character-class */\n  // 1C:Enterprise\n  // https://github.com/Diversus23/\n  //\n  Prism.languages.bsl = {\n    comment: /\\/\\/.*/,\n    string: [\n      // Строки\n      // Strings\n      {\n        pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n        greedy: true\n      }, // Дата и время\n      // Date & time\n      {\n        pattern: /'(?:[^'\\r\\n\\\\]|\\\\.)*'/\n      }\n    ],\n    keyword: [\n      {\n        // RU\n        pattern:\n          /(^|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:пока|для|новый|прервать|попытка|исключение|вызватьисключение|иначе|конецпопытки|неопределено|функция|перем|возврат|конецфункции|если|иначеесли|процедура|конецпроцедуры|тогда|знач|экспорт|конецесли|из|каждого|истина|ложь|по|цикл|конеццикла|выполнить)(?![\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])/i,\n        lookbehind: true\n      },\n      {\n        // EN\n        pattern:\n          /\\b(?:break|do|each|else|elseif|enddo|endfunction|endif|endprocedure|endtry|except|execute|export|false|for|function|if|in|new|null|procedure|raise|return|then|to|true|try|undefined|val|var|while)\\b/i\n      }\n    ],\n    number: {\n      pattern:\n        /(^(?=\\d)|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:E[+-]?\\d+)?/i,\n      lookbehind: true\n    },\n    operator: [\n      /[<>+\\-*/]=?|[%=]/, // RU\n      {\n        pattern:\n          /(^|[^\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])(?:и|или|не)(?![\\w\\u0400-\\u0484\\u0487-\\u052f\\u1d2b\\u1d78\\u2de0-\\u2dff\\ua640-\\ua69f\\ufe2e\\ufe2f])/i,\n        lookbehind: true\n      }, // EN\n      {\n        pattern: /\\b(?:and|not|or)\\b/i\n      }\n    ],\n    punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.]/,\n    directive: [\n      // Теги препроцессора вида &Клиент, &Сервер, ...\n      // Preprocessor tags of the type &Client, &Server, ...\n      {\n        pattern: /^([ \\t]*)&.*/m,\n        lookbehind: true,\n        greedy: true,\n        alias: 'important'\n      }, // Инструкции препроцессора вида:\n      // #Если Сервер Тогда\n      // ...\n      // #КонецЕсли\n      // Preprocessor instructions of the form:\n      // #If Server Then\n      // ...\n      // #EndIf\n      {\n        pattern: /^([ \\t]*)#.*/gm,\n        lookbehind: true,\n        greedy: true,\n        alias: 'important'\n      }\n    ]\n  }\n  Prism.languages.oscript = Prism.languages['bsl']\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAKlB,YAAM,UAAU,MAAM;AAAA,QACpB,SAAS;AAAA,QACT,QAAQ;AAAA;AAAA;AAAA,UAGN;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA;AAAA;AAAA,UAEA;AAAA,YACE,SAAS;AAAA,UACX;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP;AAAA;AAAA,YAEE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA;AAAA;AAAA,YAEE,SACE;AAAA,UACJ;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,UACR;AAAA;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA;AAAA,UACA;AAAA,YACE,SAAS;AAAA,UACX;AAAA,QACF;AAAA,QACA,aAAa;AAAA,QACb,WAAW;AAAA;AAAA;AAAA,UAGT;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAQA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,UAAU,UAAU,MAAM,UAAU,KAAK;AAAA,IACjD;AAAA;AAAA;", "names": []}