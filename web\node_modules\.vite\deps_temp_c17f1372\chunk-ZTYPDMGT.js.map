{"version": 3, "sources": ["../../refractor/lang/solution-file.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = solutionFile\nsolutionFile.displayName = 'solutionFile'\nsolutionFile.aliases = []\nfunction solutionFile(Prism) {\n  ;(function (Prism) {\n    var guid = {\n      // https://en.wikipedia.org/wiki/Universally_unique_identifier#Format\n      pattern: /\\{[\\da-f]{8}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{12}\\}/i,\n      alias: 'constant',\n      inside: {\n        punctuation: /[{}]/\n      }\n    }\n    Prism.languages['solution-file'] = {\n      comment: {\n        pattern: /#.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n        greedy: true,\n        inside: {\n          guid: guid\n        }\n      },\n      object: {\n        // Foo\n        //   Bar(\"abs\") = 9\n        //   EndBar\n        //   Prop = TRUE\n        // EndFoo\n        pattern:\n          /^([ \\t]*)(?:([A-Z]\\w*)\\b(?=.*(?:\\r\\n?|\\n)(?:\\1[ \\t].*(?:\\r\\n?|\\n))*\\1End\\2(?=[ \\t]*$))|End[A-Z]\\w*(?=[ \\t]*$))/m,\n        lookbehind: true,\n        greedy: true,\n        alias: 'keyword'\n      },\n      property: {\n        pattern: /^([ \\t]*)(?!\\s)[^\\r\\n\"#=()]*[^\\s\"#=()](?=\\s*=)/m,\n        lookbehind: true,\n        inside: {\n          guid: guid\n        }\n      },\n      guid: guid,\n      number: /\\b\\d+(?:\\.\\d+)*\\b/,\n      boolean: /\\b(?:FALSE|TRUE)\\b/,\n      operator: /=/,\n      punctuation: /[(),]/\n    }\n    Prism.languages['sln'] = Prism.languages['solution-file']\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,iBAAa,cAAc;AAC3B,iBAAa,UAAU,CAAC;AACxB,aAAS,aAAa,OAAO;AAC3B;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,OAAO;AAAA;AAAA,UAET,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,eAAe,IAAI;AAAA,UACjC,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMN,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AACA,QAAAA,OAAM,UAAU,KAAK,IAAIA,OAAM,UAAU,eAAe;AAAA,MAC1D,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}