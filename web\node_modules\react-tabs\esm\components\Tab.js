const _excluded=["children","className","disabled","disabledClassName","focus","id","selected","selectedClassName","tabIndex","tabRef"];function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.includes(n))continue;t[n]=r[n]}return t}import React,{useEffect,useRef}from"react";import cx from"clsx";const DEFAULT_CLASS="react-tabs__tab";const defaultProps={className:DEFAULT_CLASS,disabledClassName:`${DEFAULT_CLASS}--disabled`,focus:false,id:null,selected:false,selectedClassName:`${DEFAULT_CLASS}--selected`};const Tab=props=>{let nodeRef=useRef();const _defaultProps$props=Object.assign({},defaultProps,props),{children,className,disabled,disabledClassName,focus,id,selected,selectedClassName,tabIndex,tabRef}=_defaultProps$props,attributes=_objectWithoutPropertiesLoose(_defaultProps$props,_excluded);useEffect(()=>{if(selected&&focus){nodeRef.current.focus()}},[selected,focus]);return React.createElement("li",Object.assign({},attributes,{className:cx(className,{[selectedClassName]:selected,[disabledClassName]:disabled}),ref:node=>{nodeRef.current=node;if(tabRef)tabRef(node)},role:"tab",id:`tab${id}`,"aria-selected":selected?"true":"false","aria-disabled":disabled?"true":"false","aria-controls":`panel${id}`,tabIndex:tabIndex||(selected?"0":null),"data-rttab":true}),children)};Tab.tabsRole="Tab";export default Tab;