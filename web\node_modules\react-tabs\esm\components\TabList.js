const _excluded=["children","className"];function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.includes(n))continue;t[n]=r[n]}return t}import React from"react";import cx from"clsx";const defaultProps={className:"react-tabs__tab-list"};const TabList=props=>{const _defaultProps$props=Object.assign({},defaultProps,props),{children,className}=_defaultProps$props,attributes=_objectWithoutPropertiesLoose(_defaultProps$props,_excluded);return React.createElement("ul",Object.assign({},attributes,{className:cx(className),role:"tablist"}),children)};TabList.tabsRole="TabList";export default TabList;