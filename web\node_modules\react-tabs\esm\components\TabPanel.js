const _excluded=["children","className","forceRender","id","selected","selectedClassName"];function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.includes(n))continue;t[n]=r[n]}return t}import React from"react";import cx from"clsx";const DEFAULT_CLASS="react-tabs__tab-panel";const defaultProps={className:DEFAULT_CLASS,forceRender:false,selectedClassName:`${DEFAULT_CLASS}--selected`};const TabPanel=props=>{const _defaultProps$props=Object.assign({},defaultProps,props),{children,className,forceRender,id,selected,selectedClassName}=_defaultProps$props,attributes=_objectWithoutPropertiesLoose(_defaultProps$props,_excluded);return React.createElement("div",Object.assign({},attributes,{className:cx(className,{[selectedClassName]:selected}),role:"tabpanel",id:`panel${id}`,"aria-labelledby":`tab${id}`}),forceRender||selected?children:null)};TabPanel.tabsRole="TabPanel";export default TabPanel;