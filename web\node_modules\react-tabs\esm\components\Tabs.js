const _excluded=["children","defaultFocus","defaultIndex","focusTabOnClick","onSelect"];function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.includes(n))continue;t[n]=r[n]}return t}import{checkPropTypes}from"prop-types";import React,{useEffect,useState}from"react";import{childrenPropType,onSelectPropType,selectedIndexPropType}from"../helpers/propTypes";import UncontrolledTabs from"./UncontrolledTabs";import{getTabsCount}from"../helpers/count";const MODE_CONTROLLED=0;const MODE_UNCONTROLLED=1;const propTypes={children:childrenPropType,onSelect:onSelectPropType,selectedIndex:selectedIndexPropType};const defaultProps={defaultFocus:false,focusTabOnClick:true,forceRenderTabPanel:false,selectedIndex:null,defaultIndex:null,environment:null,disableUpDownKeys:false,disableLeftRightKeys:false};const getModeFromProps=props=>{return props.selectedIndex===null?MODE_UNCONTROLLED:MODE_CONTROLLED};const checkForIllegalModeChange=(props,mode)=>{if(process.env.NODE_ENV!=="production"&&mode!=undefined&&mode!==getModeFromProps(props)){throw new Error(`Switching between controlled mode (by using \`selectedIndex\`) and uncontrolled mode is not supported in \`Tabs\`.
For more information about controlled and uncontrolled mode of react-tabs see https://github.com/reactjs/react-tabs#controlled-vs-uncontrolled-mode.`)}};const Tabs=props=>{checkPropTypes(propTypes,props,"prop","Tabs");const _defaultProps$props=Object.assign({},defaultProps,props),{children,defaultFocus,defaultIndex,focusTabOnClick,onSelect}=_defaultProps$props,attributes=_objectWithoutPropertiesLoose(_defaultProps$props,_excluded);const[focus,setFocus]=useState(defaultFocus);const[mode]=useState(getModeFromProps(attributes));const[selectedIndex,setSelectedIndex]=useState(mode===MODE_UNCONTROLLED?defaultIndex||0:null);useEffect(()=>{setFocus(false)},[]);if(mode===MODE_UNCONTROLLED){const tabsCount=getTabsCount(children);useEffect(()=>{if(selectedIndex!=null){const maxTabIndex=Math.max(0,tabsCount-1);setSelectedIndex(Math.min(selectedIndex,maxTabIndex))}},[tabsCount])}checkForIllegalModeChange(attributes,mode);const handleSelected=(index,last,event)=>{if(typeof onSelect==="function"){if(onSelect(index,last,event)===false)return}if(focusTabOnClick){setFocus(true)}if(mode===MODE_UNCONTROLLED){setSelectedIndex(index)}};let subProps=Object.assign({},props,attributes);subProps.focus=focus;subProps.onSelect=handleSelected;if(selectedIndex!=null){subProps.selectedIndex=selectedIndex}delete subProps.defaultFocus;delete subProps.defaultIndex;delete subProps.focusTabOnClick;return React.createElement(UncontrolledTabs,subProps,children)};Tabs.tabsRole="Tabs";export default Tabs;