"use strict";exports.__esModule=true;exports.default=void 0;var _react=_interopRequireWildcard(require("react"));var _clsx=_interopRequireDefault(require("clsx"));const _excluded=["children","className","disabled","disabledClassName","focus","id","selected","selectedClassName","tabIndex","tabRef"];function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(_getRequireWildcardCache=function(e){return e?t:r})(e)}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u]}return n.default=e,t&&t.set(e,n),n}function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.includes(n))continue;t[n]=r[n]}return t}const DEFAULT_CLASS="react-tabs__tab";const defaultProps={className:DEFAULT_CLASS,disabledClassName:`${DEFAULT_CLASS}--disabled`,focus:false,id:null,selected:false,selectedClassName:`${DEFAULT_CLASS}--selected`};const Tab=props=>{let nodeRef=(0,_react.useRef)();const _defaultProps$props=Object.assign({},defaultProps,props),{children,className,disabled,disabledClassName,focus,id,selected,selectedClassName,tabIndex,tabRef}=_defaultProps$props,attributes=_objectWithoutPropertiesLoose(_defaultProps$props,_excluded);(0,_react.useEffect)(()=>{if(selected&&focus){nodeRef.current.focus()}},[selected,focus]);return _react.default.createElement("li",Object.assign({},attributes,{className:(0,_clsx.default)(className,{[selectedClassName]:selected,[disabledClassName]:disabled}),ref:node=>{nodeRef.current=node;if(tabRef)tabRef(node)},role:"tab",id:`tab${id}`,"aria-selected":selected?"true":"false","aria-disabled":disabled?"true":"false","aria-controls":`panel${id}`,tabIndex:tabIndex||(selected?"0":null),"data-rttab":true}),children)};Tab.tabsRole="Tab";var _default=exports.default=Tab;