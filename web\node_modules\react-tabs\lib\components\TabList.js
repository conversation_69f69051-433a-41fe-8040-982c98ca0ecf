"use strict";exports.__esModule=true;exports.default=void 0;var _react=_interopRequireDefault(require("react"));var _clsx=_interopRequireDefault(require("clsx"));const _excluded=["children","className"];function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.includes(n))continue;t[n]=r[n]}return t}const defaultProps={className:"react-tabs__tab-list"};const TabList=props=>{const _defaultProps$props=Object.assign({},defaultProps,props),{children,className}=_defaultProps$props,attributes=_objectWithoutPropertiesLoose(_defaultProps$props,_excluded);return _react.default.createElement("ul",Object.assign({},attributes,{className:(0,_clsx.default)(className),role:"tablist"}),children)};TabList.tabsRole="TabList";var _default=exports.default=TabList;