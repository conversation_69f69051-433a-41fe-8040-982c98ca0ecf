"use strict";exports.__esModule=true;exports.default=void 0;var _react=_interopRequireDefault(require("react"));var _clsx=_interopRequireDefault(require("clsx"));const _excluded=["children","className","forceRender","id","selected","selectedClassName"];function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.includes(n))continue;t[n]=r[n]}return t}const DEFAULT_CLASS="react-tabs__tab-panel";const defaultProps={className:DEFAULT_CLASS,forceRender:false,selectedClassName:`${DEFAULT_CLASS}--selected`};const TabPanel=props=>{const _defaultProps$props=Object.assign({},defaultProps,props),{children,className,forceRender,id,selected,selectedClassName}=_defaultProps$props,attributes=_objectWithoutPropertiesLoose(_defaultProps$props,_excluded);return _react.default.createElement("div",Object.assign({},attributes,{className:(0,_clsx.default)(className,{[selectedClassName]:selected}),role:"tabpanel",id:`panel${id}`,"aria-labelledby":`tab${id}`}),forceRender||selected?children:null)};TabPanel.tabsRole="TabPanel";var _default=exports.default=TabPanel;