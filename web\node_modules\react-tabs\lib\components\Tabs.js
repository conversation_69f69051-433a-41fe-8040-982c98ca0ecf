"use strict";exports.__esModule=true;exports.default=void 0;var _propTypes=require("prop-types");var _react=_interopRequireWildcard(require("react"));var _propTypes2=require("../helpers/propTypes");var _UncontrolledTabs=_interopRequireDefault(require("./UncontrolledTabs"));var _count=require("../helpers/count");const _excluded=["children","defaultFocus","defaultIndex","focusTabOnClick","onSelect"];function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(_getRequireWildcardCache=function(e){return e?t:r})(e)}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u]}return n.default=e,t&&t.set(e,n),n}function _objectWithoutPropertiesLoose(r,e){if(null==r)return{};var t={};for(var n in r)if({}.hasOwnProperty.call(r,n)){if(e.includes(n))continue;t[n]=r[n]}return t}const MODE_CONTROLLED=0;const MODE_UNCONTROLLED=1;const propTypes={children:_propTypes2.childrenPropType,onSelect:_propTypes2.onSelectPropType,selectedIndex:_propTypes2.selectedIndexPropType};const defaultProps={defaultFocus:false,focusTabOnClick:true,forceRenderTabPanel:false,selectedIndex:null,defaultIndex:null,environment:null,disableUpDownKeys:false,disableLeftRightKeys:false};const getModeFromProps=props=>{return props.selectedIndex===null?MODE_UNCONTROLLED:MODE_CONTROLLED};const checkForIllegalModeChange=(props,mode)=>{if(process.env.NODE_ENV!=="production"&&mode!=undefined&&mode!==getModeFromProps(props)){throw new Error(`Switching between controlled mode (by using \`selectedIndex\`) and uncontrolled mode is not supported in \`Tabs\`.
For more information about controlled and uncontrolled mode of react-tabs see https://github.com/reactjs/react-tabs#controlled-vs-uncontrolled-mode.`)}};const Tabs=props=>{(0,_propTypes.checkPropTypes)(propTypes,props,"prop","Tabs");const _defaultProps$props=Object.assign({},defaultProps,props),{children,defaultFocus,defaultIndex,focusTabOnClick,onSelect}=_defaultProps$props,attributes=_objectWithoutPropertiesLoose(_defaultProps$props,_excluded);const[focus,setFocus]=(0,_react.useState)(defaultFocus);const[mode]=(0,_react.useState)(getModeFromProps(attributes));const[selectedIndex,setSelectedIndex]=(0,_react.useState)(mode===MODE_UNCONTROLLED?defaultIndex||0:null);(0,_react.useEffect)(()=>{setFocus(false)},[]);if(mode===MODE_UNCONTROLLED){const tabsCount=(0,_count.getTabsCount)(children);(0,_react.useEffect)(()=>{if(selectedIndex!=null){const maxTabIndex=Math.max(0,tabsCount-1);setSelectedIndex(Math.min(selectedIndex,maxTabIndex))}},[tabsCount])}checkForIllegalModeChange(attributes,mode);const handleSelected=(index,last,event)=>{if(typeof onSelect==="function"){if(onSelect(index,last,event)===false)return}if(focusTabOnClick){setFocus(true)}if(mode===MODE_UNCONTROLLED){setSelectedIndex(index)}};let subProps=Object.assign({},props,attributes);subProps.focus=focus;subProps.onSelect=handleSelected;if(selectedIndex!=null){subProps.selectedIndex=selectedIndex}delete subProps.defaultFocus;delete subProps.defaultIndex;delete subProps.focusTabOnClick;return _react.default.createElement(_UncontrolledTabs.default,subProps,children)};Tabs.tabsRole="Tabs";var _default=exports.default=Tabs;