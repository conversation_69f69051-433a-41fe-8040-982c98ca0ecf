{"name": "react-tabs", "version": "6.1.0", "description": "An accessible and easy tab component for ReactJS", "main": "lib/index.js", "module": "esm/index.js", "typings": "index.d.ts", "scripts": {"clean:commonjs": "rm -rf lib", "clean:esm": "rm -rf esm", "build:commonjs": "cross-env BABEL_OUTPUT=commonjs babel src/ --out-dir lib/ --ignore **/__tests__,**/__mocks__", "build:esm": "babel src/ --out-dir esm/ --ignore **/__tests__,**/__mocks__", "build": "pnpm run --sequential '/clean:.*|build:.*/'", "format": "eslint src --fix --report-unused-disable-directives", "lint": "eslint src --report-unused-disable-directives", "prebump": "pnpm run --sequential '/lint|test/'", "prepublishOnly": "pnpm run build", "test": "cross-env BABEL_OUTPUT=commonjs jest", "start": "webpack serve", "website:clean": "rm -rf examples/dist", "website:build": "cross-env BABEL_TARGET=examples NODE_ENV=production webpack", "website:redirect": "cp -R examples/src/example examples/dist"}, "packageManager": "pnpm@9.15.0", "repository": {"type": "git", "url": "https://github.com/reactjs/react-tabs.git"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/reactjs/react-tabs/issues"}, "files": ["esm", "lib", "style", "index.d.ts"], "homepage": "https://github.com/reactjs/react-tabs", "keywords": ["react", "tabs", "a11y", "react-component"], "peerDependencies": {"react": "^18.0.0 || ^19.0.0"}, "devDependencies": {"@babel/cli": "7.26.4", "@babel/core": "7.26.0", "@babel/eslint-parser": "7.25.9", "@babel/preset-env": "7.26.0", "@babel/preset-react": "7.26.3", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.1.0", "@testing-library/user-event": "14.5.2", "babel-jest": "29.7.0", "babel-loader": "9.2.1", "babel-plugin-transform-react-remove-prop-types": "0.4.24", "cross-env": "7.0.3", "css-loader": "7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "7.37.2", "hoist-non-react-statics": "3.3.2", "html-loader": "5.1.0", "html-webpack-plugin": "5.6.3", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "less": "4.2.1", "less-loader": "12.2.0", "mini-css-extract-plugin": "2.9.2", "prettier": "3.4.2", "prism-react-renderer": "2.4.1", "react": "19.0.0", "react-dom": "19.0.0", "react-live": "4.1.8", "react-modal": "3.16.3", "webpack": "5.97.1", "webpack-cli": "5.1.4", "webpack-dev-server": "5.2.0"}, "dependencies": {"clsx": "^2.0.0", "prop-types": "^15.5.0"}, "jest": {"roots": ["src"], "testRegex": "/__tests__/.+-test\\.js$", "testEnvironment": "jsdom", "coveragePathIgnorePatterns": ["/node_module/", "/__tests__/"]}}