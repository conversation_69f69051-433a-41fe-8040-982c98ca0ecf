import React, { useState } from 'react'
import * as XLSX from 'xlsx'

interface ExcelData {
  image_index: number
  filename: string
  analysis_result: string
}

interface SimpleExcelViewerProps {
  isDarkTheme: boolean
}

const SimpleExcelViewer: React.FC<SimpleExcelViewerProps> = ({ isDarkTheme }) => {
  const [data, setData] = useState<ExcelData[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(5)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleFileUpload = (file: File) => {
    setIsLoading(true)
    setError(null)

    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })

        // 读取第一个工作表
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]

        // 转换为JSON格式
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        // 解析数据，假设第一行是表头
        const headers = jsonData[0] as string[]
        const rows = jsonData.slice(1) as any[][]

        // 转换为ExcelData格式
        const excelData: ExcelData[] = rows.map((row, index) => ({
          image_index: row[0] || index + 1,
          filename: row[1] || `file_${index + 1}`,
          analysis_result: row[2] || ''
        })).filter(item => item.analysis_result) // 过滤空行

        setData(excelData)
        setCurrentPage(1)
        console.log('Excel data loaded:', excelData.length, 'items')
      } catch (error) {
        console.error('Error parsing Excel file:', error)
        setError('Error parsing Excel file. Please check the file format.')
      } finally {
        setIsLoading(false)
      }
    }

    reader.readAsArrayBuffer(file)
  }

  const containerStyle: React.CSSProperties = {
    padding: '20px',
    minHeight: '500px',
    backgroundColor: isDarkTheme ? '#0d1117' : '#ffffff',
    color: isDarkTheme ? '#f0f6fc' : '#24292f',
    border: '1px solid ' + (isDarkTheme ? '#30363d' : '#d1d5db'),
    borderRadius: '8px'
  }

  const headerStyle: React.CSSProperties = {
    marginBottom: '20px',
    paddingBottom: '10px',
    borderBottom: '1px solid ' + (isDarkTheme ? '#30363d' : '#e1e5e9')
  }

  if (data.length === 0) {
    return (
      <div style={containerStyle}>
        <div style={headerStyle}>
          <h2>Excel File Viewer</h2>
          <p>Upload an Excel file to view and edit the content</p>
        </div>
        
        <div style={{
          border: '2px dashed ' + (isDarkTheme ? '#30363d' : '#d1d5db'),
          borderRadius: '8px',
          padding: '40px',
          textAlign: 'center',
          backgroundColor: isDarkTheme ? '#21262d' : '#f6f8fa'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>
            {isLoading ? '⏳' : '📊'}
          </div>
          <h3>{isLoading ? 'Processing...' : 'Upload Excel File'}</h3>
          <p>{isLoading ? 'Please wait while we process your file' : 'Drag and drop your Excel file here, or click to select'}</p>

          {error && (
            <div style={{
              color: '#dc3545',
              backgroundColor: '#f8d7da',
              border: '1px solid #f5c6cb',
              borderRadius: '4px',
              padding: '10px',
              margin: '10px 0'
            }}>
              {error}
            </div>
          )}
          
          <input
            type="file"
            accept=".xlsx,.xls"
            onChange={(e) => {
              const file = e.target.files?.[0]
              if (file) {
                console.log('File selected:', file.name)
                handleFileUpload(file)
              }
            }}
            disabled={isLoading}
            style={{
              margin: '20px 0',
              padding: '10px',
              border: '1px solid ' + (isDarkTheme ? '#30363d' : '#d1d5db'),
              borderRadius: '4px',
              backgroundColor: isDarkTheme ? '#0d1117' : '#ffffff',
              color: isDarkTheme ? '#f0f6fc' : '#24292f'
            }}
          />
          
          <div style={{
            marginTop: '20px',
            padding: '15px',
            backgroundColor: isDarkTheme ? '#0d1117' : '#ffffff',
            border: '1px solid ' + (isDarkTheme ? '#30363d' : '#d1d5db'),
            borderRadius: '4px',
            fontSize: '14px'
          }}>
            <strong>Expected Format:</strong><br />
            Excel file should contain columns: image_index, filename, analysis_result
          </div>
        </div>
      </div>
    )
  }

  // Calculate pagination
  const totalPages = Math.ceil(data.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentData = data.slice(startIndex, endIndex)

  return (
    <div style={containerStyle}>
      <div style={headerStyle}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2>Excel Data ({data.length} items)</h2>
          <button
            onClick={() => setData([])}
            style={{
              padding: '8px 16px',
              backgroundColor: isDarkTheme ? '#21262d' : '#f6f8fa',
              border: '1px solid ' + (isDarkTheme ? '#30363d' : '#d1d5db'),
              borderRadius: '4px',
              color: isDarkTheme ? '#f0f6fc' : '#24292f',
              cursor: 'pointer'
            }}
          >
            Upload New File
          </button>
        </div>
      </div>

      {/* Pagination Controls */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        padding: '10px',
        backgroundColor: isDarkTheme ? '#21262d' : '#f6f8fa',
        borderRadius: '4px'
      }}>
        <div>
          <label style={{ marginRight: '10px' }}>
            Items per page:
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value))
                setCurrentPage(1)
              }}
              style={{
                marginLeft: '5px',
                padding: '4px',
                border: '1px solid ' + (isDarkTheme ? '#30363d' : '#d1d5db'),
                borderRadius: '4px',
                backgroundColor: isDarkTheme ? '#0d1117' : '#ffffff',
                color: isDarkTheme ? '#f0f6fc' : '#24292f'
              }}
            >
              <option value={1}>1</option>
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
            </select>
          </label>
        </div>

        <div>
          Showing {startIndex + 1}-{Math.min(endIndex, data.length)} of {data.length} items
        </div>

        <div>
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            style={{
              padding: '4px 8px',
              marginRight: '5px',
              border: '1px solid ' + (isDarkTheme ? '#30363d' : '#d1d5db'),
              borderRadius: '4px',
              backgroundColor: isDarkTheme ? '#21262d' : '#ffffff',
              color: isDarkTheme ? '#f0f6fc' : '#24292f',
              cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
              opacity: currentPage === 1 ? 0.5 : 1
            }}
          >
            ← Previous
          </button>

          <span style={{ margin: '0 10px' }}>
            Page {currentPage} of {totalPages}
          </span>

          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            style={{
              padding: '4px 8px',
              marginLeft: '5px',
              border: '1px solid ' + (isDarkTheme ? '#30363d' : '#d1d5db'),
              borderRadius: '4px',
              backgroundColor: isDarkTheme ? '#21262d' : '#ffffff',
              color: isDarkTheme ? '#f0f6fc' : '#24292f',
              cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',
              opacity: currentPage === totalPages ? 0.5 : 1
            }}
          >
            Next →
          </button>
        </div>
      </div>

      {/* Data Table */}
      <div style={{
        border: '1px solid ' + (isDarkTheme ? '#30363d' : '#d1d5db'),
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
          fontSize: '14px'
        }}>
          <thead>
            <tr style={{
              backgroundColor: isDarkTheme ? '#21262d' : '#f6f8fa',
              borderBottom: '1px solid ' + (isDarkTheme ? '#30363d' : '#d1d5db')
            }}>
              <th style={{ padding: '12px', textAlign: 'left', width: '80px' }}>Index</th>
              <th style={{ padding: '12px', textAlign: 'left', width: '200px' }}>Filename</th>
              <th style={{ padding: '12px', textAlign: 'left' }}>Analysis Result</th>
            </tr>
          </thead>
          <tbody>
            {currentData.map((item, index) => (
              <tr key={startIndex + index} style={{
                borderBottom: '1px solid ' + (isDarkTheme ? '#21262d' : '#f6f8fa')
              }}>
                <td style={{ padding: '12px', textAlign: 'center' }}>{item.image_index}</td>
                <td style={{ padding: '12px' }}>{item.filename}</td>
                <td style={{
                  padding: '12px',
                  maxWidth: '400px',
                  wordBreak: 'break-word',
                  whiteSpace: 'pre-wrap'
                }}>
                  {item.analysis_result.length > 200
                    ? item.analysis_result.substring(0, 200) + '...'
                    : item.analysis_result
                  }
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default SimpleExcelViewer
