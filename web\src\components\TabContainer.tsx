import React, { useState } from 'react'
import { Tab, Ta<PERSON>, Tab<PERSON>ist, TabPanel } from 'react-tabs'
import MarkdownEditor from './MarkdownEditor'
import MarkdownPreview from './MarkdownPreview'
import SimpleExcelViewer from './SimpleExcelViewer'
import styles from '../styles/TabContainer.module.css'
import 'react-tabs/style/react-tabs.css'

interface TabContainerProps {
  markdown: string
  setMarkdown: (value: string) => void
  isDarkTheme: boolean
  onClear: () => void
  onLoadExample: () => void
}

const TabContainer: React.FC<TabContainerProps> = ({
  markdown,
  setMarkdown,
  isDarkTheme,
  onClear,
  onLoadExample
}) => {
  const [activeTabIndex, setActiveTabIndex] = useState(0)

  return (
    <div className={`${styles.tabContainer} ${isDarkTheme ? styles.dark : styles.light}`}>
      <Tabs selectedIndex={activeTabIndex} onSelect={setActiveTabIndex}>
        <TabList className={styles.tabList}>
          <Tab className={styles.tab}>Markdown Editor</Tab>
          <Tab className={styles.tab}>Excel Viewer</Tab>
        </TabList>

        <TabPanel className={styles.tabPanel}>
          <div className={styles.markdownPanel}>
            <div className={styles.editorPanel}>
              <MarkdownEditor 
                value={markdown} 
                onChange={setMarkdown}
                isDarkTheme={isDarkTheme}
              />
            </div>
            <div className={styles.previewPanel}>
              <MarkdownPreview 
                content={markdown}
                isDarkTheme={isDarkTheme}
              />
            </div>
          </div>
        </TabPanel>

        <TabPanel className={styles.tabPanel}>
          <SimpleExcelViewer isDarkTheme={isDarkTheme} />
        </TabPanel>
      </Tabs>
    </div>
  )
}

export default TabContainer
