.tabContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tabList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  border-bottom: 2px solid #e1e5e9;
  background: #f8f9fa;
}

.tabContainer.dark .tabList {
  border-bottom-color: #30363d;
  background: #21262d;
}

.tab {
  padding: 12px 24px;
  cursor: pointer;
  border: none;
  background: none;
  font-size: 14px;
  font-weight: 500;
  color: #656d76;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab:hover {
  color: #0969da;
  background: #f3f4f6;
}

.tab[aria-selected="true"] {
  color: #0969da;
  border-bottom-color: #0969da;
  background: #fff;
}

.tabContainer.dark .tab {
  color: #8b949e;
}

.tabContainer.dark .tab:hover {
  color: #58a6ff;
  background: #30363d;
}

.tabContainer.dark .tab[aria-selected="true"] {
  color: #58a6ff;
  border-bottom-color: #58a6ff;
  background: #0d1117;
}

.tabPanel {
  flex: 1;
  padding: 0;
  outline: none;
  height: calc(100vh - 120px);
  overflow: auto;
}

.markdownPanel {
  display: flex;
  height: 100%;
  gap: 1px;
}

.editorPanel {
  flex: 1;
  min-width: 0;
}

.previewPanel {
  flex: 1;
  min-width: 0;
}
