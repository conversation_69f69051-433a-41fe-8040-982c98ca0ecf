export interface ExcelData {
  image_index: number
  filename: string
  analysis_result: string
}

export interface PaginationState {
  currentPage: number
  itemsPerPage: number
  totalItems: number
  totalPages: number
}

export interface ExcelViewerState {
  data: ExcelData[]
  pagination: PaginationState
  selectedRowIndex: number | null
  isLoading: boolean
  error: string | null
}

export interface EditingCell {
  row: number
  field: keyof ExcelData
}
